/* Global Styles */
:root {
    --primary-color: #4285F4;
    --primary-dark: #3367D6;
    --secondary-color: #34A853;
    --text-color: #333;
    --light-bg: #F8F9FA;
    --border-color: #E0E0E0;
    --error-color: #EA4335;
    --success-color: #34A853;
    --warning-color: #FBBC05;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-bg);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 40px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 0;
    color: var(--primary-color);
}

header h2 {
    font-size: 1.5rem;
    margin-top: 5px;
    font-weight: 400;
}

.subtitle {
    color: #666;
    margin-top: 10px;
}

/* Main Content */
.main-content {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

section {
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: var(--shadow);
}

section h3 {
    margin-bottom: 20px;
    color: var(--primary-color);
    font-size: 1.3rem;
}

/* Upload Section */
.upload-section p {
    margin-bottom: 20px;
}

.drop-zone {
    height: 200px;
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: border-color 0.3s;
    margin-bottom: 20px;
    cursor: pointer;
    overflow: hidden;
}

.drop-zone:hover {
    border-color: var(--primary-color);
}

.drop-zone-active {
    border-color: var(--primary-color);
    background-color: rgba(66, 133, 244, 0.05);
}

.drop-zone-prompt {
    text-align: center;
    color: #999;
}

.drop-icon {
    font-size: 3rem;
    display: block;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.drop-zone-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.file-list {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 20px;
}

.file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 10px;
    border-bottom: 1px solid var(--border-color);
}

.file-item:last-child {
    border-bottom: none;
}

.file-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.upload-status {
    margin-bottom: 15px;
    font-weight: 500;
}

.status-success {
    color: var(--success-color);
}

.status-error {
    color: var(--error-color);
}

/* Processing Section */
.mode-selection {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.mode-card {
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
}

.mode-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.mode-card.selected {
    border-color: var(--primary-color);
    background-color: rgba(66, 133, 244, 0.05);
}

.mode-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.mode-card h4 {
    margin-bottom: 10px;
    color: var(--primary-color);
}

.options-panel {
    background-color: #f5f5f5;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    display: none;
}

.options-panel.active {
    display: block;
}

.option-group {
    margin-bottom: 15px;
}

.option-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.option-group input[type="number"] {
    width: 100%;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

.input-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.input-group input {
    flex: 1;
}

/* Results Section */
.result-container {
    position: relative;
    min-height: 300px;
}

.loader {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(66, 133, 244, 0.2);
    border-top: 5px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.result-content {
    display: none;
}

.result-content.active {
    display: block;
}

.result-image {
    margin: 20px 0;
    text-align: center;
}

.result-image img {
    max-width: 100%;
    max-height: 600px;
    border-radius: 8px;
    box-shadow: var(--shadow);
}

.result-video {
    margin: 20px 0;
    text-align: center;
}

.result-video video {
    max-width: 100%;
    max-height: 600px;
    border-radius: 8px;
    box-shadow: var(--shadow);
}

.result-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
}

/* Buttons */
.primary-button, .secondary-button {
    padding: 10px 20px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-align: center;
    text-decoration: none;
    display: inline-block;
}

.primary-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
}

.primary-button:hover {
    background-color: var(--primary-dark);
}

.primary-button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.secondary-button {
    background-color: white;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.secondary-button:hover {
    background-color: rgba(66, 133, 244, 0.05);
}

/* Error page */
.error-container {
    text-align: center;
    padding: 40px 20px;
}

.error-container h1 {
    font-size: 3rem;
    color: var(--error-color);
    margin-bottom: 20px;
}

.error-message {
    margin-bottom: 30px;
    font-size: 1.2rem;
}

/* Footer */
footer {
    text-align: center;
    margin-top: 60px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
    color: #888;
    font-size: 0.9rem;
} 