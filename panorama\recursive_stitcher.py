import cv2
import numpy as np
import os
from glob import glob
from tqdm import tqdm
import math
from .google_street_view import GoogleStreetViewCreator

class RecursivePanoramaStitcher:
    def __init__(self):
        """初始化递归全景拼接器"""
        self.base_stitcher = GoogleStreetViewCreator()
        
    def recursive_stitch_images(self, images, debug=False):
        """
        递归拼接图像列表
        按顺序每两张图片进行拼接，然后对拼接结果继续拼接，直到只剩一张图
        
        Args:
            images: 图像列表，按拼接顺序排列
            debug: 是否输出调试信息
            
        Returns:
            numpy.ndarray: 最终拼接结果
        """
        if not images:
            return None
            
        if len(images) == 1:
            return images[0]
            
        print(f"开始递归拼接 {len(images)} 张图像...")
        
        # 第一轮：两两拼接
        current_level = images.copy()
        level = 1
        
        while len(current_level) > 1:
            print(f"第 {level} 轮拼接：处理 {len(current_level)} 张图像")
            next_level = []
            
            # 两两配对进行拼接
            for i in range(0, len(current_level), 2):
                if i + 1 < len(current_level):
                    # 有配对的图像，进行拼接
                    img1 = current_level[i]
                    img2 = current_level[i + 1]
                    
                    print(f"  拼接图像对 {i//2 + 1}: {i+1} + {i+2}")
                    
                    # 使用谷歌街景模式拼接
                    stitched = self.base_stitcher.stitch_two_images_natural(img1, img2)
                    
                    if stitched is not None:
                        next_level.append(stitched)
                        if debug:
                            cv2.imwrite(f"debug_level_{level}_pair_{i//2 + 1}.jpg", stitched)
                    else:
                        print(f"    警告：图像对 {i//2 + 1} 拼接失败，尝试反向拼接...")
                        stitched = self.base_stitcher.stitch_two_images_natural(img2, img1)
                        if stitched is not None:
                            next_level.append(stitched)
                            if debug:
                                cv2.imwrite(f"debug_level_{level}_pair_{i//2 + 1}_reversed.jpg", stitched)
                        else:
                            print(f"    错误：图像对 {i//2 + 1} 拼接完全失败，保留第一张图像")
                            next_level.append(img1)
                else:
                    # 奇数个图像，最后一张单独保留到下一轮
                    print(f"  保留单张图像 {i+1} 到下一轮")
                    next_level.append(current_level[i])
            
            current_level = next_level
            level += 1
            
            print(f"第 {level-1} 轮完成，得到 {len(current_level)} 张图像")
        
        print("递归拼接完成！")
        return current_level[0] if current_level else None
    
    def create_recursive_panorama(self, image_folder, output_file=None, debug=False):
        """
        从文件夹中读取图像并创建递归拼接全景图
        
        Args:
            image_folder: 图像文件夹路径
            output_file: 输出文件路径（可选）
            debug: 是否保存调试图像
            
        Returns:
            numpy.ndarray: 拼接结果
        """
        # 加载图像
        extensions = ['jpg', 'jpeg', 'png', 'bmp', 'tiff']
        image_paths = []
        for ext in extensions:
            image_paths.extend(glob(os.path.join(image_folder, f'*.{ext}')))
            image_paths.extend(glob(os.path.join(image_folder, f'*.{ext.upper()}')))
        
        image_paths.sort()  # 按文件名排序确保顺序
        
        if not image_paths:
            print("未找到图像文件")
            return None
        
        print(f"加载 {len(image_paths)} 张图像...")
        images = []
        for path in tqdm(image_paths):
            img = cv2.imread(path)
            if img is not None:
                # 调整图像大小以提高处理速度
                h, w = img.shape[:2]
                if w > 1500:  # 限制最大宽度
                    scale = 1500.0 / w
                    new_w = 1500
                    new_h = int(h * scale)
                    img = cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_AREA)
                images.append(img)
                print(f"    加载: {os.path.basename(path)} ({img.shape[1]}x{img.shape[0]})")
        
        if not images:
            print("未能加载任何有效图像")
            return None
        
        # 递归拼接
        result = self.recursive_stitch_images(images, debug=debug)
        
        if result is not None:
            # 最终增强
            result = self.base_stitcher._final_enhancement(result)
            
            # 保存结果
            if output_file:
                cv2.imwrite(output_file, result, [cv2.IMWRITE_JPEG_QUALITY, 95])
                print(f"递归拼接结果已保存到: {output_file}")
        
        return result
    
    def create_step_by_step_panorama(self, image_folder, output_folder=None, debug=False):
        """
        创建逐步拼接的全景图，保存每一步的结果
        
        Args:
            image_folder: 图像文件夹路径
            output_folder: 输出文件夹路径
            debug: 是否保存调试信息
            
        Returns:
            list: 每一步的输出文件路径
        """
        if output_folder is None:
            output_folder = "recursive_output"
        
        os.makedirs(output_folder, exist_ok=True)
        
        # 加载图像
        extensions = ['jpg', 'jpeg', 'png', 'bmp', 'tiff']
        image_paths = []
        for ext in extensions:
            image_paths.extend(glob(os.path.join(image_folder, f'*.{ext}')))
            image_paths.extend(glob(os.path.join(image_folder, f'*.{ext.upper()}')))
        
        image_paths.sort()
        
        if not image_paths:
            print("未找到图像文件")
            return []
        
        print(f"加载 {len(image_paths)} 张图像...")
        images = []
        for path in tqdm(image_paths):
            img = cv2.imread(path)
            if img is not None:
                h, w = img.shape[:2]
                if w > 1500:
                    scale = 1500.0 / w
                    new_w = 1500
                    new_h = int(h * scale)
                    img = cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_AREA)
                images.append(img)
        
        if not images:
            return []
        
        # 递归拼接并保存每一步
        current_level = images.copy()
        level = 1
        output_files = []
        
        while len(current_level) > 1:
            print(f"第 {level} 轮拼接：处理 {len(current_level)} 张图像")
            next_level = []
            
            for i in range(0, len(current_level), 2):
                if i + 1 < len(current_level):
                    img1 = current_level[i]
                    img2 = current_level[i + 1]
                    
                    print(f"  拼接图像对 {i//2 + 1}")
                    stitched = self.base_stitcher.stitch_two_images_natural(img1, img2)
                    
                    if stitched is not None:
                        next_level.append(stitched)
                        # 保存这一步的结果
                        step_file = os.path.join(output_folder, f"step_{level}_{i//2 + 1}.jpg")
                        cv2.imwrite(step_file, stitched, [cv2.IMWRITE_JPEG_QUALITY, 95])
                        output_files.append(step_file)
                    else:
                        stitched = self.base_stitcher.stitch_two_images_natural(img2, img1)
                        if stitched is not None:
                            next_level.append(stitched)
                            step_file = os.path.join(output_folder, f"step_{level}_{i//2 + 1}_reversed.jpg")
                            cv2.imwrite(step_file, stitched, [cv2.IMWRITE_JPEG_QUALITY, 95])
                            output_files.append(step_file)
                        else:
                            next_level.append(img1)
                else:
                    next_level.append(current_level[i])
            
            current_level = next_level
            level += 1
        
        # 保存最终结果
        if current_level:
            final_result = self.base_stitcher._final_enhancement(current_level[0])
            final_file = os.path.join(output_folder, "final_panorama.jpg")
            cv2.imwrite(final_file, final_result, [cv2.IMWRITE_JPEG_QUALITY, 95])
            output_files.append(final_file)
            print(f"最终全景图已保存到: {final_file}")
        
        return output_files 