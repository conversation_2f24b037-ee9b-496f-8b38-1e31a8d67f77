import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Box, IconButton, Fab } from '@mui/material';
import {
  KeyboardArrowUp,
  KeyboardArrowDown,
  KeyboardArrowLeft,
  KeyboardArrowRight,
  FullscreenExit,
  Navigation,
  ChatBubble
} from '@mui/icons-material';

const StreetViewPanorama = ({ 
  imageUrl, 
  isFullscreen, 
  onExitFullscreen,
  onNavigate,
  currentIndex = 0,    // 当前图片索引
  totalImages = 1      // 总图片数量
}) => {
  const canvasRef = useRef(null);
  const imageRef = useRef(null);
  const animationFrameRef = useRef(null);
  
  // 计算当前图片的角度范围
  const calculateAngleRange = useCallback(() => {
    // 每张图片覆盖360度的一个段，从0到360度
    const anglePerImage = 360 / totalImages;
    const startAngle = currentIndex * anglePerImage;
    const endAngle = (currentIndex + 1) * anglePerImage;
    
    return { startAngle, endAngle, range: endAngle - startAngle };
  }, [currentIndex, totalImages]);

  const angleRange = calculateAngleRange();

  // 相机控制状态
  const [camera, setCamera] = useState({
    yaw: 180,      // 初始方向为180度（从中间开始）
    pitch: 0,      // 垂直旋转角度 (上下) - 无黑边设计
    fov: 90,       // 视野角度 (缩放) - 调整为更合适的初始值
    minFov: 40,    // 最小视野角度 (最大缩放) - 避免过度放大失真
    maxFov: 140,   // 最大视野角度 (最小缩放) - 允许更广视野
    maxPitch: 60,  // 最大俯仰角 - 增加范围，配合无黑边渲染
    minYaw: angleRange.startAngle,     // 最小旋转角度
    maxYaw: angleRange.endAngle        // 最大旋转角度
  });
  
  // 鼠标/触摸控制状态
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [lastDrag, setLastDrag] = useState({ yaw: 0, pitch: 0 });
  
  // 键盘控制状态
  const [pressedKeys, setPressedKeys] = useState(new Set());

  // 当图片索引改变时，更新相机角度范围
  useEffect(() => {
    const newAngleRange = calculateAngleRange();
    setCamera(prev => ({
      ...prev,
      minYaw: newAngleRange.startAngle,
      maxYaw: newAngleRange.endAngle,
      yaw: 180 // 重置到中间位置
    }));
  }, [currentIndex, totalImages, calculateAngleRange]);

  // 柱面投影渲染函数
  const renderPanorama = useCallback(() => {
    const canvas = canvasRef.current;
    const image = imageRef.current;
    
    if (!canvas || !image || !image.complete) return;
    
    const ctx = canvas.getContext('2d');
    const { width: canvasWidth, height: canvasHeight } = canvas;
    const { naturalWidth: imgWidth, naturalHeight: imgHeight } = image;
    
    // 清空画布
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);
    
    // 计算投影参数
    const fovRad = (camera.fov * Math.PI) / 180;
    const yawRad = (camera.yaw * Math.PI) / 180;
    const pitchRad = (camera.pitch * Math.PI) / 180;
    
    // 柱面投影渲染
    const centerX = canvasWidth / 2;
    const centerY = canvasHeight / 2;
    const scale = canvasHeight / fovRad;
    
    // 计算可见角度范围
    const visibleAngle = fovRad;
    const startAngle = yawRad - visibleAngle / 2;
    const endAngle = yawRad + visibleAngle / 2;
    
    // 渲染每一列像素
    for (let x = 0; x < canvasWidth; x++) {
      // 计算当前列对应的角度
      const angle = startAngle + (x / canvasWidth) * visibleAngle;
      
      // 将角度转换为图像坐标
      let srcX = ((angle / (2 * Math.PI)) * imgWidth) % imgWidth;
      if (srcX < 0) srcX += imgWidth;
      
      // 计算垂直偏移（考虑俯仰角）
      const pitchOffset = Math.tan(pitchRad) * scale;
      
      // 渲染垂直方向
      for (let y = 0; y < canvasHeight; y++) {
        // 计算垂直角度
        const verticalAngle = ((y - centerY + pitchOffset) / scale);
        
        // 限制垂直角度范围
        if (Math.abs(verticalAngle) > Math.PI / 2) continue;
        
        // 计算源图像的Y坐标
        const srcY = (verticalAngle / Math.PI + 0.5) * imgHeight;
        
        if (srcY >= 0 && srcY < imgHeight) {
          // 双线性插值采样
          const srcXFloor = Math.floor(srcX);
          const srcYFloor = Math.floor(srcY);
          const srcXCeil = (srcXFloor + 1) % imgWidth;
          const srcYCeil = Math.min(srcYFloor + 1, imgHeight - 1);
          
          const dx = srcX - srcXFloor;
          const dy = srcY - srcYFloor;
          
          // 创建临时canvas来获取像素数据
          const tempCanvas = document.createElement('canvas');
          tempCanvas.width = imgWidth;
          tempCanvas.height = imgHeight;
          const tempCtx = tempCanvas.getContext('2d');
          tempCtx.drawImage(image, 0, 0);
          
          try {
            const imageData = tempCtx.getImageData(0, 0, imgWidth, imgHeight);
            const pixels = imageData.data;
            
            // 获取四个相邻像素
            const getPixel = (px, py) => {
              const idx = (py * imgWidth + px) * 4;
              return [
                pixels[idx],     // R
                pixels[idx + 1], // G
                pixels[idx + 2], // B
                pixels[idx + 3]  // A
              ];
            };
            
            const p1 = getPixel(srcXFloor, srcYFloor);
            const p2 = getPixel(srcXCeil, srcYFloor);
            const p3 = getPixel(srcXFloor, srcYCeil);
            const p4 = getPixel(srcXCeil, srcYCeil);
            
            // 双线性插值
            const interpolate = (a, b, c, d, dx, dy) => {
              const top = a * (1 - dx) + b * dx;
              const bottom = c * (1 - dx) + d * dx;
              return top * (1 - dy) + bottom * dy;
            };
            
            const r = interpolate(p1[0], p2[0], p3[0], p4[0], dx, dy);
            const g = interpolate(p1[1], p2[1], p3[1], p4[1], dx, dy);
            const b = interpolate(p1[2], p2[2], p3[2], p4[2], dx, dy);
            const a = interpolate(p1[3], p2[3], p3[3], p4[3], dx, dy);
            
            ctx.fillStyle = `rgba(${Math.round(r)}, ${Math.round(g)}, ${Math.round(b)}, ${a/255})`;
            ctx.fillRect(x, y, 1, 1);
          } catch (e) {
            // 如果无法读取像素数据，使用简单采样
            ctx.drawImage(
              image,
              srcXFloor, srcYFloor, 1, 1,
              x, y, 1, 1
            );
          }
        }
      }
    }
  }, [camera]);

  // 无黑边全景图渲染 - 确保画布始终完全填充
  const renderPanoramaSimple = useCallback(() => {
    const canvas = canvasRef.current;
    const image = imageRef.current;
    
    if (!canvas || !image || !image.complete) return;
    
    const ctx = canvas.getContext('2d');
    // 使用CSS尺寸进行计算，而不是实际canvas尺寸
    const canvasWidth = parseFloat(canvas.style.width) || canvas.width;
    const canvasHeight = parseFloat(canvas.style.height) || canvas.height;
    const { naturalWidth: imgWidth, naturalHeight: imgHeight } = image;
    
    // 清空画布
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);
    
    // 限制相机yaw在允许范围内（不循环）
    const constrainedYaw = Math.max(camera.minYaw, Math.min(camera.maxYaw, camera.yaw));
    
    // 计算当前视图在全景图中的位置（基于限制的角度范围）
    const angleRange = camera.maxYaw - camera.minYaw;
    const yawInRange = constrainedYaw - camera.minYaw;
    const yawRatio = yawInRange / angleRange;
    
    // 基于FOV计算缩放比例
    const baseFov = 90;
    const zoomFactor = baseFov / camera.fov;
    
    // 计算可见区域宽度
    const baseVisibleRatio = 0.6; // 增加基础可见比例
    const visibleRatio = Math.min(baseVisibleRatio / zoomFactor, 1.0);
    const visibleWidth = imgWidth * visibleRatio;
    
    // 计算源图像的起始X坐标
    let srcX = yawRatio * (imgWidth - visibleWidth);
    srcX = Math.max(0, Math.min(imgWidth - visibleWidth, srcX));
    
    // 不限制俯仰角，允许更大范围
    const pitchRange = 60; // 增加俯仰角范围
    const constrainedPitch = Math.max(-pitchRange, Math.min(pitchRange, camera.pitch));
    
    // 计算显示尺寸 - 确保始终填满画布
    const canvasAspectRatio = canvasWidth / canvasHeight;
    const visibleAspectRatio = visibleWidth / imgHeight;
    
    let drawWidth, drawHeight;
    let drawX, drawY;
    
    // 智能缩放策略：确保画布完全覆盖，无任何黑边
    if (visibleAspectRatio > canvasAspectRatio) {
      // 图像更宽 - 以高度为准，确保完全覆盖
      drawHeight = canvasHeight;
      drawWidth = drawHeight * visibleAspectRatio;
      drawX = (canvasWidth - drawWidth) / 2;
      drawY = 0;
    } else {
      // 图像更高 - 以宽度为准，确保完全覆盖
      drawWidth = canvasWidth;
      drawHeight = drawWidth / visibleAspectRatio;
      drawX = 0;
      drawY = (canvasHeight - drawHeight) / 2;
    }
    
    // 应用缩放因子
    const scaleFactor = Math.max(1.0, zoomFactor);
    drawWidth *= scaleFactor;
    drawHeight *= scaleFactor;
    
    // 重新计算居中位置
    drawX = (canvasWidth - drawWidth) / 2;
    drawY = (canvasHeight - drawHeight) / 2;
    
    // 应用俯仰角偏移
    const maxPitchOffset = Math.max(drawHeight - canvasHeight, 0) / 2;
    const pitchOffset = (constrainedPitch / pitchRange) * maxPitchOffset;
    drawY += pitchOffset;
    
    // 确保图像完全覆盖画布 - 关键：延伸图像边界以消除任何可能的黑边
    const extraCoverage = 20; // 额外覆盖像素，确保完全无黑边
    drawX -= extraCoverage;
    drawY -= extraCoverage;
    drawWidth += extraCoverage * 2;
    drawHeight += extraCoverage * 2;
    
    // 启用图像平滑，提高渲染质量
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';
    
    // 无黑边绘制
    ctx.drawImage(
      image,
      srcX, 0, visibleWidth, imgHeight, // 源区域
      drawX, drawY, drawWidth, drawHeight // 目标区域 - 确保完全覆盖
    );
  }, [camera]);

  // 处理鼠标拖拽
  const handleMouseDown = useCallback((e) => {
    setIsDragging(true);
    setDragStart({ x: e.clientX, y: e.clientY });
    setLastDrag({ yaw: camera.yaw, pitch: camera.pitch });
  }, [camera.yaw, camera.pitch]);

  const handleMouseMove = useCallback((e) => {
    if (!isDragging) return;
    
    const deltaX = e.clientX - dragStart.x;
    const deltaY = e.clientY - dragStart.y;
    
    const sensitivity = 0.3;
    const newYaw = lastDrag.yaw - deltaX * sensitivity;
    // 限制水平旋转角度在允许范围内（不循环）
    const constrainedYaw = Math.max(camera.minYaw, Math.min(camera.maxYaw, newYaw));
    // 限制俯仰角范围，避免黑边
    const newPitch = Math.max(-camera.maxPitch, Math.min(camera.maxPitch, lastDrag.pitch + deltaY * sensitivity));
    
    setCamera(prev => ({ ...prev, yaw: constrainedYaw, pitch: newPitch }));
  }, [isDragging, dragStart, lastDrag, camera.maxPitch, camera.minYaw, camera.maxYaw]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // 处理滚轮缩放
  const handleWheel = useCallback((e) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 6 : -6; // 调整滚轮灵敏度
    
    setCamera(prev => ({
      ...prev,
      fov: Math.max(prev.minFov, Math.min(prev.maxFov, prev.fov + delta))
    }));
  }, []);

  // 键盘控制
  const handleKeyDown = useCallback((e) => {
    setPressedKeys(prev => new Set([...prev, e.key]));
    e.preventDefault();
  }, []);

  const handleKeyUp = useCallback((e) => {
    setPressedKeys(prev => {
      const newKeys = new Set(prev);
      newKeys.delete(e.key);
      return newKeys;
    });
  }, []);

  // 方向控制函数 - 重新定义上下箭头功能，限制角度范围
  const rotateCamera = useCallback((deltaYaw, deltaPitch) => {
    setCamera(prev => {
      const newYaw = prev.yaw + deltaYaw;
      // 限制水平旋转角度在允许范围内（不循环）
      const constrainedYaw = Math.max(prev.minYaw, Math.min(prev.maxYaw, newYaw));
      
      return {
        ...prev,
        yaw: constrainedYaw,
        pitch: Math.max(-prev.maxPitch, Math.min(prev.maxPitch, prev.pitch + deltaPitch))
      };
    });
  }, []);

  // 缩放控制函数 - 用于上下箭头
  const handleZoomControl = useCallback((zoomIn) => {
    const deltaFov = zoomIn ? -8 : 8; // 向上=放大(减少FOV)，向下=缩小(增加FOV) - 更平滑的步长
    setCamera(prev => ({
      ...prev,
      fov: Math.max(prev.minFov, Math.min(prev.maxFov, prev.fov + deltaFov))
    }));
  }, []);



  // 键盘输入处理
  useEffect(() => {
    const handleContinuousInput = () => {
      if (pressedKeys.size === 0) return;
      
      let deltaYaw = 0;
      let deltaPitch = 0;
      let deltaFov = 0;
      
      if (pressedKeys.has('ArrowLeft') || pressedKeys.has('a') || pressedKeys.has('A')) {
        deltaYaw = -2;
      }
      if (pressedKeys.has('ArrowRight') || pressedKeys.has('d') || pressedKeys.has('D')) {
        deltaYaw = 2;
      }
      if (pressedKeys.has('ArrowUp') || pressedKeys.has('w') || pressedKeys.has('W')) {
        deltaFov = -3; // 向上键改为放大 - 调整步长
      }
      if (pressedKeys.has('ArrowDown') || pressedKeys.has('s') || pressedKeys.has('S')) {
        deltaFov = 3; // 向下键改为缩小 - 调整步长
      }
      if (pressedKeys.has('+') || pressedKeys.has('=')) {
        deltaFov = -3; // 调整步长
      }
      if (pressedKeys.has('-')) {
        deltaFov = 3; // 调整步长
      }
      
      if (deltaYaw !== 0) {
        rotateCamera(deltaYaw, 0); // 只处理水平旋转
      }
      if (deltaFov !== 0) {
        handleZoomControl(deltaFov < 0); // 负值表示缩小FOV(放大)，正值表示增大FOV(缩小)
      }
    };
    
    const interval = setInterval(handleContinuousInput, 16); // ~60fps
    return () => clearInterval(interval);
  }, [pressedKeys, rotateCamera, handleZoomControl]);

  // 设置canvas尺寸和事件监听 - 高DPI优化
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const updateCanvasSize = () => {
      const rect = canvas.parentElement.getBoundingClientRect();
      const devicePixelRatio = window.devicePixelRatio || 1;
      
      // 设置画布的实际渲染尺寸（支持高DPI）
      canvas.width = rect.width * devicePixelRatio;
      canvas.height = rect.height * devicePixelRatio;
      
      // 设置画布的CSS显示尺寸
      canvas.style.width = rect.width + 'px';
      canvas.style.height = rect.height + 'px';
      
      // 缩放绘图上下文以匹配设备像素比
      const ctx = canvas.getContext('2d');
      ctx.scale(devicePixelRatio, devicePixelRatio);
      
      renderPanoramaSimple();
    };
    
    updateCanvasSize();
    window.addEventListener('resize', updateCanvasSize);
    
    // 添加键盘事件监听
    if (isFullscreen) {
      document.addEventListener('keydown', handleKeyDown);
      document.addEventListener('keyup', handleKeyUp);
    }
    
    return () => {
      window.removeEventListener('resize', updateCanvasSize);
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [isFullscreen, handleKeyDown, handleKeyUp, renderPanoramaSimple]);

  // 重新渲染当相机状态改变时
  useEffect(() => {
    renderPanoramaSimple();
  }, [renderPanoramaSimple]);

  // 加载图像
  useEffect(() => {
    if (imageUrl) {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      img.onload = () => {
        imageRef.current = img;
        renderPanoramaSimple();
      };
      img.src = imageUrl;
    }
  }, [imageUrl, renderPanoramaSimple]);

  return (
    <Box
      sx={{
        position: 'relative',
        width: '100%',
        height: '100%',
        overflow: 'hidden',
        backgroundColor: '#000',
        cursor: isDragging ? 'grabbing' : 'grab'
      }}
    >
      {/* 主画布 */}
      <canvas
        ref={canvasRef}
        style={{
          width: '100%',
          height: '100%',
          display: 'block'
        }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onWheel={handleWheel}
      />
      
      {/* 精美的底部导航控制 */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 40,
          left: '50%',
          transform: 'translateX(-50%)',
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          zIndex: 10,
          background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%)',
          backdropFilter: 'blur(20px) saturate(180%)',
          borderRadius: '28px',
          padding: '12px 16px',
          border: '1px solid rgba(255, 255, 255, 0.3)',
          boxShadow: '0 12px 40px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3)',
          '&:before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            borderRadius: '28px',
            background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(0, 0, 0, 0.1) 100%)',
            pointerEvents: 'none',
          }
        }}
      >
        {/* 左箭头 */}
        <IconButton
          sx={{
            background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%)',
            backdropFilter: 'blur(15px) saturate(180%)',
            color: 'white',
            width: 52,
            height: 52,
            borderRadius: '16px',
            border: '1.5px solid rgba(255, 255, 255, 0.4)',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.5)',
            '&:hover': {
              background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.35) 0%, rgba(255, 255, 255, 0.2) 100%)',
              transform: 'scale(1.05) translateY(-2px)',
              boxShadow: '0 12px 40px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.6), 0 0 20px rgba(255, 255, 255, 0.2)',
            },
            transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
            '&:active': {
              transform: 'scale(0.98) translateY(0px)',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3), inset 0 2px 0 rgba(0, 0, 0, 0.1)',
            }
          }}
          onClick={() => rotateCamera(-15, 0)}
        >
          <KeyboardArrowLeft sx={{ fontSize: 30, filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))' }} />
        </IconButton>
        
        {/* 向前箭头 - 放大 */}
        <IconButton
          sx={{
            background: 'linear-gradient(135deg, #66BB6A 0%, #4CAF50 50%, #388E3C 100%)',
            backdropFilter: 'blur(15px) saturate(180%)',
            color: 'white',
            width: 56,
            height: 56,
            borderRadius: '18px',
            border: '2px solid rgba(255, 255, 255, 0.5)',
            boxShadow: '0 10px 35px rgba(76, 175, 80, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.6)',
            '&:hover': {
              background: 'linear-gradient(135deg, #81C784 0%, #66BB6A 50%, #4CAF50 100%)',
              transform: 'scale(1.08) translateY(-3px)',
              boxShadow: '0 15px 45px rgba(76, 175, 80, 0.6), inset 0 1px 0 rgba(255, 255, 255, 0.7), 0 0 25px rgba(76, 175, 80, 0.4)',
            },
            transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
            '&:active': {
              transform: 'scale(1.02) translateY(-1px)',
              boxShadow: '0 8px 25px rgba(76, 175, 80, 0.5), inset 0 2px 0 rgba(0, 0, 0, 0.1)',
            }
          }}
          onClick={() => handleZoomControl(true)}
        >
          <KeyboardArrowUp sx={{ fontSize: 32, filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.4))' }} />
        </IconButton>
        
        {/* 向后箭头 - 缩小 */}
        <IconButton
          sx={{
            background: 'linear-gradient(135deg, #FFB74D 0%, #FF9800 50%, #F57C00 100%)',
            backdropFilter: 'blur(15px) saturate(180%)',
            color: 'white',
            width: 56,
            height: 56,
            borderRadius: '18px',
            border: '2px solid rgba(255, 255, 255, 0.5)',
            boxShadow: '0 10px 35px rgba(255, 152, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.6)',
            '&:hover': {
              background: 'linear-gradient(135deg, #FFCC02 0%, #FFB74D 50%, #FF9800 100%)',
              transform: 'scale(1.08) translateY(-3px)',
              boxShadow: '0 15px 45px rgba(255, 152, 0, 0.6), inset 0 1px 0 rgba(255, 255, 255, 0.7), 0 0 25px rgba(255, 152, 0, 0.4)',
            },
            transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
            '&:active': {
              transform: 'scale(1.02) translateY(-1px)',
              boxShadow: '0 8px 25px rgba(255, 152, 0, 0.5), inset 0 2px 0 rgba(0, 0, 0, 0.1)',
            }
          }}
          onClick={() => handleZoomControl(false)}
        >
          <KeyboardArrowDown sx={{ fontSize: 32, filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.4))' }} />
        </IconButton>
        
        {/* 右箭头 */}
        <IconButton
          sx={{
            background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%)',
            backdropFilter: 'blur(15px) saturate(180%)',
            color: 'white',
            width: 52,
            height: 52,
            borderRadius: '16px',
            border: '1.5px solid rgba(255, 255, 255, 0.4)',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.5)',
            '&:hover': {
              background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.35) 0%, rgba(255, 255, 255, 0.2) 100%)',
              transform: 'scale(1.05) translateY(-2px)',
              boxShadow: '0 12px 40px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.6), 0 0 20px rgba(255, 255, 255, 0.2)',
            },
            transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
            '&:active': {
              transform: 'scale(0.98) translateY(0px)',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3), inset 0 2px 0 rgba(0, 0, 0, 0.1)',
            }
          }}
          onClick={() => rotateCamera(15, 0)}
        >
          <KeyboardArrowRight sx={{ fontSize: 30, filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))' }} />
        </IconButton>
      </Box>
      

      

      
      {/* 精美的右下角功能按钮组 */}
      <Box sx={{
        position: 'absolute',
        bottom: 20,
        right: 20,
        display: 'flex',
        flexDirection: 'column',
        gap: 3,
        zIndex: 10,
      }}>
        {/* 退出全屏按钮 */}
        {isFullscreen && (
          <IconButton
            onClick={onExitFullscreen}
            sx={{
              background: 'linear-gradient(135deg, #42A5F5 0%, #2196F3 50%, #1976D2 100%)',
              color: 'white',
              width: 64,
              height: 64,
              borderRadius: '20px',
              border: '2px solid rgba(255, 255, 255, 0.3)',
              backdropFilter: 'blur(10px)',
              boxShadow: '0 12px 40px rgba(33, 150, 243, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.4)',
              '&:hover': {
                background: 'linear-gradient(135deg, #64B5F6 0%, #42A5F5 50%, #2196F3 100%)',
                transform: 'scale(1.08) translateY(-4px)',
                boxShadow: '0 16px 50px rgba(33, 150, 243, 0.6), inset 0 1px 0 rgba(255, 255, 255, 0.5), 0 0 30px rgba(33, 150, 243, 0.3)',
              },
              transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
              '&:active': {
                transform: 'scale(1.02) translateY(-1px)',
                boxShadow: '0 8px 30px rgba(33, 150, 243, 0.5), inset 0 2px 0 rgba(0, 0, 0, 0.1)',
              }
            }}
          >
            <FullscreenExit sx={{ fontSize: 28, filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))' }} />
          </IconButton>
        )}
        
        {/* 信息按钮 */}
        <IconButton
          sx={{
            background: 'linear-gradient(135deg, #7E57C2 0%, #673AB7 50%, #512DA8 100%)',
            color: 'white',
            width: 64,
            height: 64,
            borderRadius: '20px',
            border: '2px solid rgba(255, 255, 255, 0.3)',
            backdropFilter: 'blur(10px)',
            boxShadow: '0 12px 40px rgba(103, 58, 183, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.4)',
            '&:hover': {
              background: 'linear-gradient(135deg, #9575CD 0%, #7E57C2 50%, #673AB7 100%)',
              transform: 'scale(1.08) translateY(-4px)',
              boxShadow: '0 16px 50px rgba(103, 58, 183, 0.6), inset 0 1px 0 rgba(255, 255, 255, 0.5), 0 0 30px rgba(103, 58, 183, 0.3)',
            },
            transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
            '&:active': {
              transform: 'scale(1.02) translateY(-1px)',
              boxShadow: '0 8px 30px rgba(103, 58, 183, 0.5), inset 0 2px 0 rgba(0, 0, 0, 0.1)',
            }
          }}
        >
          <ChatBubble sx={{ fontSize: 28, filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))' }} />
        </IconButton>
      </Box>
      

    </Box>
  );
};

export default StreetViewPanorama; 