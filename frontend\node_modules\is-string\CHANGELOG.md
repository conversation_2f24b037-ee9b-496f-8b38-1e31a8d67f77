# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

Generated by [`auto-changelog`](https://github.com/CookPete/auto-changelog).

## [v1.1.1](https://github.com/inspect-js/is-string/compare/v1.1.0...v1.1.1) - 2024-12-15

### Commits

- [Dev <PERSON><PERSON>] update `@arethetypeswrong/cli`, `@ljharb/tsconfig`, `@types/tape` [`c1f7ef7`](https://github.com/inspect-js/is-string/commit/c1f7ef7948b012f769ee3a59952f34ba75a5f5c0)
- [Refactor] use `call-bound` directly [`ba8a78f`](https://github.com/inspect-js/is-string/commit/ba8a78fc1c1fe9b4223c9a8721579026d5ddbce1)
- [Deps] update `call-bind` [`93c352f`](https://github.com/inspect-js/is-string/commit/93c352fd578379c03cd068aea7590de60068a44e)

## [v1.1.0](https://github.com/inspect-js/is-string/compare/v1.0.7...v1.1.0) - 2024-12-01

### Commits

- [actions] reuse common workflows [`12aa75b`](https://github.com/inspect-js/is-string/commit/12aa75b96e2d3f5cd0da139073afba41e6250951)
- [meta] use `npmignore` to autogenerate an npmignore file [`6401572`](https://github.com/inspect-js/is-string/commit/64015720bd63e517d84752eb750bc78178bf707c)
- [actions] split out node 10-20, and 20+ [`223540c`](https://github.com/inspect-js/is-string/commit/223540c6fd5ec982b866bb1b8b3fac37ef5913de)
- [New] add types [`7e83d67`](https://github.com/inspect-js/is-string/commit/7e83d67ae35a65b4d59d59760c7af9e68aafb4a4)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `core-js`, `safe-publish-latest`, `tape` [`febd26e`](https://github.com/inspect-js/is-string/commit/febd26e9211519506d36cd236eca293c7773ae99)
- [readme] add github actions/codecov badges; update URLs [`f6bf065`](https://github.com/inspect-js/is-string/commit/f6bf0657c3b4882844af6b93ecc49872ca15f503)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `core-js`, `tape` [`8afc37a`](https://github.com/inspect-js/is-string/commit/8afc37ae53d7a1888a28e298eb986a9f534fb46e)
- [Robustness] use `call-bind` [`ac86dd7`](https://github.com/inspect-js/is-string/commit/ac86dd7fcf68b90772d1c9f8dd931e0442908f4a)
- [actions] update rebase action to use reusable workflow [`77058c8`](https://github.com/inspect-js/is-string/commit/77058c8c9e70dc77a5331dd654004e1f8500e985)
- [actions] update codecov uploader [`4312be5`](https://github.com/inspect-js/is-string/commit/4312be511df011bb3f3bcc9a46e57755c18465ea)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `core-js`, `tape` [`98c3779`](https://github.com/inspect-js/is-string/commit/98c3779e29220c495b216b6d7cb0813dbe146b59)
- [Dev Deps] update `@ljharb/eslint-config`, `auto-changelog`, `core-js`, `npmignore`, `tape` [`7d8e0e5`](https://github.com/inspect-js/is-string/commit/7d8e0e5e8a9631ffef4def01c7fe4029147cbf89)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `core-js`, `safe-publish-latest`, `tape` [`3284ad1`](https://github.com/inspect-js/is-string/commit/3284ad1a9aeff3eed23d177501c0920e9c5841e3)
- [Tests] replace `aud` with `npm audit` [`8cb7ea7`](https://github.com/inspect-js/is-string/commit/8cb7ea7a493d51435bb093a55acbd32a206e7c1a)
- [Refactor] skip expensive check, for null [`20fde50`](https://github.com/inspect-js/is-string/commit/20fde5092adddc024562f391a96db0b61478a56d)
- [Deps] update `has-tostringtag` [`b67a78d`](https://github.com/inspect-js/is-string/commit/b67a78d4b64d6337d0b6f07ae0af6b04dec327d4)
- [meta] fix repo URL [`1a2ee6b`](https://github.com/inspect-js/is-string/commit/1a2ee6b4556ebd56bdc1288cf34b942ee3b60458)
- [meta] better `eccheck` command [`6913c75`](https://github.com/inspect-js/is-string/commit/6913c75f6e7469e421880a1593c440783aa50ac6)
- [Dev Deps] add missing peer dep [`8ac8551`](https://github.com/inspect-js/is-string/commit/8ac8551d76bf8b68a94ad3504fcbf8f99d16e756)

## [v1.0.7](https://github.com/inspect-js/is-string/compare/v1.0.6...v1.0.7) - 2021-08-05

### Commits

- [Refactor] use `has-tostringtag` to behave correctly in the presence of symbol shams [`d973ffd`](https://github.com/inspect-js/is-string/commit/d973ffd2268e10c0e2cd4f0c57ecf8ce0a8d8578)
- [Dev Deps] update `auto-changelog`, `core-js`, `eslint`, `tape` [`4bfaabf`](https://github.com/inspect-js/is-string/commit/4bfaabf877e874ca21d2c44be26f13add8ee2761)

## [v1.0.6](https://github.com/inspect-js/is-string/compare/v1.0.5...v1.0.6) - 2021-05-07

### Commits

- [Tests] migrate tests to Github Actions [`c7790c8`](https://github.com/inspect-js/is-string/commit/c7790c89e5077251fe7ca32ac29eeee02f1b2751)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`1e52bbd`](https://github.com/inspect-js/is-string/commit/1e52bbd19b1608f6932c0335d9981824584c3186)
- [Fix] do not use `Object.prototype.toString` when `Symbol.toStringTag` is shammed [`83337eb`](https://github.com/inspect-js/is-string/commit/83337ebf55308b7bb9c1befae420760e0f8d8016)
- [meta] do not publish github action workflow files [`b25aea2`](https://github.com/inspect-js/is-string/commit/b25aea2e8a53ed9e9090cf96481590cdc00a0957)
- [readme] update badges [`759ccd9`](https://github.com/inspect-js/is-string/commit/759ccd94de4a2000231a179f91af6b5c12c11e00)
- [Tests] run `nyc` on all tests [`dc02f70`](https://github.com/inspect-js/is-string/commit/dc02f7080c355f0d24368c1622db09f7cc30cdbd)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `auto-changelog`, `tape`; add `aud` [`a0f76fa`](https://github.com/inspect-js/is-string/commit/a0f76fa1990bb580948f9e2daa89bdcda3fae7f0)
- [actions] add "Allow Edits" workflow [`9ec3902`](https://github.com/inspect-js/is-string/commit/9ec390295b4faef7744d2b579c1050be66168cb7)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `tape` [`57fbe21`](https://github.com/inspect-js/is-string/commit/57fbe215da83a3b601855a9c6543ad1a96de5702)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`191e55f`](https://github.com/inspect-js/is-string/commit/191e55ff1fa782654ffcce2df922e23345b56690)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog` [`1ea2b81`](https://github.com/inspect-js/is-string/commit/1ea2b81e866775a7890e75c44c742204124aa354)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`105d1b9`](https://github.com/inspect-js/is-string/commit/105d1b9851e366ef23c2a27d4064e0d36da25939)
- [Dev Deps] update `auto-changelog`, `tape`; add `aud` [`114cfad`](https://github.com/inspect-js/is-string/commit/114cfad854d8860421f847cd99a3bdb8ef1353dc)
- [meta] use `prepublishOnly` script for npm 7+ [`fc38f26`](https://github.com/inspect-js/is-string/commit/fc38f26adb486f50880c5771d145ab2bffb6247a)
- [meta] gitignore coverage output [`3419127`](https://github.com/inspect-js/is-string/commit/34191278f1fa09ba4da801a6fd7a32e31050e759)
- [actions] update rebase action to use checkout v2 [`334eca0`](https://github.com/inspect-js/is-string/commit/334eca02d40f4cf7dc15a8e7d5ff06852028abb5)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`7a332e9`](https://github.com/inspect-js/is-string/commit/7a332e963f1ab717fafa671e0fa8a1b20c53d861)
- [meta] remove explicit audit level config [`04630b1`](https://github.com/inspect-js/is-string/commit/04630b1b535084322ddeae8efb79a8810d7cf325)

## [v1.0.5](https://github.com/inspect-js/is-string/compare/v1.0.4...v1.0.5) - 2019-12-18

### Commits

- [Tests] use shared travis-ci configs [`4121d6b`](https://github.com/inspect-js/is-string/commit/4121d6b168ae1d54a81791ee6877f9813cab6253)
- [Tests] up to `node` `v12.4`, `v11.15`, `v10.15`, `v9.11`, `v8.15`, `v7.10`, `v6.17`, `v5.12`, `v4.9`; use `nvm install-latest-npm` [`e7a3e89`](https://github.com/inspect-js/is-string/commit/e7a3e89ccb9638d73f45dbcb2a42e509bd3153c4)
- Update `eslint`, `tape`, `semver`; use my personal shared `eslint` config [`6c380a7`](https://github.com/inspect-js/is-string/commit/6c380a70011714370e754fa0df95f56cdcaa3e60)
- [Tests] remove `jscs` [`3d49592`](https://github.com/inspect-js/is-string/commit/3d49592b9880fcb1a23b67286445281131a553e3)
- Update 	`is`, `tape`, `covert`, `jscs`, `editorconfig-tools`, `eslint`, `nsp`, `semver`. [`cc6983d`](https://github.com/inspect-js/is-string/commit/cc6983d06bc98f4ae9b7c9439d5d73c7318d8acd)
- [meta] add `auto-changelog` [`b857897`](https://github.com/inspect-js/is-string/commit/b85789723ce3a7064536598e0fcdd495257c6134)
- [meta] remove unused Makefile and associated utilities [`3f0f51c`](https://github.com/inspect-js/is-string/commit/3f0f51cbae1f97dbe1466eee88d105b3df0d2f0a)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `is`, `covert`, `tape`, `semver` [`9d4a95e`](https://github.com/inspect-js/is-string/commit/9d4a95e4473fe8195501878525b5af5948aa45c9)
- Update `eslint` [`e861b4b`](https://github.com/inspect-js/is-string/commit/e861b4bc71f5390670aebdff91119a1f8aeeb88a)
- Update `tape`, `jscs`, `eslint`, `@ljharb/eslint-config` [`172e2dd`](https://github.com/inspect-js/is-string/commit/172e2dd1a0b9eb042bcb9a80ff5e774a90ff0695)
- Test on `node` and `io.js` latest. [`fd426cd`](https://github.com/inspect-js/is-string/commit/fd426cd18b22b0d0e1731598125393dcfe0c5704)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `safe-publish-latest` [`23bdf83`](https://github.com/inspect-js/is-string/commit/23bdf83cf42138eba09f45bd0b040b069f9839d4)
- [actions] add automatic rebasing / merge commit blocking [`96153c0`](https://github.com/inspect-js/is-string/commit/96153c0d687a7fda2261f4c02add5d0b41e8aed7)
- [meta] create FUNDING.yml [`66ae246`](https://github.com/inspect-js/is-string/commit/66ae246d6cdaa4ccbc21f7c144b672139b8ccef6)
- [Dev Deps] update `is`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config`, `semver` [`817361a`](https://github.com/inspect-js/is-string/commit/817361a9673cd1ec9854b52578a980159f7d8701)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `safe-publish-latest`, `semver`, `tape` [`fc35d3f`](https://github.com/inspect-js/is-string/commit/fc35d3feb40921bb22e1639903cb7f2fab77814b)
- [Dev Deps] update `jscs` [`886767e`](https://github.com/inspect-js/is-string/commit/886767e04e5ad59ac0bc926a87233cc8546c8b4f)
- [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops [`3410922`](https://github.com/inspect-js/is-string/commit/341092203c11a3b92eee55a7ecb7b8265e8fcecd)
- [Tests] up to `io.js` `v3.3`, `node` `v4.1` [`4d6c73b`](https://github.com/inspect-js/is-string/commit/4d6c73b507bcd39050ef71e554069f72fc5b222a)
- Update `nsp`, `eslint` [`b11de49`](https://github.com/inspect-js/is-string/commit/b11de4910beee1ffe1e67fbe25ec6707ca796b27)
- Update `eslint`, `semver` [`0777977`](https://github.com/inspect-js/is-string/commit/0777977757a85a1db75831d03a14b4b1fde05d7e)
- Only apps should have lockfiles [`78b49ff`](https://github.com/inspect-js/is-string/commit/78b49ffd04d4cd8c57d9e7b485421fbf3641b41b)
- [meta] add `funding` field [`81328a6`](https://github.com/inspect-js/is-string/commit/81328a6ef3eee989164127e4c0c82f1da73d3567)
- [Dev Deps] update `eslint`, `tape` [`fc9a225`](https://github.com/inspect-js/is-string/commit/fc9a225b27935f7c9c2704281d7fddd3614d3cb8)
- [Tests] use `eclint` instead of `editorconfig-tools` [`59c2c61`](https://github.com/inspect-js/is-string/commit/59c2c610dbd8e8ca1e4aa3fa9c9f93205cab9b07)
- [Dev Deps] Update `tape`, `eslint` [`a429816`](https://github.com/inspect-js/is-string/commit/a429816688e23c81948b4ae72324c26c27849b7c)
- Test on `io.js` `v2.2` [`08b476e`](https://github.com/inspect-js/is-string/commit/08b476ed0734a70e3091c04ddd2f173a2df21eb2)
- Test up to `io.js` `v3.0` [`22637ef`](https://github.com/inspect-js/is-string/commit/22637ef9e0030533df85cf1992fc099a88b1924c)
- [meta] add `safe-publish-latest` [`20ccb48`](https://github.com/inspect-js/is-string/commit/20ccb48fd85f0245eb893507d00003090da020d0)
- [Dev Deps] update `tape` [`06b58a0`](https://github.com/inspect-js/is-string/commit/06b58a048c2a820e5611ad2bd9ddfbe893295a57)
- Switch from vb.teelaun.ch to versionbadg.es for the npm version badge SVG. [`ea7cf84`](https://github.com/inspect-js/is-string/commit/ea7cf849b952c924d1687a302098251a7b827c80)
- Test on `io.js` `v2.4` [`66ec3ea`](https://github.com/inspect-js/is-string/commit/66ec3ea390b364583a792799b53857fd186ccc88)
- Test on `io.js` `v2.3` [`ca6e796`](https://github.com/inspect-js/is-string/commit/ca6e796f16ec433b88962162fde8012f28e18f1e)
- Fix tests for faked @@toStringTag [`3cce832`](https://github.com/inspect-js/is-string/commit/3cce8329133dfd233987359df151018b3b136be1)

## [v1.0.4](https://github.com/inspect-js/is-string/compare/v1.0.3...v1.0.4) - 2015-01-29

### Commits

- If @@toStringTag is not present, use the old-school Object#toString test. [`30675ec`](https://github.com/inspect-js/is-string/commit/30675ecb5c5cc43873918661a414a1d0f8b77325)

## [v1.0.3](https://github.com/inspect-js/is-string/compare/v1.0.2...v1.0.3) - 2015-01-29

### Commits

- Refactor to aid optimization of non-try/catch code. [`9b2772a`](https://github.com/inspect-js/is-string/commit/9b2772abe09ba8cbaa631322cc226ee906d2db22)

## [v1.0.2](https://github.com/inspect-js/is-string/compare/v1.0.1...v1.0.2) - 2015-01-29

### Commits

- Fix broken package.json [`dc921d3`](https://github.com/inspect-js/is-string/commit/dc921d332b64e4041162f04e4712b0dc687863a5)

## [v1.0.1](https://github.com/inspect-js/is-string/compare/v1.0.0...v1.0.1) - 2015-01-29

### Commits

- Fix eslint config. [`c4e05bd`](https://github.com/inspect-js/is-string/commit/c4e05bd171da6002d432e451fd48912db8b048e0)
- Add early exits for typeof "string", or typeof not "object". [`82f41d3`](https://github.com/inspect-js/is-string/commit/82f41d36a599bc6a06152792c84c7683e412c513)

## v1.0.0 - 2015-01-29

### Commits

- Dotfiles. [`45bc9dd`](https://github.com/inspect-js/is-string/commit/45bc9dd60201722344986a6c7536be9ea9ccefbf)
- `make release` [`23707f5`](https://github.com/inspect-js/is-string/commit/23707f5ecfdf00afb0e57c06ac07f7f49cdeb606)
- package.json [`575ad81`](https://github.com/inspect-js/is-string/commit/575ad811c61b156cfbcc60ff61947183c6ebe6a2)
- Read me [`3f67c9a`](https://github.com/inspect-js/is-string/commit/3f67c9a0725f811845d38646a19322895cd03981)
- Initial commit [`2c26a7a`](https://github.com/inspect-js/is-string/commit/2c26a7a2e41dec77be2c59d5847f29a6ab7c0b29)
- Tests. [`38c987b`](https://github.com/inspect-js/is-string/commit/38c987b8513b0ac03b0897e0fce7de8135d4ee0f)
- Implementation. [`0471d59`](https://github.com/inspect-js/is-string/commit/0471d59078d7f3f77619913ec21c57c0af27114c)
