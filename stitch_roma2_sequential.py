#!/usr/bin/env python3
"""
Roma2顺序拼接脚本
按顺序拼接，统一高度，智能重叠处理，无黑边
"""

import os
import sys
import cv2
import numpy as np
import time
from panorama.sequential_stitcher import SequentialPanoramaStitcher

def main():
    """主函数"""
    print("=" * 70)
    print("🏛️  ROMA2 顺序全景拼接")
    print("=" * 70)
    print("🎯 目标:")
    print("- 按1.jpg到16.jpg顺序拼接")
    print("- 统一高度，无透视变形") 
    print("- 智能检测重叠区域")
    print("- 渐变混合重叠部分")
    print("- 最终结果无黑边")
    print("-" * 70)
    
    # 配置参数
    input_folder = "roma2"
    output_file = "roma2_sequential_panorama.jpg"
    target_height = 1500  # 统一高度
    
    # 检查输入文件夹
    if not os.path.exists(input_folder):
        print(f"❌ 错误：文件夹 '{input_folder}' 不存在")
        return
    
    print(f"📁 输入文件夹: {input_folder}")
    print(f"🖼️  输出文件: {output_file}")
    print(f"📏 目标高度: {target_height}px")
    print()
    
    # 创建顺序拼接器
    print("🚀 初始化顺序拼接器...")
    stitcher = SequentialPanoramaStitcher(target_height=target_height)
    
    # 记录开始时间
    start_time = time.time()
    
    try:
        print("🔄 开始Roma2顺序拼接处理...")
        print()
        
        # 使用顺序拼接方法
        result = stitcher.create_sequential_panorama(
            image_folder=input_folder,
            output_file=output_file,
            debug=True  # 启用调试模式，保存中间步骤
        )
        
        # 计算处理时间
        end_time = time.time()
        processing_time = end_time - start_time
        
        if result is not None:
            print()
            print("=" * 70)
            print("🎉 ROMA2顺序拼接成功！")
            print("=" * 70)
            
            # 显示结果信息
            h, w = result.shape[:2]
            print(f"🖼️  最终全景图尺寸: {w:,}×{h:,} 像素")
            print(f"📁 已保存到: {output_file}")
            print(f"⏱️  处理时间: {processing_time:.1f} 秒")
            
            # 显示详细文件信息
            if os.path.exists(output_file):
                file_size_mb = os.path.getsize(output_file) / (1024 * 1024)
                total_pixels = h * w
                
                print(f"📦 文件大小: {file_size_mb:.2f} MB")
                print(f"🔢 像素总数: {total_pixels:,} 像素")
                print(f"📏 高度一致性: {h}px (✅ 统一)")
                
                # 计算内容覆盖率
                 gray = cv2.cvtColor(result, cv2.COLOR_BGR2GRAY)
                 mask = (gray > 5).astype(np.uint8)  # 转换为uint8类型
                 non_black_pixels = cv2.countNonZero(mask)  # 排除接近黑色的像素
                 content_ratio = non_black_pixels / total_pixels
                
                print(f"📊 内容覆盖率: {content_ratio:.1%}")
                
                # 检查黑边情况
                border_pixels = np.sum(gray <= 5)
                black_ratio = border_pixels / total_pixels
                print(f"🖤 黑边占比: {black_ratio:.1%}")
                
                if black_ratio < 0.01:
                    print("✅ 黑边控制良好")
                elif black_ratio < 0.05:
                    print("⚠️  存在少量黑边")
                else:
                    print("❌ 黑边较多，需要优化")
                
                # 计算宽高比
                aspect_ratio = w / h
                print(f"📐 宽高比: {aspect_ratio:.2f}:1")
                
                # 估算图像质量等级
                if total_pixels > 15000000:  # 超过1500万像素
                    quality_level = "🌟 超高清"
                elif total_pixels > 8000000:  # 超过800万像素
                    quality_level = "⭐ 高清"
                elif total_pixels > 3000000:  # 超过300万像素
                    quality_level = "✨ 标清"
                else:
                    quality_level = "📷 普通"
                
                print(f"🏆 质量等级: {quality_level}")
            
            print()
            print("🎯 Roma2顺序拼接特点:")
            print(f"- 按数字顺序拼接16张图片")
            print(f"- 统一高度{target_height}px，保持宽高比")
            print("- 智能检测重叠区域")
            print("- 渐变混合避免接缝")
            print("- 自动去除黑边")
            print("- 跳过相似图片")
            
            # 提供查看建议
            print()
            print("💡 查看建议:")
            if w > 10000:
                print("- 图片很宽，建议使用专业图片查看器")
            if file_size_mb > 20:
                print("- 文件较大，加载可能需要时间")
            print("- 查看debug_step_*.jpg了解拼接过程")
            
        else:
            print("❌ Roma2顺序拼接失败")
            print("🔧 可能原因:")
            print("   1. 图片文件损坏")
            print("   2. 内存不足")
            print("   3. 图片顺序问题")
            
    except Exception as e:
        print(f"❌ 拼接过程中出现错误: {str(e)}")
        print("🔧 错误详情:")
        import traceback
        traceback.print_exc()
        
        print("\n💡 故障排除建议:")
        print("1. 检查roma2文件夹中是否有1.jpg到16.jpg")
        print("2. 确保有足够内存 (建议4GB+)")
        print("3. 检查图片文件是否完整")

def analyze_sequential_result():
    """分析顺序拼接结果"""
    print("\n" + "=" * 70)
    print("📊 顺序拼接结果分析")
    print("=" * 70)
    
    result_file = "roma2_sequential_panorama.jpg"
    
    if not os.path.exists(result_file):
        print(f"❌ 结果文件 '{result_file}' 不存在")
        return
    
    # 读取结果图像
    img = cv2.imread(result_file)
    if img is None:
        print(f"❌ 无法读取 '{result_file}'")
        return
    
    h, w = img.shape[:2]
    print(f"📐 图像尺寸: {w:,}×{h:,} 像素")
    
    # 分析颜色分布
    print("\n🎨 颜色分析:")
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # 计算亮度统计
    mean_brightness = np.mean(gray)
    std_brightness = np.std(gray)
    print(f"💡 平均亮度: {mean_brightness:.1f}")
    print(f"📊 亮度标准差: {std_brightness:.1f}")
    
    # 分析黑边
    black_pixels = np.sum(gray <= 5)
    black_ratio = black_pixels / (h * w)
    print(f"🖤 黑色像素: {black_pixels:,} ({black_ratio:.2%})")
    
    # 分析边缘
    print("\n📏 边缘分析:")
    top_row = gray[0, :]
    bottom_row = gray[-1, :]
    left_col = gray[:, 0]
    right_col = gray[:, -1]
    
    edges_black = [
        np.mean(top_row) < 10,
        np.mean(bottom_row) < 10,
        np.mean(left_col) < 10,
        np.mean(right_col) < 10
    ]
    
    edge_names = ["顶部", "底部", "左侧", "右侧"]
    for i, is_black in enumerate(edges_black):
        status = "❌ 有黑边" if is_black else "✅ 无黑边"
        print(f"{edge_names[i]}: {status}")
    
    # 检查高度一致性
    print(f"\n📏 高度一致性: {h}px")
    if h == 1500:
        print("✅ 符合目标高度")
    else:
        print(f"⚠️  偏离目标高度(1500px)")
    
    # 宽高比分析
    aspect_ratio = w / h
    print(f"📐 宽高比: {aspect_ratio:.2f}:1")
    
    if aspect_ratio > 10:
        print("📺 超宽全景图，适合横屏查看")
    elif aspect_ratio > 5:
        print("🖼️  宽屏全景图")
    else:
        print("📱 标准比例")

def show_usage():
    """显示使用说明"""
    print("Roma2顺序拼接使用说明:")
    print("=" * 50)
    print("1. 基本拼接:")
    print("   python stitch_roma2_sequential.py")
    print()
    print("2. 分析结果:")
    print("   python stitch_roma2_sequential.py --analyze")
    print()
    print("3. 帮助信息:")
    print("   python stitch_roma2_sequential.py --help")
    print()
    print("📁 输入: roma2/ 文件夹 (1.jpg-16.jpg)")
    print("🖼️  输出: roma2_sequential_panorama.jpg")
    print("🔧 调试: debug_step_*.jpg 中间步骤")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] in ['-h', '--help', 'help']:
            show_usage()
        elif sys.argv[1] in ['-a', '--analyze', 'analyze']:
            analyze_sequential_result()
        else:
            print(f"未知参数: {sys.argv[1]}")
            show_usage()
    else:
        main()
        analyze_sequential_result() 