document.addEventListener('DOMContentLoaded', () => {
    // Elements
    const panoramaViewer = document.getElementById('panorama-viewer');
    const currentPanorama = document.getElementById('current-panorama');
    const previousButton = document.getElementById('previous-btn');
    const nextButton = document.getElementById('next-btn');
    const progressBar = document.getElementById('progress-bar');
    const locationIndicator = document.getElementById('location-indicator');
    const locationTotal = document.getElementById('location-total');
    const fullscreenBtn = document.getElementById('fullscreen-btn');
    
    // State
    let currentIndex = 0;
    let isFullscreen = false;
    let isDragging = false;
    let startX = 0;
    let scrollLeft = 0;
    let panoramaWidth = 0;
    
    // Initialize
    init();
    
    // Functions
    function init() {
        // Load first panorama
        loadPanorama(currentIndex);
        
        // Set total locations
        locationTotal.textContent = panoramas.length;
        
        // Update progress bar
        updateProgressBar();
        
        // Set up event listeners
        setupNavigation();
        setupPanoramaInteraction();
        setupFullscreenButton();
        setupKeyboardNavigation();
    }
    
    function setupNavigation() {
        previousButton.addEventListener('click', () => {
            navigateTo(currentIndex - 1);
        });
        
        nextButton.addEventListener('click', () => {
            navigateTo(currentIndex + 1);
        });
    }
    
    function setupPanoramaInteraction() {
        // Mouse events for dragging panorama
        panoramaViewer.addEventListener('mousedown', (e) => {
            isDragging = true;
            startX = e.pageX - panoramaViewer.offsetLeft;
            scrollLeft = currentPanorama.offsetLeft;
            panoramaViewer.style.cursor = 'grabbing';
        });
        
        panoramaViewer.addEventListener('mouseleave', () => {
            isDragging = false;
            panoramaViewer.style.cursor = 'grab';
        });
        
        panoramaViewer.addEventListener('mouseup', () => {
            isDragging = false;
            panoramaViewer.style.cursor = 'grab';
        });
        
        panoramaViewer.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            
            const x = e.pageX - panoramaViewer.offsetLeft;
            const walk = (x - startX) * 2; // Adjust scrolling speed
            
            // Calculate new position, ensuring it doesn't go too far out of bounds
            let newLeft = scrollLeft + walk;
            const maxScroll = panoramaWidth - panoramaViewer.offsetWidth;
            
            // Allow some overflow for better UX, but not too much
            const overflowLimit = panoramaViewer.offsetWidth * 0.2;
            newLeft = Math.max(newLeft, -panoramaWidth + overflowLimit);
            newLeft = Math.min(newLeft, panoramaViewer.offsetWidth - overflowLimit);
            
            currentPanorama.style.left = `${newLeft}px`;
        });
        
        // Touch events for mobile dragging
        panoramaViewer.addEventListener('touchstart', (e) => {
            isDragging = true;
            startX = e.touches[0].pageX - panoramaViewer.offsetLeft;
            scrollLeft = currentPanorama.offsetLeft;
        });
        
        panoramaViewer.addEventListener('touchend', () => {
            isDragging = false;
        });
        
        panoramaViewer.addEventListener('touchmove', (e) => {
            if (!isDragging) return;
            e.preventDefault();
            
            const x = e.touches[0].pageX - panoramaViewer.offsetLeft;
            const walk = (x - startX) * 2;
            
            // Calculate new position with bounds
            let newLeft = scrollLeft + walk;
            const maxScroll = panoramaWidth - panoramaViewer.offsetWidth;
            
            const overflowLimit = panoramaViewer.offsetWidth * 0.2;
            newLeft = Math.max(newLeft, -panoramaWidth + overflowLimit);
            newLeft = Math.min(newLeft, panoramaViewer.offsetWidth - overflowLimit);
            
            currentPanorama.style.left = `${newLeft}px`;
        });
    }
    
    function setupFullscreenButton() {
        fullscreenBtn.addEventListener('click', toggleFullscreen);
    }
    
    function setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            switch (e.key) {
                case 'ArrowLeft':
                    navigateTo(currentIndex - 1);
                    break;
                case 'ArrowRight':
                    navigateTo(currentIndex + 1);
                    break;
                case 'f':
                    toggleFullscreen();
                    break;
            }
        });
    }
    
    function loadPanorama(index) {
        if (index < 0 || index >= panoramas.length) return;
        
        // Load new panorama
        currentPanorama.src = panoramas[index];
        
        // Reset position
        currentPanorama.style.left = '0';
        
        // Update current index
        currentIndex = index;
        
        // Update UI
        locationIndicator.textContent = currentIndex + 1;
        updateProgressBar();
        updateNavigationButtons();
        
        // Get panorama dimensions when loaded
        currentPanorama.onload = () => {
            panoramaWidth = currentPanorama.offsetWidth;
            centerPanorama();
        };
    }
    
    function centerPanorama() {
        // Center the panorama horizontally
        const viewerWidth = panoramaViewer.offsetWidth;
        const imageWidth = currentPanorama.offsetWidth;
        
        if (imageWidth > viewerWidth) {
            // If panorama is wider than viewer, center it
            const centerOffset = (imageWidth - viewerWidth) / 2;
            currentPanorama.style.left = `-${centerOffset}px`;
            scrollLeft = -centerOffset;
        } else {
            // If panorama is narrower, just center it
            const centerOffset = (viewerWidth - imageWidth) / 2;
            currentPanorama.style.left = `${centerOffset}px`;
            scrollLeft = centerOffset;
        }
    }
    
    function navigateTo(index) {
        // Check bounds
        if (index < 0 || index >= panoramas.length) return;
        
        loadPanorama(index);
    }
    
    function updateProgressBar() {
        const progress = ((currentIndex + 1) / panoramas.length) * 100;
        progressBar.style.width = `${progress}%`;
    }
    
    function updateNavigationButtons() {
        // Enable/disable navigation buttons based on current position
        previousButton.disabled = currentIndex === 0;
        nextButton.disabled = currentIndex === panoramas.length - 1;
    }
    
    function toggleFullscreen() {
        const container = document.querySelector('.viewer-container');
        
        if (!isFullscreen) {
            // Enter fullscreen
            if (container.requestFullscreen) {
                container.requestFullscreen();
            } else if (container.mozRequestFullScreen) {
                container.mozRequestFullScreen();
            } else if (container.webkitRequestFullscreen) {
                container.webkitRequestFullscreen();
            } else if (container.msRequestFullscreen) {
                container.msRequestFullscreen();
            }
            
            container.classList.add('fullscreen');
            fullscreenBtn.textContent = '⤓ Exit Fullscreen';
        } else {
            // Exit fullscreen
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.mozCancelFullScreen) {
                document.mozCancelFullScreen();
            } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
            } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
            }
            
            container.classList.remove('fullscreen');
            fullscreenBtn.textContent = '⤢ Fullscreen';
        }
        
        isFullscreen = !isFullscreen;
    }
    
    // Listen for fullscreen changes
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);
    
    function handleFullscreenChange() {
        if (!document.fullscreenElement && 
            !document.webkitFullscreenElement && 
            !document.mozFullScreenElement && 
            !document.msFullscreenElement) {
            
            // Update UI when exiting fullscreen
            isFullscreen = false;
            document.querySelector('.viewer-container').classList.remove('fullscreen');
            fullscreenBtn.textContent = '⤢ Fullscreen';
        }
    }
}); 