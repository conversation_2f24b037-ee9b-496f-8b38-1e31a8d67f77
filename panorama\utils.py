import os
import cv2
import numpy as np
from glob import glob
from tqdm import tqdm
from sklearn.cluster import KMeans
import matplotlib.pyplot as plt

def load_images(folder_path, max_size=None):
    """
    Load all images from a folder
    
    Args:
        folder_path (str): Path to folder containing images
        max_size (tuple): Max size (width, height) to resize images to
        
    Returns:
        list: List of loaded images
    """
    print(f"Loading images from: {folder_path}")
    if not os.path.exists(folder_path):
        print(f"Error: Folder path does not exist: {folder_path}")
        return []
        
    # Get all image files
    extensions = ['jpg', 'jpeg', 'png', 'bmp', 'tiff']
    image_paths = []
    for ext in extensions:
        found_paths = glob(os.path.join(folder_path, f'*.{ext}'))
        found_paths.extend(glob(os.path.join(folder_path, f'*.{ext.upper()}')))
        image_paths.extend(found_paths)
        print(f"Found {len(found_paths)} files with extension .{ext}")
    
    print(f"Total images found: {len(image_paths)}")
    
    # Sort images by name to maintain sequence
    image_paths.sort()
    
    images = []
    for path in tqdm(image_paths, desc="Loading images"):
        img = cv2.imread(path)
        if img is None:
            print(f"Failed to load {path}")
            continue
        
        # Skip image enhancement to preserve original quality
        # img = enhance_image_quality(img)
        
        # Resize if needed
        if max_size:
            h, w = img.shape[:2]
            if w > max_size[0] or h > max_size[1]:
                # Calculate new dimensions while preserving aspect ratio
                if w/h > max_size[0]/max_size[1]:
                    new_w = max_size[0]
                    new_h = int(h * (max_size[0] / w))
                else:
                    new_h = max_size[1]
                    new_w = int(w * (max_size[1] / h))
                
                # Use INTER_LINEAR instead of LANCZOS4 for simpler interpolation
                img = cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
        
        images.append(img)
    
    print(f"Successfully loaded {len(images)} images")
    return images

def save_image(filename, image, quality_params=None):
    """
    Save an image to disk
    
    Args:
        filename (str): Path to save image
        image (numpy.ndarray): Image to save
        quality_params (list): Optional quality parameters for cv2.imwrite
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Ensure directory exists
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        # Use the provided quality parameters or default to highest quality
        if quality_params:
            cv2.imwrite(filename, image, quality_params)
        else:
            # Use highest quality for JPEG (100) or PNG compression (9)
            if filename.lower().endswith(('.jpg', '.jpeg')):
                cv2.imwrite(filename, image, [cv2.IMWRITE_JPEG_QUALITY, 100])
            elif filename.lower().endswith('.png'):
                cv2.imwrite(filename, image, [cv2.IMWRITE_PNG_COMPRESSION, 0])  # 0 = no compression
            else:
                cv2.imwrite(filename, image)
        return True
    except Exception as e:
        print(f"Error saving image {filename}: {e}")
        return False

def blend_images(img1, img2, mask, blend_width=40):
    """
    Blend two images using a mask with improved feathering
    
    Args:
        img1 (numpy.ndarray): First image
        img2 (numpy.ndarray): Second image
        mask (numpy.ndarray): Mask indicating which pixels to blend
        blend_width (int): Width of the blending area
        
    Returns:
        numpy.ndarray: Blended image
    """
    # Ensure mask has proper dimensions
    if mask.ndim == 2:
        mask = mask[:, :, np.newaxis]
    
    # Create a feathered mask for smoother transitions
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (blend_width, blend_width))
    feathered_mask = cv2.erode(mask, kernel)
    feathered_mask = cv2.GaussianBlur(feathered_mask, (blend_width*2+1, blend_width*2+1), 0)
    
    # Normalize mask to range [0, 1]
    mask_normalized = feathered_mask.astype(float) / 255
    
    # Blend images using the feathered mask
    blended = img1 * (1 - mask_normalized) + img2 * mask_normalized
    
    return blended.astype(np.uint8)

def create_circular_mask(h, w, center=None, radius=None):
    """
    Create a circular mask for vignette effects or circular panoramas
    
    Args:
        h (int): Height of mask
        w (int): Width of mask
        center (tuple): Center point (x, y)
        radius (int): Radius of circle
        
    Returns:
        numpy.ndarray: Binary mask
    """
    if center is None:
        center = (w // 2, h // 2)
    if radius is None:
        radius = min(h, w) // 2
    
    Y, X = np.ogrid[:h, :w]
    dist_from_center = np.sqrt((X - center[0])**2 + (Y - center[1])**2)
    
    mask = dist_from_center <= radius
    return mask.astype(np.uint8) * 255

def match_histograms(source, reference):
    """
    Match the histogram of source image to reference image
    
    Args:
        source (numpy.ndarray): Source image to transform
        reference (numpy.ndarray): Reference image
        
    Returns:
        numpy.ndarray: Histogram-matched image
    """
    # Convert images to LAB color space
    source_lab = cv2.cvtColor(source, cv2.COLOR_BGR2LAB)
    reference_lab = cv2.cvtColor(reference, cv2.COLOR_BGR2LAB)
    
    # Split the LAB channels
    source_l, source_a, source_b = cv2.split(source_lab)
    reference_l, reference_a, reference_b = cv2.split(reference_lab)
    
    # Match the L channel histograms
    matched_l = match_histogram_channel(source_l, reference_l)
    
    # Merge the channels back
    matched_lab = cv2.merge([matched_l, source_a, source_b])
    
    # Convert back to BGR
    matched = cv2.cvtColor(matched_lab, cv2.COLOR_LAB2BGR)
    
    return matched

def match_histogram_channel(source, reference):
    """
    Match histogram of a single channel
    
    Args:
        source (numpy.ndarray): Source channel
        reference (numpy.ndarray): Reference channel
        
    Returns:
        numpy.ndarray: Matched channel
    """
    # Calculate histograms
    src_hist, src_bins = np.histogram(source.flatten(), 256, [0, 256])
    ref_hist, ref_bins = np.histogram(reference.flatten(), 256, [0, 256])
    
    # Calculate cumulative distribution functions
    src_cdf = src_hist.cumsum()
    src_cdf_normalized = src_cdf * float(src_hist.max()) / src_cdf.max()
    
    ref_cdf = ref_hist.cumsum()
    ref_cdf_normalized = ref_cdf * float(ref_hist.max()) / ref_cdf.max()
    
    # Create lookup table
    lookup_table = np.zeros(256, dtype=np.uint8)
    for i in range(256):
        j = 0
        while j < 256 and ref_cdf_normalized[j] <= src_cdf_normalized[i]:
            j += 1
        lookup_table[i] = j - 1 if j > 0 else 0
    
    # Apply lookup table
    return cv2.LUT(source, lookup_table)

def detect_scene_changes(images, threshold=0.5):
    """
    Detect major scene changes in a sequence of images
    
    Args:
        images (list): List of images
        threshold (float): Threshold for scene change detection
        
    Returns:
        list: Indices of images where scene changes
    """
    if not images or len(images) < 2:
        return []
    
    scene_changes = []
    
    for i in range(1, len(images)):
        # Convert to grayscale
        gray1 = cv2.cvtColor(images[i-1], cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(images[i], cv2.COLOR_BGR2GRAY)
        
        # Calculate difference
        diff = cv2.absdiff(gray1, gray2)
        
        # Threshold the difference
        _, thresh = cv2.threshold(diff, 30, 255, cv2.THRESH_BINARY)
        
        # Calculate percentage of changed pixels
        change_percent = np.count_nonzero(thresh) / thresh.size
        
        if change_percent > threshold:
            scene_changes.append(i)
    
    return scene_changes

def stabilize_sequence(images):
    """
    Apply advanced image stabilization to a sequence
    
    Args:
        images (list): List of images
        
    Returns:
        list: Stabilized images
    """
    if not images or len(images) < 2:
        return images
    
    stabilized = [images[0]]
    prev_gray = cv2.cvtColor(images[0], cv2.COLOR_BGR2GRAY)
    
    # Define motion model - use homography for better perspective handling
    warp_mode = cv2.MOTION_HOMOGRAPHY
    warp_matrix = np.eye(3, 3, dtype=np.float32)
    
    # Define termination criteria
    criteria = (cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 100, 0.0001)
    
    for i in tqdm(range(1, len(images)), desc="Stabilizing"):
        # Convert to grayscale
        curr_gray = cv2.cvtColor(images[i], cv2.COLOR_BGR2GRAY)
        
        try:
            # Detect features in both frames
            prev_pts = cv2.goodFeaturesToTrack(prev_gray, maxCorners=200, qualityLevel=0.01, minDistance=30)
            
            if prev_pts is not None and len(prev_pts) > 10:
                # Track features
                curr_pts, status, _ = cv2.calcOpticalFlowPyrLK(prev_gray, curr_gray, prev_pts, None)
                
                # Filter only valid points
                idx = np.where(status == 1)[0]
                if len(idx) >= 4:  # Need at least 4 points for homography
                    prev_valid = prev_pts[idx].reshape(-1, 2)
                    curr_valid = curr_pts[idx].reshape(-1, 2)
                    
                    # Find homography
                    H, mask = cv2.findHomography(curr_valid, prev_valid, cv2.RANSAC, 5.0)
                    
                    if H is not None:
                        # Apply transformation
                        h, w = images[i].shape[:2]
                        stabilized_frame = cv2.warpPerspective(images[i], H, (w, h))
                        stabilized.append(stabilized_frame)
                    else:
                        print(f"Homography estimation failed for frame {i}")
                        stabilized.append(images[i])
                else:
                    print(f"Not enough matching points for frame {i}")
                    stabilized.append(images[i])
            else:
                print(f"Not enough features detected in frame {i-1}")
                stabilized.append(images[i])
                
            # Update previous frame
            prev_gray = curr_gray
        except Exception as e:
            print(f"Stabilization failed for frame {i}: {e}")
            stabilized.append(images[i])
    
    return stabilized

def enhance_image_contrast(image, clip_limit=2.0, tile_grid_size=(8, 8)):
    """
    Enhance image contrast using CLAHE
    
    Args:
        image (numpy.ndarray): Input image
        clip_limit (float): Threshold for contrast limiting
        tile_grid_size (tuple): Size of grid for histogram equalization
        
    Returns:
        numpy.ndarray: Enhanced image
    """
    # Convert to LAB color space
    lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
    l, a, b = cv2.split(lab)
    
    # Apply CLAHE to L-channel
    clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=tile_grid_size)
    cl = clahe.apply(l)
    
    # Merge channels and convert back to BGR
    enhanced_lab = cv2.merge((cl, a, b))
    enhanced = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)
    
    return enhanced

def enhance_image_quality(image):
    """
    Comprehensive image enhancement for better stitching quality
    
    Args:
        image (numpy.ndarray): Input image
        
    Returns:
        numpy.ndarray: Enhanced image
    """
    if image is None:
        return None
        
    # 1. 降噪处理 - 使用非局部均值去噪，保留边缘细节
    denoised = cv2.fastNlMeansDenoisingColored(image, None, 5, 5, 7, 21)
    
    # 2. 对比度增强 - 使用CLAHE但保持适度参数
    enhanced = enhance_image_contrast(denoised, clip_limit=1.5, tile_grid_size=(4, 4))
    
    # 3. 锐化 - 使用USM锐化算法增强边缘
    gaussian = cv2.GaussianBlur(enhanced, (0, 0), 3)
    unsharp_mask = cv2.addWeighted(enhanced, 1.5, gaussian, -0.5, 0)
    
    # 4. 色彩校正 - 轻微增加饱和度
    hsv = cv2.cvtColor(unsharp_mask, cv2.COLOR_BGR2HSV).astype('float32')
    h, s, v = cv2.split(hsv)
    s = np.clip(s * 1.2, 0, 255)  # 增加饱和度
    hsv_enhanced = cv2.merge([h, s, v])
    result = cv2.cvtColor(hsv_enhanced.astype('uint8'), cv2.COLOR_HSV2BGR)
    
    return result

def find_optimal_image_sequence(image_folder, max_images=50):
    """
    Find the optimal sequence of images for stitching
    by selecting representative frames
    
    Args:
        image_folder (str): Folder containing images
        max_images (int): Maximum number of images to select
        
    Returns:
        list: Paths to selected images
    """
    # Get all image files
    extensions = ['jpg', 'jpeg', 'png', 'bmp', 'tiff']
    image_paths = []
    for ext in extensions:
        image_paths.extend(glob(os.path.join(image_folder, f'*.{ext}')))
        image_paths.extend(glob(os.path.join(image_folder, f'*.{ext.upper()}')))
    
    # Sort images by name to maintain sequence
    image_paths.sort()
    
    if len(image_paths) <= max_images:
        return image_paths
    
    # Extract features from each image
    features = []
    for path in tqdm(image_paths, desc="Extracting features"):
        img = cv2.imread(path, 0)  # Read as grayscale
        if img is None:
            continue
            
        # Resize for faster processing
        img = cv2.resize(img, (100, 100))
        
        # Extract histogram as feature
        hist = cv2.calcHist([img], [0], None, [64], [0, 256])
        hist = cv2.normalize(hist, hist).flatten()
        features.append(hist)
    
    # Use k-means to find representative images
    kmeans = KMeans(n_clusters=max_images, random_state=0)
    kmeans.fit(features)
    
    # Get the closest image to each cluster center
    selected_indices = []
    for i in range(max_images):
        cluster_members = np.where(kmeans.labels_ == i)[0]
        if len(cluster_members) == 0:
            continue
            
        # Find the closest member to cluster center
        distances = [np.linalg.norm(features[j] - kmeans.cluster_centers_[i]) for j in cluster_members]
        closest_idx = cluster_members[np.argmin(distances)]
        selected_indices.append(closest_idx)
    
    # Sort indices to maintain temporal order
    selected_indices.sort()
    
    # Return selected image paths
    return [image_paths[i] for i in selected_indices]

def optimize_image_order(images):
    """
    优化图像顺序，尽量使相邻图像有更多重叠
    
    Args:
        images (list): 图像列表
        
    Returns:
        list: 优化后的图像列表
    """
    if len(images) <= 2:
        return images
    
    # 使用特征匹配计算图像间的相似度
    n_images = len(images)
    similarity_matrix = np.zeros((n_images, n_images))
    
    # 创建特征检测器
    sift = cv2.SIFT_create()
    
    # 计算每张图像的特征
    print("计算图像特征...")
    features = []
    for img in tqdm(images):
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        kp, des = sift.detectAndCompute(gray, None)
        features.append((kp, des))
    
    # 计算图像间的相似度
    print("计算图像相似度...")
    for i in tqdm(range(n_images)):
        for j in range(i+1, n_images):
            if features[i][1] is not None and features[j][1] is not None:
                # 使用FLANN匹配器
                FLANN_INDEX_KDTREE = 1
                index_params = dict(algorithm=FLANN_INDEX_KDTREE, trees=5)
                search_params = dict(checks=50)
                flann = cv2.FlannBasedMatcher(index_params, search_params)
                
                try:
                    matches = flann.knnMatch(features[i][1], features[j][1], k=2)
                    
                    # 应用Lowe's比率测试
                    good_matches = []
                    for m_n in matches:
                        # 确保有两个匹配点
                        if len(m_n) == 2:
                            m, n = m_n
                            if m.distance < 0.7 * n.distance:
                                good_matches.append(m)
                    
                    # 相似度基于好的匹配点数量
                    similarity_matrix[i, j] = len(good_matches)
                    similarity_matrix[j, i] = len(good_matches)
                except Exception as e:
                    print(f"匹配图像 {i} 和 {j} 时出错: {str(e)}")
                    similarity_matrix[i, j] = 0
                    similarity_matrix[j, i] = 0
    
    # 使用贪婪算法找到最佳路径
    path = [0]  # 从第一张图像开始
    remaining = set(range(1, n_images))
    
    while remaining:
        last = path[-1]
        next_idx = max(remaining, key=lambda x: similarity_matrix[last, x])
        path.append(next_idx)
        remaining.remove(next_idx)
    
    # 返回重新排序的图像
    return [images[i] for i in path]

def visualize_keypoints(image, keypoints):
    """
    Visualize keypoints on an image
    
    Args:
        image (numpy.ndarray): Input image
        keypoints: List of keypoints
        
    Returns:
        numpy.ndarray: Image with keypoints
    """
    return cv2.drawKeypoints(image, keypoints, None, (0, 255, 0), 
                            cv2.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS)

def visualize_matches(img1, kp1, img2, kp2, matches):
    """
    Visualize matches between two images
    
    Args:
        img1, img2: Input images
        kp1, kp2: Keypoints for each image
        matches: List of matches
        
    Returns:
        numpy.ndarray: Image showing matches
    """
    return cv2.drawMatches(img1, kp1, img2, kp2, matches, None, 
                          flags=cv2.DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS)

def save_debug_image(filename, image):
    """
    Save a debug image to disk and ensure the directory exists
    
    Args:
        filename (str): Path to save image
        image (numpy.ndarray): Image to save
    """
    # Ensure directory exists
    os.makedirs(os.path.dirname(filename), exist_ok=True)
    
    # Save image
    cv2.imwrite(filename, image)
    print(f"Debug image saved to {filename}")

def debug_stitching(img1, img2, matches, homography, status, output_path, kp1=None, kp2=None):
    """
    Generate debug visualization for stitching process
    
    Args:
        img1 (numpy.ndarray): First image
        img2 (numpy.ndarray): Second image
        matches (list): List of matches
        homography (numpy.ndarray): Homography matrix
        status (numpy.ndarray): Status of each match (inlier/outlier)
        output_path (str): Path to save debug image
        kp1 (list): Keypoints from first image
        kp2 (list): Keypoints from second image
    """
    # Ensure directory exists
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Create visualization
    h1, w1 = img1.shape[:2]
    h2, w2 = img2.shape[:2]
    
    # Create a blank canvas
    vis = np.zeros((max(h1, h2), w1 + w2, 3), dtype=np.uint8)
    
    # Place images side by side
    vis[:h1, :w1] = img1
    vis[:h2, w1:w1+w2] = img2
    
    # Check if keypoints are provided
    if kp1 is None or kp2 is None:
        # Just save the side-by-side images without drawing matches
        cv2.imwrite(output_path, vis)
        print(f"Debug image saved to {output_path} (without matches)")
        return
    
    # Draw matches
    inlier_color = (0, 255, 0)  # Green for inliers
    outlier_color = (0, 0, 255)  # Red for outliers
    
    for i, (idx1, idx2) in enumerate(matches):
        # Get keypoint coordinates
        pt1 = tuple(map(int, kp1[idx1]))
        pt2 = (int(kp2[idx2][0] + w1), int(kp2[idx2][1]))
        
        # Draw line between matched keypoints
        color = inlier_color if status[i][0] == 1 else outlier_color
        cv2.line(vis, pt1, pt2, color, 1)
        
        # Draw keypoints
        cv2.circle(vis, pt1, 3, color, -1)
        cv2.circle(vis, pt2, 3, color, -1)
    
    # Save visualization
    cv2.imwrite(output_path, vis)
    print(f"Debug image saved to {output_path}")

def apply_vignette_correction(image):
    """
    修正图像中的暗角效应
    
    Args:
        image (numpy.ndarray): 输入图像
        
    Returns:
        numpy.ndarray: 修正后的图像
    """
    rows, cols = image.shape[:2]
    
    # 生成径向渐变掩码
    kernel_x = cv2.getGaussianKernel(cols, cols/2)
    kernel_y = cv2.getGaussianKernel(rows, rows/2)
    kernel = kernel_y * kernel_x.T
    mask = kernel / kernel.max()
    
    # 扩展掩码到3通道
    mask_3channel = np.dstack([mask, mask, mask])
    
    # 应用掩码进行校正
    result = image.astype(np.float32) / mask_3channel
    result = np.clip(result, 0, 255).astype(np.uint8)
    
    return result

def correct_exposure_differences(images):
    """
    校正一组图像中的曝光差异
    
    Args:
        images (list): 图像列表
        
    Returns:
        list: 曝光校正后的图像列表
    """
    if len(images) <= 1:
        return images
    
    # 计算所有图像的平均亮度
    mean_brightness = []
    for img in images:
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        mean_brightness.append(np.mean(gray))
    
    # 计算目标亮度（使用中值作为目标）
    target_brightness = np.median(mean_brightness)
    
    # 校正每张图像的亮度
    corrected_images = []
    for i, img in enumerate(images):
        # 计算亮度调整因子
        alpha = target_brightness / mean_brightness[i]
        
        # 应用亮度调整
        corrected = cv2.convertScaleAbs(img, alpha=alpha, beta=0)
        corrected_images.append(corrected)
    
    return corrected_images 