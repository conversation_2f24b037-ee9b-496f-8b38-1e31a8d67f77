{"ast": null, "code": "var _jsxFileName = \"D:\\\\Study_Code\\\\2025-8-5\\\\qrjy_images\\\\frontend\\\\src\\\\pages\\\\CreateScene.js\";\nimport React, { useState, useCallback, useRef } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport Typography from '@mui/material/Typography';\nimport Button from '@mui/material/Button';\nimport Paper from '@mui/material/Paper';\nimport Box from '@mui/material/Box';\nimport TextField from '@mui/material/TextField';\nimport CircularProgress from '@mui/material/CircularProgress';\nimport LinearProgress from '@mui/material/LinearProgress';\nimport Card from '@mui/material/Card';\nimport CardContent from '@mui/material/CardContent';\nimport FormControl from '@mui/material/FormControl';\nimport FormLabel from '@mui/material/FormLabel';\nimport RadioGroup from '@mui/material/RadioGroup';\nimport Radio from '@mui/material/Radio';\nimport FormControlLabel from '@mui/material/FormControlLabel';\nimport Stepper from '@mui/material/Stepper';\nimport Step from '@mui/material/Step';\nimport StepLabel from '@mui/material/StepLabel';\nimport Alert from '@mui/material/Alert';\nimport ImageList from '@mui/material/ImageList';\nimport ImageListItem from '@mui/material/ImageListItem';\nimport CloudUploadIcon from '@mui/icons-material/CloudUpload';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CreateScene = () => {\n  const navigate = useNavigate();\n  const fileInputRef = useRef(null);\n  const [activeStep, setActiveStep] = useState(0);\n  const [sceneName, setSceneName] = useState('');\n  const [scene, setScene] = useState(null);\n  const [files, setFiles] = useState([]);\n  const [uploading, setUploading] = useState(false);\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [processingMode, setProcessingMode] = useState('panorama');\n  const [stitcherType, setStitcherType] = useState('anti_ghosting'); // 默认使用防重影算法\n  const stitchMode = 'single'; // 拼接模式：固定使用完整拼接（单张全景图）\n  const [processing, setProcessing] = useState(false);\n  const [error, setError] = useState('');\n  const [processingInterval, setProcessingInterval] = useState(null);\n\n  // Handle file selection\n  const handleFileSelect = useCallback(event => {\n    const selectedFiles = Array.from(event.target.files);\n    // Filter for image files and add preview URLs\n    const imageFiles = selectedFiles.filter(file => file.type.startsWith('image/'));\n    setFiles(imageFiles.map(file => Object.assign(file, {\n      preview: URL.createObjectURL(file)\n    })));\n  }, []);\n\n  // Handle click on upload area\n  const handleUploadAreaClick = () => {\n    var _fileInputRef$current;\n    (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n  };\n\n  // Create a new scene\n  const handleCreateScene = async () => {\n    if (!sceneName.trim()) {\n      setError('Please enter a scene name');\n      return;\n    }\n    try {\n      const formData = new FormData();\n      formData.append('name', sceneName);\n      const response = await axios.post('/api/scenes', formData);\n      setScene(response.data);\n      setActiveStep(1);\n      setError('');\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError('Failed to create scene: ' + (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || err.message));\n    }\n  };\n\n  // Upload files to the scene\n  const handleUploadFiles = async () => {\n    if (files.length === 0) {\n      setError('Please select files first');\n      return;\n    }\n    setUploading(true);\n    setUploadProgress(0);\n    try {\n      const formData = new FormData();\n      files.forEach(file => {\n        formData.append('files', file);\n      });\n      await axios.post(`/api/scenes/${scene.id}/upload`, formData, {\n        onUploadProgress: progressEvent => {\n          const percentCompleted = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n          setUploadProgress(percentCompleted);\n        }\n      });\n      setActiveStep(2);\n      setError('');\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      setError('Upload failed: ' + (((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.message) || err.message));\n    } finally {\n      setUploading(false);\n    }\n  };\n\n  // Process the uploaded images\n  const handleProcess = useCallback(async () => {\n    // 防止重复点击\n    if (processing) return;\n    setProcessing(true);\n    setError('');\n    setUploadProgress(0);\n    try {\n      // 构建处理参数\n      const params = {\n        stitcher_type: stitcherType\n      };\n\n      // 如果是 Street View 模式，添加相关参数\n      if (processingMode === 'streetview') {\n        // 添加拼接模式参数\n        params.stitch_mode = stitchMode;\n      }\n\n      // Start processing\n      await axios.post(`/api/scenes/${scene.id}/process`, {\n        mode: processingMode,\n        params: params\n      });\n\n      // Set up polling to check status\n      const interval = setInterval(async () => {\n        try {\n          const statusResponse = await axios.get(`/api/scenes/${scene.id}/status`);\n          const {\n            status,\n            progress\n          } = statusResponse.data;\n\n          // Use functional update to avoid stale closure\n          setUploadProgress(prev => progress || prev);\n          if (status === 'completed') {\n            clearInterval(interval);\n            setProcessingInterval(null);\n\n            // After processing is completed, detect landmarks automatically\n            try {\n              await axios.post(`/api/scenes/${scene.id}/detect_landmarks`);\n            } catch (landmarkError) {\n              console.error('Landmark detection failed:', landmarkError);\n            }\n\n            // Get updated scene data first\n            const sceneResponse = await axios.get(`/api/scenes/${scene.id}`);\n\n            // 使用批量状态更新避免DOM冲突\n            setTimeout(() => {\n              setScene(sceneResponse.data);\n              setProcessing(false);\n              setActiveStep(3);\n            }, 50);\n          } else if (status === 'error') {\n            clearInterval(interval);\n            setProcessingInterval(null);\n            setProcessing(false);\n            setError('Processing failed: ' + (statusResponse.data.error || 'Unknown error'));\n          }\n        } catch (err) {\n          console.error('Polling error:', err);\n        }\n      }, 2000);\n      setProcessingInterval(interval);\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      setError('Processing request failed: ' + (((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || err.message));\n      setProcessing(false);\n    }\n  }, [processing, stitcherType, processingMode, stitchMode, scene === null || scene === void 0 ? void 0 : scene.id]);\n\n  // Cleanup when unmounting\n  React.useEffect(() => {\n    return () => {\n      // Clear any object URLs to avoid memory leaks\n      files.forEach(file => {\n        if (file.preview) {\n          try {\n            URL.revokeObjectURL(file.preview);\n          } catch (e) {\n            // Ignore errors during cleanup\n          }\n        }\n      });\n    };\n  }, [files]);\n\n  // Separate effect for interval cleanup\n  React.useEffect(() => {\n    return () => {\n      if (processingInterval) {\n        clearInterval(processingInterval);\n      }\n    };\n  }, [processingInterval]);\n\n  // View the scene after processing\n  const handleViewScene = () => {\n    navigate(`/scenes/${scene.id}`);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Create New Scene\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Stepper, {\n      activeStep: activeStep,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Step, {\n        children: /*#__PURE__*/_jsxDEV(StepLabel, {\n          children: \"Create Scene\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Step, {\n        children: /*#__PURE__*/_jsxDEV(StepLabel, {\n          children: \"Upload Images\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Step, {\n        children: /*#__PURE__*/_jsxDEV(StepLabel, {\n          children: \"Processing Options\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Step, {\n        children: /*#__PURE__*/_jsxDEV(StepLabel, {\n          children: \"View Result\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 17\n    }, this), activeStep === 0 && /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 2,\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Scene Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        label: \"Scene Name\",\n        variant: \"outlined\",\n        fullWidth: true,\n        value: sceneName,\n        onChange: e => setSceneName(e.target.value),\n        margin: \"normal\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleCreateScene,\n          children: \"Create Scene\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 9\n    }, this), activeStep === 1 && /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 2,\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Upload Images\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"file\",\n        ref: fileInputRef,\n        onChange: handleFileSelect,\n        multiple: true,\n        accept: \"image/*\",\n        style: {\n          display: 'none'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        onClick: handleUploadAreaClick,\n        sx: {\n          border: '2px dashed #ccc',\n          borderRadius: 2,\n          p: 3,\n          mb: 2,\n          textAlign: 'center',\n          cursor: 'pointer',\n          '&:hover': {\n            bgcolor: 'rgba(63, 81, 181, 0.05)',\n            borderColor: 'primary.main'\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(CloudUploadIcon, {\n          sx: {\n            fontSize: 48,\n            color: 'primary.main',\n            mb: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          children: \"Click to select image files\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          children: \"Supports uploading multiple image files\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 11\n      }, this), files.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: [\"Selected \", files.length, \" files\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(ImageList, {\n          sx: {\n            height: 220\n          },\n          cols: 6,\n          children: files.map((file, index) => /*#__PURE__*/_jsxDEV(ImageListItem, {\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: file.preview,\n              alt: `Preview ${index}`,\n              loading: \"lazy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 21\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 13\n      }, this), uploading && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          gutterBottom: true,\n          children: [\"Upload Progress: \", uploadProgress, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n          variant: \"determinate\",\n          value: uploadProgress\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          onClick: () => setActiveStep(0),\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleUploadFiles,\n          disabled: files.length === 0 || uploading,\n          startIcon: uploading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 38\n          }, this) : null,\n          children: uploading ? 'Uploading...' : 'Upload Files'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 9\n    }, this), activeStep === 2 && /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 2,\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Processing Options\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n        component: \"fieldset\",\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(FormLabel, {\n          component: \"legend\",\n          children: \"Processing Mode\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n          value: processingMode,\n          onChange: e => setProcessingMode(e.target.value),\n          name: \"processing-mode-group\",\n          children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n            value: \"panorama\",\n            control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 26\n            }, this),\n            label: \"Panorama - Stitch all images into a single panorama\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            value: \"streetview\",\n            control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 26\n            }, this),\n            label: \"Street View Mode - Create a navigable panorama sequence\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 13\n        }, this)]\n      }, \"processing-mode\", true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 11\n      }, this), processingMode === 'streetview' && /*#__PURE__*/_jsxDEV(FormControl, {\n        component: \"fieldset\",\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(FormLabel, {\n          component: \"legend\",\n          children: \"\\u62FC\\u63A5\\u7B97\\u6CD5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n          value: stitcherType,\n          onChange: e => setStitcherType(e.target.value),\n          name: \"stitcher-type-group\",\n          children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n            value: \"anti_ghosting\",\n            control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 28\n            }, this),\n            label: \"\\u9632\\u91CD\\u5F71\\u62FC\\u63A5 - \\u9AD8\\u8D28\\u91CF\\u62FC\\u63A5\\uFF0C\\u51CF\\u5C11\\u91CD\\u5F71\\u73B0\\u8C61\\uFF08\\u63A8\\u8350\\uFF09\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            value: \"real\",\n            control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 28\n            }, this),\n            label: \"\\u6807\\u51C6\\u62FC\\u63A5 - \\u5FEB\\u901F\\u62FC\\u63A5\\u7B97\\u6CD5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 15\n        }, this)]\n      }, \"stitcher-type\", true, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 13\n      }, this), processing && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          gutterBottom: true,\n          children: [\"Processing Progress: \", uploadProgress, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n          variant: uploadProgress > 0 ? \"determinate\" : \"indeterminate\",\n          value: uploadProgress\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          onClick: () => setActiveStep(1),\n          disabled: processing,\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleProcess,\n          disabled: processing,\n          startIcon: processing ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 39\n          }, this) : null,\n          children: processing ? 'Processing...' : 'Start Processing'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 9\n    }, this), activeStep === 3 && scene && /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 2,\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Processing Complete\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"success\",\n        sx: {\n          mb: 3\n        },\n        children: \"Scene processing is complete! You can now view the results.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            children: [\"Scene Name: \", scene.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: [\"Type: \", scene.type === 'panorama' ? 'Panorama' : 'Street View Mode']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: [\"Created At: \", scene.created_at]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 429,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: handleViewScene,\n        color: \"primary\",\n        children: \"View Scene\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 443,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 422,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 219,\n    columnNumber: 5\n  }, this);\n};\nexport default CreateScene;", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useRef", "useNavigate", "axios", "Typography", "<PERSON><PERSON>", "Paper", "Box", "TextField", "CircularProgress", "LinearProgress", "Card", "<PERSON><PERSON><PERSON><PERSON>", "FormControl", "FormLabel", "RadioGroup", "Radio", "FormControlLabel", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "ImageList", "ImageListItem", "CloudUploadIcon", "jsxDEV", "_jsxDEV", "CreateScene", "navigate", "fileInputRef", "activeStep", "setActiveStep", "scene<PERSON><PERSON>", "setSceneName", "scene", "setScene", "files", "setFiles", "uploading", "setUploading", "uploadProgress", "setUploadProgress", "processingMode", "setProcessingMode", "stitcherType", "setStitcherType", "stitchMode", "processing", "setProcessing", "error", "setError", "processingInterval", "setProcessingInterval", "handleFileSelect", "event", "selectedFiles", "Array", "from", "target", "imageFiles", "filter", "file", "type", "startsWith", "map", "Object", "assign", "preview", "URL", "createObjectURL", "handleUploadAreaClick", "_fileInputRef$current", "current", "click", "handleCreateScene", "trim", "formData", "FormData", "append", "response", "post", "data", "err", "_err$response", "_err$response$data", "message", "handleUploadFiles", "length", "for<PERSON>ach", "id", "onUploadProgress", "progressEvent", "percentCompleted", "Math", "round", "loaded", "total", "_err$response2", "_err$response2$data", "handleProcess", "params", "stitcher_type", "stitch_mode", "mode", "interval", "setInterval", "statusResponse", "get", "status", "progress", "prev", "clearInterval", "landmarkError", "console", "sceneResponse", "setTimeout", "_err$response3", "_err$response3$data", "useEffect", "revokeObjectURL", "e", "handleViewScene", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "mb", "severity", "elevation", "p", "label", "fullWidth", "value", "onChange", "margin", "mt", "onClick", "ref", "multiple", "accept", "style", "display", "border", "borderRadius", "textAlign", "cursor", "bgcolor", "borderColor", "fontSize", "color", "height", "cols", "index", "src", "alt", "loading", "gap", "disabled", "startIcon", "size", "component", "name", "control", "created_at"], "sources": ["D:/Study_Code/2025-8-5/qrjy_images/frontend/src/pages/CreateScene.js"], "sourcesContent": ["import React, { useState, useCallback, useRef } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\n\nimport Typography from '@mui/material/Typography';\nimport Button from '@mui/material/Button';\nimport Paper from '@mui/material/Paper';\nimport Box from '@mui/material/Box';\nimport TextField from '@mui/material/TextField';\nimport CircularProgress from '@mui/material/CircularProgress';\nimport LinearProgress from '@mui/material/LinearProgress';\nimport Card from '@mui/material/Card';\nimport CardContent from '@mui/material/CardContent';\nimport FormControl from '@mui/material/FormControl';\nimport FormLabel from '@mui/material/FormLabel';\nimport RadioGroup from '@mui/material/RadioGroup';\nimport Radio from '@mui/material/Radio';\nimport FormControlLabel from '@mui/material/FormControlLabel';\nimport Stepper from '@mui/material/Stepper';\nimport Step from '@mui/material/Step';\nimport StepLabel from '@mui/material/StepLabel';\nimport Alert from '@mui/material/Alert';\nimport ImageList from '@mui/material/ImageList';\nimport ImageListItem from '@mui/material/ImageListItem';\nimport CloudUploadIcon from '@mui/icons-material/CloudUpload';\n\nconst CreateScene = () => {\n  const navigate = useNavigate();\n  const fileInputRef = useRef(null);\n  const [activeStep, setActiveStep] = useState(0);\n  const [sceneName, setSceneName] = useState('');\n  const [scene, setScene] = useState(null);\n  const [files, setFiles] = useState([]);\n  const [uploading, setUploading] = useState(false);\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [processingMode, setProcessingMode] = useState('panorama');\n  const [stitcherType, setStitcherType] = useState('anti_ghosting'); // 默认使用防重影算法\n  const stitchMode = 'single'; // 拼接模式：固定使用完整拼接（单张全景图）\n  const [processing, setProcessing] = useState(false);\n  const [error, setError] = useState('');\n  const [processingInterval, setProcessingInterval] = useState(null);\n\n  // Handle file selection\n  const handleFileSelect = useCallback((event) => {\n    const selectedFiles = Array.from(event.target.files);\n    // Filter for image files and add preview URLs\n    const imageFiles = selectedFiles.filter(file => file.type.startsWith('image/'));\n\n    setFiles(imageFiles.map(file =>\n      Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      })\n    ));\n  }, []);\n\n  // Handle click on upload area\n  const handleUploadAreaClick = () => {\n    fileInputRef.current?.click();\n  };\n\n  // Create a new scene\n  const handleCreateScene = async () => {\n    if (!sceneName.trim()) {\n      setError('Please enter a scene name');\n      return;\n    }\n\n    try {\n      const formData = new FormData();\n      formData.append('name', sceneName);\n\n      const response = await axios.post('/api/scenes', formData);\n      setScene(response.data);\n      setActiveStep(1);\n      setError('');\n    } catch (err) {\n      setError('Failed to create scene: ' + (err.response?.data?.message || err.message));\n    }\n  };\n\n  // Upload files to the scene\n  const handleUploadFiles = async () => {\n    if (files.length === 0) {\n      setError('Please select files first');\n      return;\n    }\n\n    setUploading(true);\n    setUploadProgress(0);\n    \n    try {\n      const formData = new FormData();\n      files.forEach(file => {\n        formData.append('files', file);\n      });\n\n      await axios.post(`/api/scenes/${scene.id}/upload`, formData, {\n        onUploadProgress: (progressEvent) => {\n          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);\n          setUploadProgress(percentCompleted);\n        }\n      });\n\n      setActiveStep(2);\n      setError('');\n    } catch (err) {\n      setError('Upload failed: ' + (err.response?.data?.message || err.message));\n    } finally {\n      setUploading(false);\n    }\n  };\n\n  // Process the uploaded images\n  const handleProcess = useCallback(async () => {\n    // 防止重复点击\n    if (processing) return;\n\n    setProcessing(true);\n    setError('');\n    setUploadProgress(0);\n\n    try {\n      // 构建处理参数\n      const params = {\n        stitcher_type: stitcherType\n      };\n\n      // 如果是 Street View 模式，添加相关参数\n      if (processingMode === 'streetview') {\n        // 添加拼接模式参数\n        params.stitch_mode = stitchMode;\n      }\n\n      // Start processing\n      await axios.post(`/api/scenes/${scene.id}/process`, {\n        mode: processingMode,\n        params: params\n      });\n\n      // Set up polling to check status\n      const interval = setInterval(async () => {\n        try {\n          const statusResponse = await axios.get(`/api/scenes/${scene.id}/status`);\n          const { status, progress } = statusResponse.data;\n\n          // Use functional update to avoid stale closure\n          setUploadProgress(prev => progress || prev);\n\n          if (status === 'completed') {\n            clearInterval(interval);\n            setProcessingInterval(null);\n\n            // After processing is completed, detect landmarks automatically\n            try {\n              await axios.post(`/api/scenes/${scene.id}/detect_landmarks`);\n            } catch (landmarkError) {\n              console.error('Landmark detection failed:', landmarkError);\n            }\n\n            // Get updated scene data first\n            const sceneResponse = await axios.get(`/api/scenes/${scene.id}`);\n\n            // 使用批量状态更新避免DOM冲突\n            setTimeout(() => {\n              setScene(sceneResponse.data);\n              setProcessing(false);\n              setActiveStep(3);\n            }, 50);\n\n          } else if (status === 'error') {\n            clearInterval(interval);\n            setProcessingInterval(null);\n            setProcessing(false);\n            setError('Processing failed: ' + (statusResponse.data.error || 'Unknown error'));\n          }\n        } catch (err) {\n          console.error('Polling error:', err);\n        }\n      }, 2000);\n\n      setProcessingInterval(interval);\n    } catch (err) {\n      setError('Processing request failed: ' + (err.response?.data?.message || err.message));\n      setProcessing(false);\n    }\n  }, [processing, stitcherType, processingMode, stitchMode, scene?.id]);\n\n  // Cleanup when unmounting\n  React.useEffect(() => {\n    return () => {\n      // Clear any object URLs to avoid memory leaks\n      files.forEach(file => {\n        if (file.preview) {\n          try {\n            URL.revokeObjectURL(file.preview);\n          } catch (e) {\n            // Ignore errors during cleanup\n          }\n        }\n      });\n    };\n  }, [files]);\n\n  // Separate effect for interval cleanup\n  React.useEffect(() => {\n    return () => {\n      if (processingInterval) {\n        clearInterval(processingInterval);\n      }\n    };\n  }, [processingInterval]);\n\n  // View the scene after processing\n  const handleViewScene = () => {\n    navigate(`/scenes/${scene.id}`);\n  };\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" gutterBottom>Create New Scene</Typography>\n      \n      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>\n        <Step>\n          <StepLabel>Create Scene</StepLabel>\n        </Step>\n        <Step>\n          <StepLabel>Upload Images</StepLabel>\n        </Step>\n        <Step>\n          <StepLabel>Processing Options</StepLabel>\n        </Step>\n        <Step>\n          <StepLabel>View Result</StepLabel>\n        </Step>\n      </Stepper>\n\n      {error && <Alert severity=\"error\" sx={{ mb: 2 }}>{error}</Alert>}\n      \n      {activeStep === 0 && (\n        <Paper elevation={2} sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>Scene Information</Typography>\n          <TextField\n            label=\"Scene Name\"\n            variant=\"outlined\"\n            fullWidth\n            value={sceneName}\n            onChange={(e) => setSceneName(e.target.value)}\n            margin=\"normal\"\n          />\n          <Box sx={{ mt: 2 }}>\n            <Button \n              variant=\"contained\" \n              onClick={handleCreateScene}\n            >\n              Create Scene\n            </Button>\n          </Box>\n        </Paper>\n      )}\n\n      {activeStep === 1 && (\n        <Paper elevation={2} sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>Upload Images</Typography>\n          <input\n            type=\"file\"\n            ref={fileInputRef}\n            onChange={handleFileSelect}\n            multiple\n            accept=\"image/*\"\n            style={{ display: 'none' }}\n          />\n          <Box\n            onClick={handleUploadAreaClick}\n            sx={{\n              border: '2px dashed #ccc',\n              borderRadius: 2,\n              p: 3,\n              mb: 2,\n              textAlign: 'center',\n              cursor: 'pointer',\n              '&:hover': {\n                bgcolor: 'rgba(63, 81, 181, 0.05)',\n                borderColor: 'primary.main'\n              }\n            }}\n          >\n            <CloudUploadIcon sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />\n            <Typography variant=\"body1\">\n              Click to select image files\n            </Typography>\n            <Typography variant=\"body2\" color=\"textSecondary\">\n              Supports uploading multiple image files\n            </Typography>\n          </Box>\n          \n          {files.length > 0 && (\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle1\" gutterBottom>\n                Selected {files.length} files\n              </Typography>\n              <ImageList sx={{ height: 220 }} cols={6}>\n                {files.map((file, index) => (\n                  <ImageListItem key={index}>\n                    <img\n                      src={file.preview}\n                      alt={`Preview ${index}`}\n                      loading=\"lazy\"\n                    />\n                  </ImageListItem>\n                ))}\n              </ImageList>\n            </Box>\n          )}\n\n          {uploading && (\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"body2\" gutterBottom>\n                Upload Progress: {uploadProgress}%\n              </Typography>\n              <LinearProgress variant=\"determinate\" value={uploadProgress} />\n            </Box>\n          )}\n          \n          <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>\n            <Button \n              variant=\"outlined\" \n              onClick={() => setActiveStep(0)}\n            >\n              Previous\n            </Button>\n            <Button \n              variant=\"contained\" \n              onClick={handleUploadFiles}\n              disabled={files.length === 0 || uploading}\n              startIcon={uploading ? <CircularProgress size={20} /> : null}\n            >\n              {uploading ? 'Uploading...' : 'Upload Files'}\n            </Button>\n          </Box>\n        </Paper>\n      )}\n\n      {activeStep === 2 && (\n        <Paper elevation={2} sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>Processing Options</Typography>\n          \n          <FormControl component=\"fieldset\" sx={{ mb: 3 }} key=\"processing-mode\">\n            <FormLabel component=\"legend\">Processing Mode</FormLabel>\n            <RadioGroup\n              value={processingMode}\n              onChange={(e) => setProcessingMode(e.target.value)}\n              name=\"processing-mode-group\"\n            >\n              <FormControlLabel\n                value=\"panorama\"\n                control={<Radio />}\n                label=\"Panorama - Stitch all images into a single panorama\"\n              />\n              <FormControlLabel\n                value=\"streetview\"\n                control={<Radio />}\n                label=\"Street View Mode - Create a navigable panorama sequence\"\n              />\n            </RadioGroup>\n          </FormControl>\n\n          {processingMode === 'streetview' && (\n            <FormControl component=\"fieldset\" sx={{ mb: 3 }} key=\"stitcher-type\">\n              <FormLabel component=\"legend\">拼接算法</FormLabel>\n              <RadioGroup\n                value={stitcherType}\n                onChange={(e) => setStitcherType(e.target.value)}\n                name=\"stitcher-type-group\"\n              >\n                <FormControlLabel\n                  value=\"anti_ghosting\"\n                  control={<Radio />}\n                  label=\"防重影拼接 - 高质量拼接，减少重影现象（推荐）\"\n                />\n                <FormControlLabel\n                  value=\"real\"\n                  control={<Radio />}\n                  label=\"标准拼接 - 快速拼接算法\"\n                />\n              </RadioGroup>\n            </FormControl>\n          )}\n\n          {processing && (\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"body2\" gutterBottom>\n                Processing Progress: {uploadProgress}%\n              </Typography>\n              <LinearProgress \n                variant={uploadProgress > 0 ? \"determinate\" : \"indeterminate\"} \n                value={uploadProgress} \n              />\n            </Box>\n          )}\n          \n          <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>\n            <Button \n              variant=\"outlined\" \n              onClick={() => setActiveStep(1)}\n              disabled={processing}\n            >\n              Previous\n            </Button>\n            <Button \n              variant=\"contained\" \n              onClick={handleProcess}\n              disabled={processing}\n              startIcon={processing ? <CircularProgress size={20} /> : null}\n            >\n              {processing ? 'Processing...' : 'Start Processing'}\n            </Button>\n          </Box>\n        </Paper>\n      )}\n\n      {activeStep === 3 && scene && (\n        <Paper elevation={2} sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>Processing Complete</Typography>\n          \n          <Alert severity=\"success\" sx={{ mb: 3 }}>\n            Scene processing is complete! You can now view the results.\n          </Alert>\n          \n          <Card sx={{ mb: 3 }}>\n            <CardContent>\n              <Typography variant=\"subtitle1\">\n                Scene Name: {scene.name}\n              </Typography>\n              <Typography variant=\"body2\" color=\"textSecondary\">\n                Type: {scene.type === 'panorama' ? 'Panorama' : 'Street View Mode'}\n              </Typography>\n              <Typography variant=\"body2\" color=\"textSecondary\">\n                Created At: {scene.created_at}\n              </Typography>\n            </CardContent>\n          </Card>\n          \n          <Button \n            variant=\"contained\" \n            onClick={handleViewScene}\n            color=\"primary\"\n          >\n            View Scene\n          </Button>\n        </Paper>\n      )}\n    </Box>\n  );\n};\n\nexport default CreateScene; "], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AAC5D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AAEzB,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,GAAG,MAAM,mBAAmB;AACnC,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,eAAe,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,WAAW,GAAGA,CAAA,KAAM;EACxB,MAAMC,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM2B,YAAY,GAAG5B,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmC,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqC,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC2C,cAAc,EAAEC,iBAAiB,CAAC,GAAG5C,QAAQ,CAAC,UAAU,CAAC;EAChE,MAAM,CAAC6C,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC;EACnE,MAAM+C,UAAU,GAAG,QAAQ,CAAC,CAAC;EAC7B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACkD,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;;EAElE;EACA,MAAMsD,gBAAgB,GAAGrD,WAAW,CAAEsD,KAAK,IAAK;IAC9C,MAAMC,aAAa,GAAGC,KAAK,CAACC,IAAI,CAACH,KAAK,CAACI,MAAM,CAACtB,KAAK,CAAC;IACpD;IACA,MAAMuB,UAAU,GAAGJ,aAAa,CAACK,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,CAAC;IAE/E1B,QAAQ,CAACsB,UAAU,CAACK,GAAG,CAACH,IAAI,IAC1BI,MAAM,CAACC,MAAM,CAACL,IAAI,EAAE;MAClBM,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACR,IAAI;IACnC,CAAC,CACH,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMS,qBAAqB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAClC,CAAAA,qBAAA,GAAA1C,YAAY,CAAC2C,OAAO,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBE,KAAK,CAAC,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC1C,SAAS,CAAC2C,IAAI,CAAC,CAAC,EAAE;MACrBzB,QAAQ,CAAC,2BAA2B,CAAC;MACrC;IACF;IAEA,IAAI;MACF,MAAM0B,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE9C,SAAS,CAAC;MAElC,MAAM+C,QAAQ,GAAG,MAAM5E,KAAK,CAAC6E,IAAI,CAAC,aAAa,EAAEJ,QAAQ,CAAC;MAC1DzC,QAAQ,CAAC4C,QAAQ,CAACE,IAAI,CAAC;MACvBlD,aAAa,CAAC,CAAC,CAAC;MAChBmB,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOgC,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZlC,QAAQ,CAAC,0BAA0B,IAAI,EAAAiC,aAAA,GAAAD,GAAG,CAACH,QAAQ,cAAAI,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcF,IAAI,cAAAG,kBAAA,uBAAlBA,kBAAA,CAAoBC,OAAO,KAAIH,GAAG,CAACG,OAAO,CAAC,CAAC;IACrF;EACF,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAIlD,KAAK,CAACmD,MAAM,KAAK,CAAC,EAAE;MACtBrC,QAAQ,CAAC,2BAA2B,CAAC;MACrC;IACF;IAEAX,YAAY,CAAC,IAAI,CAAC;IAClBE,iBAAiB,CAAC,CAAC,CAAC;IAEpB,IAAI;MACF,MAAMmC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BzC,KAAK,CAACoD,OAAO,CAAC3B,IAAI,IAAI;QACpBe,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEjB,IAAI,CAAC;MAChC,CAAC,CAAC;MAEF,MAAM1D,KAAK,CAAC6E,IAAI,CAAC,eAAe9C,KAAK,CAACuD,EAAE,SAAS,EAAEb,QAAQ,EAAE;QAC3Dc,gBAAgB,EAAGC,aAAa,IAAK;UACnC,MAAMC,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAAEH,aAAa,CAACI,MAAM,GAAG,GAAG,GAAIJ,aAAa,CAACK,KAAK,CAAC;UACvFvD,iBAAiB,CAACmD,gBAAgB,CAAC;QACrC;MACF,CAAC,CAAC;MAEF7D,aAAa,CAAC,CAAC,CAAC;MAChBmB,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOgC,GAAG,EAAE;MAAA,IAAAe,cAAA,EAAAC,mBAAA;MACZhD,QAAQ,CAAC,iBAAiB,IAAI,EAAA+C,cAAA,GAAAf,GAAG,CAACH,QAAQ,cAAAkB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAchB,IAAI,cAAAiB,mBAAA,uBAAlBA,mBAAA,CAAoBb,OAAO,KAAIH,GAAG,CAACG,OAAO,CAAC,CAAC;IAC5E,CAAC,SAAS;MACR9C,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM4D,aAAa,GAAGnG,WAAW,CAAC,YAAY;IAC5C;IACA,IAAI+C,UAAU,EAAE;IAEhBC,aAAa,CAAC,IAAI,CAAC;IACnBE,QAAQ,CAAC,EAAE,CAAC;IACZT,iBAAiB,CAAC,CAAC,CAAC;IAEpB,IAAI;MACF;MACA,MAAM2D,MAAM,GAAG;QACbC,aAAa,EAAEzD;MACjB,CAAC;;MAED;MACA,IAAIF,cAAc,KAAK,YAAY,EAAE;QACnC;QACA0D,MAAM,CAACE,WAAW,GAAGxD,UAAU;MACjC;;MAEA;MACA,MAAM3C,KAAK,CAAC6E,IAAI,CAAC,eAAe9C,KAAK,CAACuD,EAAE,UAAU,EAAE;QAClDc,IAAI,EAAE7D,cAAc;QACpB0D,MAAM,EAAEA;MACV,CAAC,CAAC;;MAEF;MACA,MAAMI,QAAQ,GAAGC,WAAW,CAAC,YAAY;QACvC,IAAI;UACF,MAAMC,cAAc,GAAG,MAAMvG,KAAK,CAACwG,GAAG,CAAC,eAAezE,KAAK,CAACuD,EAAE,SAAS,CAAC;UACxE,MAAM;YAAEmB,MAAM;YAAEC;UAAS,CAAC,GAAGH,cAAc,CAACzB,IAAI;;UAEhD;UACAxC,iBAAiB,CAACqE,IAAI,IAAID,QAAQ,IAAIC,IAAI,CAAC;UAE3C,IAAIF,MAAM,KAAK,WAAW,EAAE;YAC1BG,aAAa,CAACP,QAAQ,CAAC;YACvBpD,qBAAqB,CAAC,IAAI,CAAC;;YAE3B;YACA,IAAI;cACF,MAAMjD,KAAK,CAAC6E,IAAI,CAAC,eAAe9C,KAAK,CAACuD,EAAE,mBAAmB,CAAC;YAC9D,CAAC,CAAC,OAAOuB,aAAa,EAAE;cACtBC,OAAO,CAAChE,KAAK,CAAC,4BAA4B,EAAE+D,aAAa,CAAC;YAC5D;;YAEA;YACA,MAAME,aAAa,GAAG,MAAM/G,KAAK,CAACwG,GAAG,CAAC,eAAezE,KAAK,CAACuD,EAAE,EAAE,CAAC;;YAEhE;YACA0B,UAAU,CAAC,MAAM;cACfhF,QAAQ,CAAC+E,aAAa,CAACjC,IAAI,CAAC;cAC5BjC,aAAa,CAAC,KAAK,CAAC;cACpBjB,aAAa,CAAC,CAAC,CAAC;YAClB,CAAC,EAAE,EAAE,CAAC;UAER,CAAC,MAAM,IAAI6E,MAAM,KAAK,OAAO,EAAE;YAC7BG,aAAa,CAACP,QAAQ,CAAC;YACvBpD,qBAAqB,CAAC,IAAI,CAAC;YAC3BJ,aAAa,CAAC,KAAK,CAAC;YACpBE,QAAQ,CAAC,qBAAqB,IAAIwD,cAAc,CAACzB,IAAI,CAAChC,KAAK,IAAI,eAAe,CAAC,CAAC;UAClF;QACF,CAAC,CAAC,OAAOiC,GAAG,EAAE;UACZ+B,OAAO,CAAChE,KAAK,CAAC,gBAAgB,EAAEiC,GAAG,CAAC;QACtC;MACF,CAAC,EAAE,IAAI,CAAC;MAER9B,qBAAqB,CAACoD,QAAQ,CAAC;IACjC,CAAC,CAAC,OAAOtB,GAAG,EAAE;MAAA,IAAAkC,cAAA,EAAAC,mBAAA;MACZnE,QAAQ,CAAC,6BAA6B,IAAI,EAAAkE,cAAA,GAAAlC,GAAG,CAACH,QAAQ,cAAAqC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcnC,IAAI,cAAAoC,mBAAA,uBAAlBA,mBAAA,CAAoBhC,OAAO,KAAIH,GAAG,CAACG,OAAO,CAAC,CAAC;MACtFrC,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CAACD,UAAU,EAAEH,YAAY,EAAEF,cAAc,EAAEI,UAAU,EAAEZ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEuD,EAAE,CAAC,CAAC;;EAErE;EACA3F,KAAK,CAACwH,SAAS,CAAC,MAAM;IACpB,OAAO,MAAM;MACX;MACAlF,KAAK,CAACoD,OAAO,CAAC3B,IAAI,IAAI;QACpB,IAAIA,IAAI,CAACM,OAAO,EAAE;UAChB,IAAI;YACFC,GAAG,CAACmD,eAAe,CAAC1D,IAAI,CAACM,OAAO,CAAC;UACnC,CAAC,CAAC,OAAOqD,CAAC,EAAE;YACV;UAAA;QAEJ;MACF,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,CAACpF,KAAK,CAAC,CAAC;;EAEX;EACAtC,KAAK,CAACwH,SAAS,CAAC,MAAM;IACpB,OAAO,MAAM;MACX,IAAInE,kBAAkB,EAAE;QACtB4D,aAAa,CAAC5D,kBAAkB,CAAC;MACnC;IACF,CAAC;EACH,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;;EAExB;EACA,MAAMsE,eAAe,GAAGA,CAAA,KAAM;IAC5B7F,QAAQ,CAAC,WAAWM,KAAK,CAACuD,EAAE,EAAE,CAAC;EACjC,CAAC;EAED,oBACE/D,OAAA,CAACnB,GAAG;IAAAmH,QAAA,gBACFhG,OAAA,CAACtB,UAAU;MAACuH,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAAgB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEnEtG,OAAA,CAACR,OAAO;MAACY,UAAU,EAAEA,UAAW;MAACmG,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,gBAC7ChG,OAAA,CAACP,IAAI;QAAAuG,QAAA,eACHhG,OAAA,CAACN,SAAS;UAAAsG,QAAA,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eACPtG,OAAA,CAACP,IAAI;QAAAuG,QAAA,eACHhG,OAAA,CAACN,SAAS;UAAAsG,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eACPtG,OAAA,CAACP,IAAI;QAAAuG,QAAA,eACHhG,OAAA,CAACN,SAAS;UAAAsG,QAAA,EAAC;QAAkB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACPtG,OAAA,CAACP,IAAI;QAAAuG,QAAA,eACHhG,OAAA,CAACN,SAAS;UAAAsG,QAAA,EAAC;QAAW;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAET/E,KAAK,iBAAIvB,OAAA,CAACL,KAAK;MAAC8G,QAAQ,EAAC,OAAO;MAACF,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,EAAEzE;IAAK;MAAA4E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAE/DlG,UAAU,KAAK,CAAC,iBACfJ,OAAA,CAACpB,KAAK;MAAC8H,SAAS,EAAE,CAAE;MAACH,EAAE,EAAE;QAAEI,CAAC,EAAE;MAAE,CAAE;MAAAX,QAAA,gBAChChG,OAAA,CAACtB,UAAU;QAACuH,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACpEtG,OAAA,CAAClB,SAAS;QACR8H,KAAK,EAAC,YAAY;QAClBX,OAAO,EAAC,UAAU;QAClBY,SAAS;QACTC,KAAK,EAAExG,SAAU;QACjByG,QAAQ,EAAGjB,CAAC,IAAKvF,YAAY,CAACuF,CAAC,CAAC9D,MAAM,CAAC8E,KAAK,CAAE;QAC9CE,MAAM,EAAC;MAAQ;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACFtG,OAAA,CAACnB,GAAG;QAAC0H,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAE,CAAE;QAAAjB,QAAA,eACjBhG,OAAA,CAACrB,MAAM;UACLsH,OAAO,EAAC,WAAW;UACnBiB,OAAO,EAAElE,iBAAkB;UAAAgD,QAAA,EAC5B;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEAlG,UAAU,KAAK,CAAC,iBACfJ,OAAA,CAACpB,KAAK;MAAC8H,SAAS,EAAE,CAAE;MAACH,EAAE,EAAE;QAAEI,CAAC,EAAE;MAAE,CAAE;MAAAX,QAAA,gBAChChG,OAAA,CAACtB,UAAU;QAACuH,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAAa;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAChEtG,OAAA;QACEoC,IAAI,EAAC,MAAM;QACX+E,GAAG,EAAEhH,YAAa;QAClB4G,QAAQ,EAAEpF,gBAAiB;QAC3ByF,QAAQ;QACRC,MAAM,EAAC,SAAS;QAChBC,KAAK,EAAE;UAAEC,OAAO,EAAE;QAAO;MAAE;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACFtG,OAAA,CAACnB,GAAG;QACFqI,OAAO,EAAEtE,qBAAsB;QAC/B2D,EAAE,EAAE;UACFiB,MAAM,EAAE,iBAAiB;UACzBC,YAAY,EAAE,CAAC;UACfd,CAAC,EAAE,CAAC;UACJH,EAAE,EAAE,CAAC;UACLkB,SAAS,EAAE,QAAQ;UACnBC,MAAM,EAAE,SAAS;UACjB,SAAS,EAAE;YACTC,OAAO,EAAE,yBAAyB;YAClCC,WAAW,EAAE;UACf;QACF,CAAE;QAAA7B,QAAA,gBAEFhG,OAAA,CAACF,eAAe;UAACyG,EAAE,EAAE;YAAEuB,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE,cAAc;YAAEvB,EAAE,EAAE;UAAE;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvEtG,OAAA,CAACtB,UAAU;UAACuH,OAAO,EAAC,OAAO;UAAAD,QAAA,EAAC;QAE5B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbtG,OAAA,CAACtB,UAAU;UAACuH,OAAO,EAAC,OAAO;UAAC8B,KAAK,EAAC,eAAe;UAAA/B,QAAA,EAAC;QAElD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EAEL5F,KAAK,CAACmD,MAAM,GAAG,CAAC,iBACf7D,OAAA,CAACnB,GAAG;QAAC0H,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,gBACjBhG,OAAA,CAACtB,UAAU;UAACuH,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,GAAC,WAClC,EAACtF,KAAK,CAACmD,MAAM,EAAC,QACzB;QAAA;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbtG,OAAA,CAACJ,SAAS;UAAC2G,EAAE,EAAE;YAAEyB,MAAM,EAAE;UAAI,CAAE;UAACC,IAAI,EAAE,CAAE;UAAAjC,QAAA,EACrCtF,KAAK,CAAC4B,GAAG,CAAC,CAACH,IAAI,EAAE+F,KAAK,kBACrBlI,OAAA,CAACH,aAAa;YAAAmG,QAAA,eACZhG,OAAA;cACEmI,GAAG,EAAEhG,IAAI,CAACM,OAAQ;cAClB2F,GAAG,EAAE,WAAWF,KAAK,EAAG;cACxBG,OAAO,EAAC;YAAM;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf;UAAC,GALgB4B,KAAK;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMV,CAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CACN,EAEA1F,SAAS,iBACRZ,OAAA,CAACnB,GAAG;QAAC0H,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,gBACjBhG,OAAA,CAACtB,UAAU;UAACuH,OAAO,EAAC,OAAO;UAACC,YAAY;UAAAF,QAAA,GAAC,mBACtB,EAAClF,cAAc,EAAC,GACnC;QAAA;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbtG,OAAA,CAAChB,cAAc;UAACiH,OAAO,EAAC,aAAa;UAACa,KAAK,EAAEhG;QAAe;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CACN,eAEDtG,OAAA,CAACnB,GAAG;QAAC0H,EAAE,EAAE;UAAEU,EAAE,EAAE,CAAC;UAAEM,OAAO,EAAE,MAAM;UAAEe,GAAG,EAAE;QAAE,CAAE;QAAAtC,QAAA,gBAC1ChG,OAAA,CAACrB,MAAM;UACLsH,OAAO,EAAC,UAAU;UAClBiB,OAAO,EAAEA,CAAA,KAAM7G,aAAa,CAAC,CAAC,CAAE;UAAA2F,QAAA,EACjC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtG,OAAA,CAACrB,MAAM;UACLsH,OAAO,EAAC,WAAW;UACnBiB,OAAO,EAAEtD,iBAAkB;UAC3B2E,QAAQ,EAAE7H,KAAK,CAACmD,MAAM,KAAK,CAAC,IAAIjD,SAAU;UAC1C4H,SAAS,EAAE5H,SAAS,gBAAGZ,OAAA,CAACjB,gBAAgB;YAAC0J,IAAI,EAAE;UAAG;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG,IAAK;UAAAN,QAAA,EAE5DpF,SAAS,GAAG,cAAc,GAAG;QAAc;UAAAuF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEAlG,UAAU,KAAK,CAAC,iBACfJ,OAAA,CAACpB,KAAK;MAAC8H,SAAS,EAAE,CAAE;MAACH,EAAE,EAAE;QAAEI,CAAC,EAAE;MAAE,CAAE;MAAAX,QAAA,gBAChChG,OAAA,CAACtB,UAAU;QAACuH,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAAkB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAErEtG,OAAA,CAACb,WAAW;QAACuJ,SAAS,EAAC,UAAU;QAACnC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAC9ChG,OAAA,CAACZ,SAAS;UAACsJ,SAAS,EAAC,QAAQ;UAAA1C,QAAA,EAAC;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACzDtG,OAAA,CAACX,UAAU;UACTyH,KAAK,EAAE9F,cAAe;UACtB+F,QAAQ,EAAGjB,CAAC,IAAK7E,iBAAiB,CAAC6E,CAAC,CAAC9D,MAAM,CAAC8E,KAAK,CAAE;UACnD6B,IAAI,EAAC,uBAAuB;UAAA3C,QAAA,gBAE5BhG,OAAA,CAACT,gBAAgB;YACfuH,KAAK,EAAC,UAAU;YAChB8B,OAAO,eAAE5I,OAAA,CAACV,KAAK;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnBM,KAAK,EAAC;UAAqD;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eACFtG,OAAA,CAACT,gBAAgB;YACfuH,KAAK,EAAC,YAAY;YAClB8B,OAAO,eAAE5I,OAAA,CAACV,KAAK;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnBM,KAAK,EAAC;UAAyD;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA,GAjBsC,iBAAiB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAkBzD,CAAC,EAEbtF,cAAc,KAAK,YAAY,iBAC9BhB,OAAA,CAACb,WAAW;QAACuJ,SAAS,EAAC,UAAU;QAACnC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAC9ChG,OAAA,CAACZ,SAAS;UAACsJ,SAAS,EAAC,QAAQ;UAAA1C,QAAA,EAAC;QAAI;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAC9CtG,OAAA,CAACX,UAAU;UACTyH,KAAK,EAAE5F,YAAa;UACpB6F,QAAQ,EAAGjB,CAAC,IAAK3E,eAAe,CAAC2E,CAAC,CAAC9D,MAAM,CAAC8E,KAAK,CAAE;UACjD6B,IAAI,EAAC,qBAAqB;UAAA3C,QAAA,gBAE1BhG,OAAA,CAACT,gBAAgB;YACfuH,KAAK,EAAC,eAAe;YACrB8B,OAAO,eAAE5I,OAAA,CAACV,KAAK;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnBM,KAAK,EAAC;UAA0B;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACFtG,OAAA,CAACT,gBAAgB;YACfuH,KAAK,EAAC,MAAM;YACZ8B,OAAO,eAAE5I,OAAA,CAACV,KAAK;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnBM,KAAK,EAAC;UAAe;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA,GAjBsC,eAAe;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAkBvD,CACd,EAEAjF,UAAU,iBACTrB,OAAA,CAACnB,GAAG;QAAC0H,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,gBACjBhG,OAAA,CAACtB,UAAU;UAACuH,OAAO,EAAC,OAAO;UAACC,YAAY;UAAAF,QAAA,GAAC,uBAClB,EAAClF,cAAc,EAAC,GACvC;QAAA;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbtG,OAAA,CAAChB,cAAc;UACbiH,OAAO,EAAEnF,cAAc,GAAG,CAAC,GAAG,aAAa,GAAG,eAAgB;UAC9DgG,KAAK,EAAEhG;QAAe;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,eAEDtG,OAAA,CAACnB,GAAG;QAAC0H,EAAE,EAAE;UAAEU,EAAE,EAAE,CAAC;UAAEM,OAAO,EAAE,MAAM;UAAEe,GAAG,EAAE;QAAE,CAAE;QAAAtC,QAAA,gBAC1ChG,OAAA,CAACrB,MAAM;UACLsH,OAAO,EAAC,UAAU;UAClBiB,OAAO,EAAEA,CAAA,KAAM7G,aAAa,CAAC,CAAC,CAAE;UAChCkI,QAAQ,EAAElH,UAAW;UAAA2E,QAAA,EACtB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtG,OAAA,CAACrB,MAAM;UACLsH,OAAO,EAAC,WAAW;UACnBiB,OAAO,EAAEzC,aAAc;UACvB8D,QAAQ,EAAElH,UAAW;UACrBmH,SAAS,EAAEnH,UAAU,gBAAGrB,OAAA,CAACjB,gBAAgB;YAAC0J,IAAI,EAAE;UAAG;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG,IAAK;UAAAN,QAAA,EAE7D3E,UAAU,GAAG,eAAe,GAAG;QAAkB;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEAlG,UAAU,KAAK,CAAC,IAAII,KAAK,iBACxBR,OAAA,CAACpB,KAAK;MAAC8H,SAAS,EAAE,CAAE;MAACH,EAAE,EAAE;QAAEI,CAAC,EAAE;MAAE,CAAE;MAAAX,QAAA,gBAChChG,OAAA,CAACtB,UAAU;QAACuH,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAAmB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEtEtG,OAAA,CAACL,KAAK;QAAC8G,QAAQ,EAAC,SAAS;QAACF,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,EAAC;MAEzC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAERtG,OAAA,CAACf,IAAI;QAACsH,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,eAClBhG,OAAA,CAACd,WAAW;UAAA8G,QAAA,gBACVhG,OAAA,CAACtB,UAAU;YAACuH,OAAO,EAAC,WAAW;YAAAD,QAAA,GAAC,cAClB,EAACxF,KAAK,CAACmI,IAAI;UAAA;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACbtG,OAAA,CAACtB,UAAU;YAACuH,OAAO,EAAC,OAAO;YAAC8B,KAAK,EAAC,eAAe;YAAA/B,QAAA,GAAC,QAC1C,EAACxF,KAAK,CAAC4B,IAAI,KAAK,UAAU,GAAG,UAAU,GAAG,kBAAkB;UAAA;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACbtG,OAAA,CAACtB,UAAU;YAACuH,OAAO,EAAC,OAAO;YAAC8B,KAAK,EAAC,eAAe;YAAA/B,QAAA,GAAC,cACpC,EAACxF,KAAK,CAACqI,UAAU;UAAA;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEPtG,OAAA,CAACrB,MAAM;QACLsH,OAAO,EAAC,WAAW;QACnBiB,OAAO,EAAEnB,eAAgB;QACzBgC,KAAK,EAAC,SAAS;QAAA/B,QAAA,EAChB;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAED,eAAerG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}