import cv2
import numpy as np
import os
from tqdm import tqdm
from .utils import load_images, save_image, debug_stitching, save_debug_image
from .utils import enhance_image_quality, enhance_image_contrast, match_histograms, correct_exposure_differences, optimize_image_order

class Stitcher:
    def __init__(self, detector='sift'):
        """
        Initialize panorama stitcher with selected feature detector
        
        Args:
            detector (str): Feature detector to use ('sift', 'orb', 'akaze')
        """
        self.detector_type = detector
        
        # Initialize feature detectors
        self.sift = cv2.SIFT_create(nfeatures=10000, contrastThreshold=0.04)
        self.orb = cv2.ORB_create(nfeatures=10000, scaleFactor=1.2, WTA_K=3, scoreType=cv2.ORB_HARRIS_SCORE)
        self.akaze = cv2.AKAZE_create()
        
        # Initialize matchers
        FLANN_INDEX_KDTREE = 1
        index_params = dict(algorithm=FLANN_INDEX_KDTREE, trees=5)
        search_params = dict(checks=100)  # 增加检查次数以提高匹配质量
        self.flann_matcher = cv2.FlannBasedMatcher(index_params, search_params)
        
        self.bf_matcher = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=False)
    
    def detect_and_compute(self, image, detector_type=None):
        """
        Detect keypoints and compute descriptors using specified detector
        
        Args:
            image: Input image
            detector_type: Type of detector to use ('sift', 'orb', 'akaze')
            
        Returns:
            tuple: (keypoints, descriptors)
        """
        if detector_type is None:
            detector_type = self.detector_type
            
        # Convert to grayscale if needed
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
            
        # Apply contrast enhancement
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        gray = clahe.apply(gray)
        
        # Detect keypoints and compute descriptors
        if detector_type.lower() == 'sift':
            keypoints, descriptors = self.sift.detectAndCompute(gray, None)
        elif detector_type.lower() == 'orb':
            keypoints, descriptors = self.orb.detectAndCompute(gray, None)
        elif detector_type.lower() == 'akaze':
            keypoints, descriptors = self.akaze.detectAndCompute(gray, None)
        else:
            # Default to SIFT
            keypoints, descriptors = self.sift.detectAndCompute(gray, None)
        
        return keypoints, descriptors
    
    def match_features(self, desc1, desc2, ratio=0.75, detector_type=None):
        """
        Match features between two images using ratio test
        
        Args:
            desc1: Descriptors from first image
            desc2: Descriptors from second image
            ratio: Lowe's ratio test threshold
            detector_type: Type of detector used ('sift', 'orb', 'akaze')
            
        Returns:
            list: Good matches passing the ratio test
        """
        if detector_type is None:
            detector_type = self.detector_type
            
        # Select appropriate matcher based on detector type
        if detector_type.lower() == 'sift':
            matcher = self.flann_matcher
        else:
            matcher = self.bf_matcher
            
        # Get matches
        try:
            matches = matcher.knnMatch(desc1, desc2, k=2)
            
            # Apply Lowe's ratio test
            good_matches = []
            for m_n in matches:
                # 确保匹配结果包含两个元素
                if len(m_n) == 2:
                    m, n = m_n
                    if m.distance < ratio * n.distance:
                        good_matches.append(m)
            
            return good_matches
        except Exception as e:
            print(f"Error matching features: {str(e)}")
            return []
    
    def stitch_pair(self, img1, img2, confidence=0.5, debug=False):
        """
        Stitch two images together with enhanced quality
        
        Args:
            img1: First image
            img2: Second image
            confidence: Confidence threshold for homography
            debug: Whether to save debug images
            
        Returns:
            numpy.ndarray: Stitched panorama image, None if stitching failed
        """
        # Make a copy of the images to preserve originals
        img1_copy = img1.copy()
        img2_copy = img2.copy()
        
        # 预处理图像以提高拼接质量
        img1_copy = enhance_image_quality(img1_copy)
        img2_copy = enhance_image_quality(img2_copy)
        
        # 匹配图像直方图以保持一致的色彩
        img2_copy = match_histograms(img2_copy, img1_copy)
        
        # 尝试多种特征检测器，选择最佳结果
        detectors = ['sift', 'akaze', 'orb']
        best_result = None
        best_inlier_count = 0
        
        for detector in detectors:
            print(f"尝试使用 {detector} 检测器...")
            
            # Detect features
            kp1, desc1 = self.detect_and_compute(img1_copy, detector)
            kp2, desc2 = self.detect_and_compute(img2_copy, detector)
            
            if desc1 is None or desc2 is None or len(kp1) < 4 or len(kp2) < 4:
                print(f"Not enough keypoints found with {detector}")
                print(f"Image 1: {len(kp1) if kp1 else 0} keypoints")
                print(f"Image 2: {len(kp2) if kp2 else 0} keypoints")
                continue
            
            # Match features
            matches = self.match_features(desc1, desc2, ratio=0.7, detector_type=detector)
            
            if len(matches) < 4:
                print(f"Not enough matches found with {detector}: {len(matches)}")
                continue
                
            # Extract matched keypoints
            src_pts = np.float32([kp1[m.queryIdx].pt for m in matches]).reshape(-1, 1, 2)
            dst_pts = np.float32([kp2[m.trainIdx].pt for m in matches]).reshape(-1, 1, 2)
            
            # Find homography
            H, mask = cv2.findHomography(src_pts, dst_pts, cv2.RANSAC, 4.0, maxIters=10000, confidence=0.995)
            
            if H is None:
                print(f"Failed to find homography with {detector}")
                continue
                
            # Count inliers
            inlier_count = np.sum(mask)
            inlier_ratio = inlier_count / len(matches)
            
            print(f"{detector} found {inlier_count} inliers ({inlier_ratio:.2f})")
            
            if inlier_count > best_inlier_count:
                best_inlier_count = inlier_count
                best_result = {
                    'H': H,
                    'kp1': kp1,
                    'kp2': kp2,
                    'matches': matches,
                    'mask': mask,
                    'detector': detector
                }
                
        # If no detector worked, return None
        if best_result is None:
            print("Failed to stitch images with any detector")
            return None
            
        # Use the best result
        H = best_result['H']
        kp1 = best_result['kp1']
        kp2 = best_result['kp2']
        matches = best_result['matches']
        mask = best_result['mask']
        
        print(f"Using {best_result['detector']} for stitching with {best_inlier_count} inliers")
        
        try:
            # Get image dimensions
            h1, w1 = img1_copy.shape[:2]
            h2, w2 = img2_copy.shape[:2]
            
            # 参考示例代码中的方法：直接将img_b放在左侧，将img_a变换后拼接
            # 将图像2（左图）放在左侧，图像1（右图）通过单应性变换拼接到右侧
            result_width = w1 + w2
            result_height = max(h1, h2)
            
            # 对图像1应用透视变换 - 使用更高质量的插值
            warped = cv2.warpPerspective(img1_copy, H, (result_width, result_height), 
                                        flags=cv2.INTER_LANCZOS4 + cv2.WARP_FILL_OUTLIERS)
            
            # 将图像2放在结果图像的左侧
            warped[0:h2, 0:w2] = img2_copy
            
            # 创建混合区域以实现无缝过渡
            blend_width = 50
            mask = np.zeros((result_height, result_width), dtype=np.uint8)
            mask[0:h2, 0:w2] = 255
            
            # 创建混合掩码
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (blend_width, blend_width))
            blend_mask = cv2.erode(mask, kernel)
            blend_mask = cv2.GaussianBlur(blend_mask, (blend_width*2+1, blend_width*2+1), 0)
            
            # 将混合掩码归一化为[0,1]
            blend_mask = blend_mask.astype(np.float32) / 255.0
            blend_mask = np.dstack([blend_mask, blend_mask, blend_mask])
            
            # 创建一个包含左图的全尺寸图像
            img_with_2 = np.zeros_like(warped)
            img_with_2[0:h2, 0:w2] = img2_copy
            
            # 根据混合掩码混合两个图像
            warped = img_with_2 * blend_mask + warped * (1 - blend_mask)
            warped = warped.astype(np.uint8)
            
            # 裁剪黑色边框
            # 找到所有非黑色像素
            gray = cv2.cvtColor(warped, cv2.COLOR_BGR2GRAY)
            _, thresh = cv2.threshold(gray, 1, 255, cv2.THRESH_BINARY)
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if contours:
                # 找到所有轮廓的边界矩形
                all_contours = np.vstack([contours[i] for i in range(len(contours))])
                x, y, w, h = cv2.boundingRect(all_contours)
                
                # 添加小边框
                border = 5
                x = max(0, x - border)
                y = max(0, y - border)
                w = min(warped.shape[1] - x, w + 2*border)
                h = min(warped.shape[0] - y, h + 2*border)
                
                # 裁剪图像以去除黑色边框
                result = warped[y:y+h, x:x+w]
            else:
                result = warped
            
            # 后处理以提高图像质量
            # 1. 应用锐化滤波器增强细节
            kernel = np.array([[0, -1, 0],
                              [-1, 5, -1],
                              [0, -1, 0]])
            result = cv2.filter2D(result, -1, kernel)
            
            # 2. 使用CLAHE增强对比度而不引入过多噪点
            result = enhance_image_contrast(result, clip_limit=1.5, tile_grid_size=(8, 8))
            
            # 3. 轻微增加饱和度
            hsv = cv2.cvtColor(result, cv2.COLOR_BGR2HSV).astype('float32')
            h, s, v = cv2.split(hsv)
            s = np.clip(s * 1.1, 0, 255)  # 轻微增加饱和度
            hsv_enhanced = cv2.merge([h, s, v])
            result = cv2.cvtColor(hsv_enhanced.astype('uint8'), cv2.COLOR_HSV2BGR)
            
            # 确保结果是8位图像
            result = np.clip(result, 0, 255).astype(np.uint8)
            
            # Generate and save debug images if requested
            if debug:
                # Create a visualization of the matches
                matched_img = cv2.drawMatches(img1_copy, kp1, img2_copy, kp2, 
                                           [m for i, m in enumerate(matches) if mask[i][0] == 1], 
                                           None, 
                                           flags=cv2.DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS)
                
                debug_dir = os.path.join('debug')
                os.makedirs(debug_dir, exist_ok=True)
                cv2.imwrite(os.path.join(debug_dir, 'matches.jpg'), matched_img)
                cv2.imwrite(os.path.join(debug_dir, 'warped.jpg'), warped)
                cv2.imwrite(os.path.join(debug_dir, 'result.jpg'), result)
            
            return result
            
        except Exception as e:
            import traceback
            print(f"Error during image stitching: {str(e)}")
            print(traceback.format_exc())
            return None
    
    def stitch_sequence(self, images):
        """
        Stitch a sequence of images into a panorama with enhanced quality
        
        Args:
            images: List of images to stitch
            
        Returns:
            numpy.ndarray: Stitched panorama
        """
        if not images:
            return None
        
        if len(images) == 1:
            return images[0]
        
        print(f"Stitching {len(images)} images...")
        
        # 预处理所有图像
        processed_images = [enhance_image_quality(img) for img in images]
        
        # 校正曝光差异
        processed_images = correct_exposure_differences(processed_images)
        
        # 优化图像顺序
        processed_images = optimize_image_order(processed_images)
        
        # For just 2 images, use direct pair stitching
        if len(processed_images) == 2:
            return self.stitch_pair(processed_images[0], processed_images[1])
        
        # For more images, use progressive stitching
        result = processed_images[0]
        
        for i in tqdm(range(1, len(processed_images))):
            next_result = self.stitch_pair(result, processed_images[i])
            if next_result is None:
                print(f"Failed to stitch image {i}")
                # Try with the next image instead
                if i < len(processed_images) - 1:
                    print(f"Trying with image {i+1} instead")
                    continue
                else:
                    return result  # Return what we have so far
            result = next_result
        
        return result
    
    def create_streetview(self, image_folder, step=5, output_folder='output'):
        """
        Create a sequence of panoramas for street view navigation

        Args:
            image_folder: Folder containing input images
            step: Number of images to include in each panorama
            output_folder: Folder to save output panoramas

        Returns:
            list: Paths to generated panoramas
        """
        # Create output folder
        os.makedirs(output_folder, exist_ok=True)

        # Load all images
        all_images = load_images(image_folder)

        if len(all_images) < step:
            print(f"Not enough images ({len(all_images)}) for step size {step}")
            if len(all_images) > 0:
                # If we have at least one image, create a single panorama
                panorama = all_images[0]
                output_path = os.path.join(output_folder, 'panorama_0.jpg')
                save_image(output_path, panorama)
                return [output_path]
            return []

        # 校正曝光差异
        all_images = correct_exposure_differences(all_images)

        # 计算每个全景图的重叠
        overlap = max(1, step // 2)

        # Create panoramas with overlapping images
        panorama_paths = []

        for i in tqdm(range(0, len(all_images), step - overlap), desc="Creating street view"):
            # Get a slice of images for this panorama
            end_idx = min(i + step, len(all_images))
            image_slice = all_images[i:end_idx]

            if len(image_slice) < 2:
                # Not enough images for a panorama, just use the single image
                if len(image_slice) == 1:
                    panorama = image_slice[0]
                    output_path = os.path.join(output_folder, f'panorama_{len(panorama_paths)}.jpg')
                    save_image(output_path, panorama)
                    panorama_paths.append(output_path)
                continue

            # Stitch this slice
            panorama = self.stitch_sequence(image_slice)

            if panorama is not None:
                # Save panorama
                output_path = os.path.join(output_folder, f'panorama_{len(panorama_paths)}.jpg')
                save_image(output_path, panorama)
                panorama_paths.append(output_path)

                # Create a thumbnail for navigation
                thumbnail = cv2.resize(panorama, (320, 180), interpolation=cv2.INTER_AREA)
                thumb_path = os.path.join(output_folder, f'thumb_{len(panorama_paths)-1}.jpg')
                save_image(thumb_path, thumbnail)

        # Create metadata file for navigation
        metadata = {
            'panoramas': [os.path.basename(path) for path in panorama_paths],
            'count': len(panorama_paths)
        }

        with open(os.path.join(output_folder, 'streetview.json'), 'w') as f:
            import json
            json.dump(metadata, f, indent=2)

        return panorama_paths