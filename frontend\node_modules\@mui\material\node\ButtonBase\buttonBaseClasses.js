"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
exports.getButtonBaseUtilityClass = getButtonBaseUtilityClass;
var _generateUtilityClasses = _interopRequireDefault(require("@mui/utils/generateUtilityClasses"));
var _generateUtilityClass = _interopRequireDefault(require("@mui/utils/generateUtilityClass"));
function getButtonBaseUtilityClass(slot) {
  return (0, _generateUtilityClass.default)('MuiButtonBase', slot);
}
const buttonBaseClasses = (0, _generateUtilityClasses.default)('MuiButtonBase', ['root', 'disabled', 'focusVisible']);
var _default = exports.default = buttonBaseClasses;