import cv2
import numpy as np
import os
from glob import glob
from tqdm import tqdm
import math

class ImprovedRecursivePanoramaStitcher:
    def __init__(self):
        """初始化改进的递归全景拼接器"""
        # 使用更保守的SIFT参数以获得更多特征点
        self.sift = cv2.SIFT_create(
            nfeatures=5000,  # 增加特征点数量
            contrastThreshold=0.03,  # 降低对比度阈值以获得更多特征
            edgeThreshold=15,
            sigma=1.6
        )
        
        # FLANN匹配器
        FLANN_INDEX_KDTREE = 1
        index_params = dict(algorithm=FLANN_INDEX_KDTREE, trees=5)
        search_params = dict(checks=50)
        self.flann = cv2.FlannBasedMatcher(index_params, search_params)
    
    def preprocess_image(self, image):
        """预处理图像"""
        # 轻微的对比度增强
        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        
        # 使用CLAHE增强L通道
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        l = clahe.apply(l)
        
        enhanced = cv2.merge([l, a, b])
        result = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
        
        return result
    
    def detect_and_describe(self, image):
        """特征检测和描述"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
            
        # 增强对比度用于特征检测
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        gray = clahe.apply(gray)
        
        # 检测特征
        keypoints, descriptors = self.sift.detectAndCompute(gray, None)
        return keypoints, descriptors
    
    def match_features(self, desc1, desc2, ratio=0.75):
        """特征匹配 - 使用更宽松的比例测试"""
        if desc1 is None or desc2 is None:
            return []
            
        try:
            matches = self.flann.knnMatch(desc1, desc2, k=2)
            good_matches = []
            for match_pair in matches:
                if len(match_pair) == 2:
                    m, n = match_pair
                    if m.distance < ratio * n.distance:
                        good_matches.append(m)
            return good_matches
        except:
            return []
    
    def stitch_two_images_improved(self, img1, img2, debug=False):
        """
        改进的两图像拼接方法 - 更好地保留原始内容
        """
        # 预处理图像
        img1_proc = self.preprocess_image(img1.copy())
        img2_proc = self.preprocess_image(img2.copy())
        
        # 特征检测
        kp1, desc1 = self.detect_and_describe(img1_proc)
        kp2, desc2 = self.detect_and_describe(img2_proc)
        
        print(f"    特征点数量: img1={len(kp1) if kp1 else 0}, img2={len(kp2) if kp2 else 0}")
        
        if desc1 is None or desc2 is None or len(kp1) < 10 or len(kp2) < 10:
            print(f"    特征点不足，使用简单拼接")
            return self._simple_horizontal_stitch(img1, img2)
        
        # 特征匹配
        matches = self.match_features(desc1, desc2, ratio=0.8)  # 使用更宽松的比例
        
        print(f"    匹配点数量: {len(matches)}")
        
        if len(matches) < 8:  # 降低最低匹配点要求
            print(f"    匹配点不足，使用简单拼接")
            return self._simple_horizontal_stitch(img1, img2)
        
        # 提取匹配点
        src_pts = np.float32([kp1[m.queryIdx].pt for m in matches]).reshape(-1, 1, 2)
        dst_pts = np.float32([kp2[m.trainIdx].pt for m in matches]).reshape(-1, 1, 2)
        
        # 计算单应性矩阵
        homography, mask = cv2.findHomography(
            src_pts, dst_pts, 
            cv2.RANSAC, 
            ransacReprojThreshold=5.0,  # 增大阈值以获得更多内点
            confidence=0.99,
            maxIters=3000
        )
        
        if homography is None:
            print(f"    无法计算单应性矩阵，使用简单拼接")
            return self._simple_horizontal_stitch(img1, img2)
        
        # 计算内点数量
        inlier_count = np.sum(mask)
        inlier_ratio = inlier_count / len(matches)
        print(f"    内点数量: {inlier_count} ({inlier_ratio:.2f})")
        
        if inlier_count < 6:  # 内点太少
            print(f"    内点不足，使用简单拼接")
            return self._simple_horizontal_stitch(img1, img2)
        
        # 使用改进的拼接方法
        try:
            result = self._advanced_stitch_with_homography(img1_proc, img2_proc, homography, debug)
            if result is not None:
                return result
            else:
                print(f"    高级拼接失败，使用简单拼接")
                return self._simple_horizontal_stitch(img1, img2)
        except Exception as e:
            print(f"    拼接过程出错: {str(e)}，使用简单拼接")
            return self._simple_horizontal_stitch(img1, img2)
    
    def _advanced_stitch_with_homography(self, img1, img2, H, debug=False):
        """使用单应性矩阵进行高级拼接"""
        h1, w1 = img1.shape[:2]
        h2, w2 = img2.shape[:2]
        
        # 计算变换后的角点
        corners1 = np.float32([[0, 0], [0, h1], [w1, h1], [w1, 0]]).reshape(-1, 1, 2)
        corners2 = np.float32([[0, 0], [0, h2], [w2, h2], [w2, 0]]).reshape(-1, 1, 2)
        corners2_transformed = cv2.perspectiveTransform(corners2, H)
        
        # 计算输出画布大小
        all_corners = np.concatenate((corners1, corners2_transformed), axis=0)
        [x_min, y_min] = np.int32(all_corners.min(axis=0).ravel())
        [x_max, y_max] = np.int32(all_corners.max(axis=0).ravel())
        
        # 创建平移矩阵
        translation_dist = [-x_min, -y_min]
        H_translation = np.array([[1, 0, translation_dist[0]], 
                                 [0, 1, translation_dist[1]], 
                                 [0, 0, 1]])
        
        # 输出尺寸
        output_width = x_max - x_min
        output_height = y_max - y_min
        
        # 创建输出画布
        result = np.zeros((output_height, output_width, 3), dtype=np.uint8)
        
        # 变换第二张图像
        warped_img2 = cv2.warpPerspective(
            img2, 
            H_translation.dot(H), 
            (output_width, output_height),
            flags=cv2.INTER_LINEAR,
            borderMode=cv2.BORDER_CONSTANT,
            borderValue=(0, 0, 0)
        )
        
        # 创建第一张图像的掩码和变换
        img1_canvas = np.zeros((output_height, output_width, 3), dtype=np.uint8)
        img1_mask = np.zeros((output_height, output_width), dtype=np.uint8)
        
        # 放置第一张图像
        y1_start = translation_dist[1]
        y1_end = y1_start + h1
        x1_start = translation_dist[0]
        x1_end = x1_start + w1
        
        # 确保坐标在有效范围内
        y1_start = max(0, y1_start)
        y1_end = min(output_height, y1_end)
        x1_start = max(0, x1_start)
        x1_end = min(output_width, x1_end)
        
        if y1_end > y1_start and x1_end > x1_start:
            # 计算在原图中的对应区域
            orig_y_start = max(0, -translation_dist[1])
            orig_y_end = orig_y_start + (y1_end - y1_start)
            orig_x_start = max(0, -translation_dist[0])
            orig_x_end = orig_x_start + (x1_end - x1_start)
            
            img1_canvas[y1_start:y1_end, x1_start:x1_end] = img1[orig_y_start:orig_y_end, orig_x_start:orig_x_end]
            img1_mask[y1_start:y1_end, x1_start:x1_end] = 255
        
        # 创建第二张图像的掩码
        img2_gray = cv2.cvtColor(warped_img2, cv2.COLOR_BGR2GRAY)
        img2_mask = (img2_gray > 0).astype(np.uint8) * 255
        
        # 找到重叠区域
        overlap_mask = cv2.bitwise_and(img1_mask, img2_mask)
        overlap_area = np.sum(overlap_mask > 0)
        
        print(f"      重叠区域像素数: {overlap_area}")
        
        if overlap_area > 1000:  # 有足够的重叠区域
            # 创建渐变混合掩码
            blend_mask = self._create_advanced_blend_mask(img1_mask, img2_mask, overlap_mask)
            
            # 混合重叠区域
            overlap_region = overlap_mask > 0
            for i in range(3):
                result[overlap_region, i] = (
                    img1_canvas[overlap_region, i] * (1 - blend_mask[overlap_region]) +
                    warped_img2[overlap_region, i] * blend_mask[overlap_region]
                ).astype(np.uint8)
            
            # 填充非重叠区域
            img1_only = (img1_mask > 0) & (img2_mask == 0)
            img2_only = (img2_mask > 0) & (img1_mask == 0)
            
            result[img1_only] = img1_canvas[img1_only]
            result[img2_only] = warped_img2[img2_only]
            
        else:
            # 重叠区域太小，直接拼接
            print(f"      重叠区域太小，直接拼接")
            result[img1_mask > 0] = img1_canvas[img1_mask > 0]
            result[img2_mask > 0] = warped_img2[img2_mask > 0]
        
        # 智能裁剪 - 更保守的裁剪策略
        result = self._conservative_crop(result)
        
        if debug:
            cv2.imwrite("debug_advanced_stitch.jpg", result)
        
        return result
    
    def _create_advanced_blend_mask(self, mask1, mask2, overlap_mask):
        """创建高级混合掩码"""
        # 计算到各自图像边缘的距离
        dist1 = cv2.distanceTransform(mask1, cv2.DIST_L2, 5)
        dist2 = cv2.distanceTransform(mask2, cv2.DIST_L2, 5)
        
        # 在重叠区域内计算权重
        total_dist = dist1 + dist2
        blend_mask = np.zeros_like(dist1, dtype=np.float32)
        
        valid_mask = (total_dist > 0) & (overlap_mask > 0)
        blend_mask[valid_mask] = dist2[valid_mask] / total_dist[valid_mask]
        
        # 应用高斯平滑使过渡更自然
        blend_mask = cv2.GaussianBlur(blend_mask, (31, 31), 0)
        
        # 确保在[0,1]范围内
        blend_mask = np.clip(blend_mask, 0, 1)
        
        return blend_mask
    
    def _simple_horizontal_stitch(self, img1, img2):
        """简单的水平拼接作为备选方案"""
        h1, w1 = img1.shape[:2]
        h2, w2 = img2.shape[:2]
        
        # 调整图像高度到相同
        target_height = max(h1, h2)
        
        if h1 != target_height:
            img1 = cv2.resize(img1, (w1, target_height), interpolation=cv2.INTER_AREA)
        if h2 != target_height:
            img2 = cv2.resize(img2, (w2, target_height), interpolation=cv2.INTER_AREA)
        
        # 水平拼接
        result = np.hstack([img1, img2])
        
        print(f"      使用简单水平拼接: {result.shape[1]}×{result.shape[0]}")
        return result
    
    def _conservative_crop(self, image):
        """保守的裁剪策略 - 尽量保留更多内容"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        _, thresh = cv2.threshold(gray, 1, 255, cv2.THRESH_BINARY)
        
        # 找到非零区域
        coords = cv2.findNonZero(thresh)
        if coords is None:
            return image
        
        x, y, w, h = cv2.boundingRect(coords)
        
        # 添加更大的边框以保留更多内容
        border = 10
        x = max(0, x - border)
        y = max(0, y - border)
        w = min(image.shape[1] - x, w + 2*border)
        h = min(image.shape[0] - y, h + 2*border)
        
        return image[y:y+h, x:x+w]
    
    def recursive_stitch_images(self, images, debug=False):
        """
        改进的递归拼接图像列表
        """
        if not images:
            return None
            
        if len(images) == 1:
            return images[0]
            
        print(f"开始改进递归拼接 {len(images)} 张图像...")
        
        current_level = images.copy()
        level = 1
        
        while len(current_level) > 1:
            print(f"第 {level} 轮拼接：处理 {len(current_level)} 张图像")
            next_level = []
            
            # 两两配对进行拼接
            for i in range(0, len(current_level), 2):
                if i + 1 < len(current_level):
                    img1 = current_level[i]
                    img2 = current_level[i + 1]
                    
                    print(f"  拼接图像对 {i//2 + 1}: {i+1} + {i+2}")
                    print(f"    图像1尺寸: {img1.shape[1]}×{img1.shape[0]}")
                    print(f"    图像2尺寸: {img2.shape[1]}×{img2.shape[0]}")
                    
                    # 使用改进的拼接方法
                    stitched = self.stitch_two_images_improved(img1, img2, debug=debug)
                    
                    if stitched is not None:
                        next_level.append(stitched)
                        print(f"    拼接成功: {stitched.shape[1]}×{stitched.shape[0]}")
                        if debug:
                            cv2.imwrite(f"debug_improved_level_{level}_pair_{i//2 + 1}.jpg", stitched)
                    else:
                        print(f"    拼接失败，尝试反向...")
                        stitched = self.stitch_two_images_improved(img2, img1, debug=debug)
                        if stitched is not None:
                            next_level.append(stitched)
                            print(f"    反向拼接成功: {stitched.shape[1]}×{stitched.shape[0]}")
                            if debug:
                                cv2.imwrite(f"debug_improved_level_{level}_pair_{i//2 + 1}_reversed.jpg", stitched)
                        else:
                            print(f"    拼接完全失败，保留较大的图像")
                            # 保留较大的图像
                            if img1.shape[0] * img1.shape[1] >= img2.shape[0] * img2.shape[1]:
                                next_level.append(img1)
                            else:
                                next_level.append(img2)
                else:
                    # 奇数个图像，保留到下一轮
                    print(f"  保留单张图像 {i+1} 到下一轮")
                    next_level.append(current_level[i])
            
            current_level = next_level
            level += 1
            
            print(f"第 {level-1} 轮完成，得到 {len(current_level)} 张图像")
            for idx, img in enumerate(current_level):
                print(f"    结果{idx+1}: {img.shape[1]}×{img.shape[0]}")
        
        final_result = current_level[0] if current_level else None
        if final_result is not None:
            print(f"递归拼接完成！最终尺寸: {final_result.shape[1]}×{final_result.shape[0]}")
            # 最终质量增强
            final_result = self._final_enhancement(final_result)
        
        return final_result
    
    def _final_enhancement(self, image):
        """最终图像增强"""
        if image is None:
            return None
        
        # 轻微锐化
        kernel = np.array([[-0.5, -0.5, -0.5], 
                          [-0.5, 5.0, -0.5], 
                          [-0.5, -0.5, -0.5]])
        sharpened = cv2.filter2D(image, -1, kernel)
        
        # 混合原图和锐化图
        result = cv2.addWeighted(image, 0.8, sharpened, 0.2, 0)
        
        return result
    
    def create_improved_panorama(self, image_folder, output_file=None, debug=False):
        """创建改进的全景图"""
        # 加载图像
        extensions = ['jpg', 'jpeg', 'png', 'bmp', 'tiff']
        image_paths = []
        for ext in extensions:
            image_paths.extend(glob(os.path.join(image_folder, f'*.{ext}')))
            image_paths.extend(glob(os.path.join(image_folder, f'*.{ext.upper()}')))
        
        image_paths.sort()
        
        if not image_paths:
            print("未找到图像文件")
            return None
        
        print(f"加载 {len(image_paths)} 张图像...")
        images = []
        for path in tqdm(image_paths):
            img = cv2.imread(path)
            if img is not None:
                # 保持原始分辨率以获得更好的拼接效果
                h, w = img.shape[:2]
                max_size = 2000  # 提高最大尺寸限制
                if w > max_size:
                    scale = max_size / w
                    new_w = max_size
                    new_h = int(h * scale)
                    img = cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_AREA)
                images.append(img)
                print(f"    加载: {os.path.basename(path)} ({img.shape[1]}×{img.shape[0]})")
        
        if not images:
            print("未能加载任何有效图像")
            return None
        
        # 改进的递归拼接
        result = self.recursive_stitch_images(images, debug=debug)
        
        if result is not None and output_file:
            cv2.imwrite(output_file, result, [cv2.IMWRITE_JPEG_QUALITY, 95])
            print(f"改进拼接结果已保存到: {output_file}")
        
        return result 