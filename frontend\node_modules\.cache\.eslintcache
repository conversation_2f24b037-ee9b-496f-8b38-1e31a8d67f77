[{"D:\\Study_Code\\2025-8-5\\qrjy_images\\frontend\\src\\index.js": "1", "D:\\Study_Code\\2025-8-5\\qrjy_images\\frontend\\src\\App.js": "2", "D:\\Study_Code\\2025-8-5\\qrjy_images\\frontend\\src\\pages\\Home.js": "3", "D:\\Study_Code\\2025-8-5\\qrjy_images\\frontend\\src\\pages\\SceneViewer.js": "4", "D:\\Study_Code\\2025-8-5\\qrjy_images\\frontend\\src\\components\\AppHeader.js": "5", "D:\\Study_Code\\2025-8-5\\qrjy_images\\frontend\\src\\pages\\SceneList.js": "6", "D:\\Study_Code\\2025-8-5\\qrjy_images\\frontend\\src\\pages\\CreateScene.js": "7", "D:\\Study_Code\\2025-8-5\\qrjy_images\\frontend\\src\\components\\StreetViewPanorama.js": "8"}, {"size": 210, "mtime": 1754373582256, "results": "9", "hashOfConfig": "10"}, {"size": 1351, "mtime": 1752396829885, "results": "11", "hashOfConfig": "10"}, {"size": 3815, "mtime": 1752650598056, "results": "12", "hashOfConfig": "10"}, {"size": 29132, "mtime": 1754155421553, "results": "13", "hashOfConfig": "10"}, {"size": 1232, "mtime": 1752650376870, "results": "14", "hashOfConfig": "10"}, {"size": 7151, "mtime": 1752650408218, "results": "15", "hashOfConfig": "10"}, {"size": 13773, "mtime": 1754374879417, "results": "16", "hashOfConfig": "10"}, {"size": 25692, "mtime": 1754157299453, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "j9wy68", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Study_Code\\2025-8-5\\qrjy_images\\frontend\\src\\index.js", [], [], "D:\\Study_Code\\2025-8-5\\qrjy_images\\frontend\\src\\App.js", [], [], "D:\\Study_Code\\2025-8-5\\qrjy_images\\frontend\\src\\pages\\Home.js", ["42"], [], "D:\\Study_Code\\2025-8-5\\qrjy_images\\frontend\\src\\pages\\SceneViewer.js", ["43", "44", "45", "46"], [], "D:\\Study_Code\\2025-8-5\\qrjy_images\\frontend\\src\\components\\AppHeader.js", [], [], "D:\\Study_Code\\2025-8-5\\qrjy_images\\frontend\\src\\pages\\SceneList.js", [], [], "D:\\Study_Code\\2025-8-5\\qrjy_images\\frontend\\src\\pages\\CreateScene.js", ["47"], [], "D:\\Study_Code\\2025-8-5\\qrjy_images\\frontend\\src\\components\\StreetViewPanorama.js", ["48", "49", "50", "51", "52", "53", "54"], [], {"ruleId": "55", "severity": 1, "message": "56", "line": 7, "column": 8, "nodeType": "57", "messageId": "58", "endLine": 7, "endColumn": 11}, {"ruleId": "55", "severity": 1, "message": "59", "line": 55, "column": 10, "nodeType": "57", "messageId": "58", "endLine": 55, "endColumn": 23}, {"ruleId": "55", "severity": 1, "message": "60", "line": 55, "column": 25, "nodeType": "57", "messageId": "58", "endLine": 55, "endColumn": 41}, {"ruleId": "55", "severity": 1, "message": "61", "line": 56, "column": 10, "nodeType": "57", "messageId": "58", "endLine": 56, "endColumn": 18}, {"ruleId": "55", "severity": 1, "message": "62", "line": 56, "column": 20, "nodeType": "57", "messageId": "58", "endLine": 56, "endColumn": 31}, {"ruleId": "55", "severity": 1, "message": "63", "line": 37, "column": 24, "nodeType": "57", "messageId": "58", "endLine": 37, "endColumn": 39}, {"ruleId": "55", "severity": 1, "message": "64", "line": 2, "column": 27, "nodeType": "57", "messageId": "58", "endLine": 2, "endColumn": 30}, {"ruleId": "55", "severity": 1, "message": "65", "line": 9, "column": 3, "nodeType": "57", "messageId": "58", "endLine": 9, "endColumn": 13}, {"ruleId": "55", "severity": 1, "message": "66", "line": 23, "column": 9, "nodeType": "57", "messageId": "58", "endLine": 23, "endColumn": 26}, {"ruleId": "55", "severity": 1, "message": "67", "line": 69, "column": 9, "nodeType": "57", "messageId": "58", "endLine": 69, "endColumn": 23}, {"ruleId": "55", "severity": 1, "message": "68", "line": 88, "column": 11, "nodeType": "57", "messageId": "58", "endLine": 88, "endColumn": 18}, {"ruleId": "55", "severity": 1, "message": "69", "line": 95, "column": 11, "nodeType": "57", "messageId": "58", "endLine": 95, "endColumn": 19}, {"ruleId": "55", "severity": 1, "message": "70", "line": 364, "column": 11, "nodeType": "57", "messageId": "58", "endLine": 364, "endColumn": 21}, "no-unused-vars", "'Box' is defined but never used.", "Identifier", "unusedVar", "'isConfiguring' is assigned a value but never used.", "'setIsConfiguring' is assigned a value but never used.", "'isAdding' is assigned a value but never used.", "'setIsAdding' is assigned a value but never used.", "'setStitcherType' is assigned a value but never used.", "'Fab' is defined but never used.", "'Navigation' is defined but never used.", "'animationFrameRef' is assigned a value but never used.", "'renderPanorama' is assigned a value but never used.", "'centerX' is assigned a value but never used.", "'endAngle' is assigned a value but never used.", "'deltaPitch' is assigned a value but never used."]