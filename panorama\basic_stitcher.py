import numpy as np
import cv2
import os

class BasicStitcher:
    def __init__(self):
        # 初始化SIFT检测器
        self.sift = cv2.SIFT_create()

    def stitch(self, images, ratio=0.75, reproj_thresh=4.0, show_matches=False):
        """
        拼接两张图片
        :param images: 包含两张图片的列表
        :param ratio: Lowe's ratio test的参数
        :param reproj_thresh: RANSAC重投影误差阈值
        :param show_matches: 是否显示匹配点
        :return: 拼接后的图片，如果show_matches为True，则返回拼接后的图片和匹配点可视化图
        """
        if len(images) != 2:
            print("需要提供两张图像进行拼接")
            return None
            
        image_b, image_a = images

        # 检测关键点并计算特征描述子
        kps_a, features_a = self.detect_and_describe(image_a)
        kps_b, features_b = self.detect_and_describe(image_b)

        # 匹配关键点
        matches, homography, status = self.match_keypoints(kps_a, kps_b, features_a, features_b, ratio, reproj_thresh)

        if homography is None:
            print("Not enough matches are found - {}/{}".format(len(matches) if matches else 0, 4))
            return None

        # 对image_a进行透视变换 - 不使用任何增强或插值方法
        result = cv2.warpPerspective(
            image_a, 
            homography, 
            (image_a.shape[1] + image_b.shape[1], max(image_a.shape[0], image_b.shape[0]))
        )

        # 将image_b直接拼接到result的左侧，不使用混合
        # 修改这部分代码以处理不同尺寸的图像
        if image_b.shape[0] <= result.shape[0]:
            result[0:image_b.shape[0], 0:image_b.shape[1]] = image_b
        else:
            # 如果image_b更高，则需要调整result的大小或裁剪image_b
            result[0:result.shape[0], 0:image_b.shape[1]] = image_b[0:result.shape[0], :]

        # 裁剪黑色边框
        result = self._crop_black_borders(result)

        if show_matches:
            # 生成匹配点的可视化图
            vis = self.draw_matches(image_a, image_b, kps_a, kps_b, matches, status)
            return result, vis

        return result
        
    def stitch_multiple(self, images):
        """
        拼接多张图片
        :param images: 图片列表
        :return: 拼接后的全景图
        """
        if len(images) < 2:
            return images[0] if images else None
            
        print(f"Stitching {len(images)} images...")
        
        # 从第一张图片开始，逐步拼接，不做任何预处理
        panorama = images[0]
        
        for i in range(1, len(images)):
            print(f"Stitching image {i+1}/{len(images)}...")
            # 注意：原始代码中image_b在前，image_a在后
            # 所以这里我们传递[images[i], panorama]而不是[panorama, images[i]]
            result = self.stitch([images[i], panorama])
            
            if result is None:
                print(f"Failed to stitch image {i+1}")
                # 如果拼接失败，尝试反向拼接
                result = self.stitch([panorama, images[i]])
                
                if result is None:
                    print(f"Also failed with reverse order for image {i+1}")
                    continue
            
            panorama = result
            
        return panorama

    def detect_and_describe(self, image):
        """
        检测关键点并计算特征描述子
        :param image: 输入图片
        :return: 关键点坐标和特征描述子
        """
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        kps, features = self.sift.detectAndCompute(gray, None)
        kps = np.float32([kp.pt for kp in kps])
        return kps, features

    def match_keypoints(self, kps_a, kps_b, features_a, features_b, ratio, reproj_thresh):
        """
        匹配关键点
        :param kps_a: 图片A的关键点
        :param kps_b: 图片B的关键点
        :param features_a: 图片A的特征描述子
        :param features_b: 图片B的特征描述子
        :param ratio: Lowe's ratio test的参数
        :param reproj_thresh: RANSAC重投影误差阈值
        :return: 匹配点、单应性矩阵和状态
        """
        try:
            matcher = cv2.BFMatcher()
            raw_matches = matcher.knnMatch(features_a, features_b, 2)

            matches = []
            for m_n in raw_matches:
                # 确保匹配结果包含两个元素
                if len(m_n) == 2:
                    m, n = m_n
                    if m.distance < n.distance * ratio:
                        matches.append((m.trainIdx, m.queryIdx))

            if len(matches) > 4:
                pts_a = np.float32([kps_a[i] for (_, i) in matches])
                pts_b = np.float32([kps_b[i] for (i, _) in matches])
                homography, status = cv2.findHomography(pts_a, pts_b, cv2.RANSAC, reproj_thresh)
                return matches, homography, status
        except Exception as e:
            print(f"匹配关键点时出错: {str(e)}")
            
        return None, None, None

    def draw_matches(self, image_a, image_b, kps_a, kps_b, matches, status):
        """
        绘制匹配点
        :param image_a: 图片A
        :param image_b: 图片B
        :param kps_a: 图片A的关键点
        :param kps_b: 图片B的关键点
        :param matches: 匹配点
        :param status: 匹配状态
        :return: 匹配点可视化图
        """
        h_a, w_a = image_a.shape[:2]
        h_b, w_b = image_b.shape[:2]
        vis = np.zeros((max(h_a, h_b), w_a + w_b, 3), dtype="uint8")
        vis[0:h_a, 0:w_a] = image_a
        vis[0:h_b, w_a:] = image_b

        for i, (train_idx, query_idx) in enumerate(matches):
            if status[i][0] == 1:
                pt_a = (int(kps_a[query_idx][0]), int(kps_a[query_idx][1]))
                pt_b = (int(kps_b[train_idx][0]) + w_a, int(kps_b[train_idx][1]))
                cv2.line(vis, pt_a, pt_b, (0, 255, 0), 1)

        return vis
        
    def _crop_black_borders(self, image):
        """
        裁剪图像中的黑色边框
        :param image: 输入图像
        :return: 裁剪后的图像
        """
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
            
        # 二值化
        _, thresh = cv2.threshold(gray, 1, 255, cv2.THRESH_BINARY)
        
        # 查找轮廓
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if contours:
            # 合并所有轮廓
            all_contours = np.vstack([contours[i] for i in range(len(contours))])
            x, y, w, h = cv2.boundingRect(all_contours)
            
            # 添加小边距
            border = 5
            x = max(0, x - border)
            y = max(0, y - border)
            w = min(image.shape[1] - x, w + 2*border)
            h = min(image.shape[0] - y, h + 2*border)
            
            # 裁剪图像
            return image[y:y+h, x:x+w]
        
        return image 