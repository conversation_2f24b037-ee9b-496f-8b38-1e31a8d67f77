import cv2
import numpy as np
import os
from glob import glob
from tqdm import tqdm
import math

class GoogleStreetViewCreator:
    def __init__(self, target_height=None):
        """初始化Google街景风格的全景图生成器"""
        # 使用更保守的SIFT参数
        self.sift = cv2.SIFT_create(
            nfeatures=3000,
            contrastThreshold=0.04,
            edgeThreshold=10,
            sigma=1.6
        )
        
        # FLANN匹配器
        FLANN_INDEX_KDTREE = 1
        index_params = dict(algorithm=FLANN_INDEX_KDTREE, trees=5)
        search_params = dict(checks=50)
        self.flann = cv2.FlannBasedMatcher(index_params, search_params)
        
        # 目标高度设置（如果为None则不限制）
        self.target_height = target_height
        
    def preprocess_image(self, image):
        """预处理图像 - 轻微增强但保持自然"""
        # 轻微的对比度增强
        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        
        # 使用更温和的CLAHE
        clahe = cv2.createCLAHE(clipLimit=1.5, tileGridSize=(8, 8))
        l = clahe.apply(l)
        
        enhanced = cv2.merge([l, a, b])
        result = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
        
        return result
    
    def detect_and_describe(self, image):
        """特征检测和描述"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
            
        # 轻微增强对比度用于特征检测
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        gray = clahe.apply(gray)
        
        # 检测特征
        keypoints, descriptors = self.sift.detectAndCompute(gray, None)
        return keypoints, descriptors
    
    def match_features(self, desc1, desc2, ratio=0.7):
        """特征匹配 - 使用更严格的比例测试"""
        if desc1 is None or desc2 is None:
            return []
            
        try:
            matches = self.flann.knnMatch(desc1, desc2, k=2)
            good_matches = []
            for match_pair in matches:
                if len(match_pair) == 2:
                    m, n = match_pair
                    if m.distance < ratio * n.distance:
                        good_matches.append(m)
            return good_matches
        except:
            return []
    
    def cylindrical_projection(self, img, focal_length=None):
        """将图像投影到柱面，减少拼接时的失真"""
        h, w = img.shape[:2]
        
        # 如果没有提供焦距，使用图像宽度作为估计
        if focal_length is None:
            focal_length = w * 0.8  # 经验值
        
        # 创建输出图像
        output = np.zeros_like(img)
        
        # 柱面投影
        center_x, center_y = w // 2, h // 2
        
        for y in range(h):
            for x in range(w):
                # 将像素坐标转换为相对于中心的坐标
                x_rel = x - center_x
                y_rel = y - center_y
                
                # 柱面投影公式
                theta = np.arctan(x_rel / focal_length)
                x_cyl = focal_length * theta + center_x
                y_cyl = focal_length * y_rel / np.sqrt(x_rel**2 + focal_length**2) + center_y
                
                # 检查投影后的坐标是否在图像范围内
                if 0 <= x_cyl < w and 0 <= y_cyl < h:
                    # 双线性插值
                    x_floor, y_floor = int(x_cyl), int(y_cyl)
                    x_ceil, y_ceil = min(x_floor + 1, w - 1), min(y_floor + 1, h - 1)
                    
                    dx, dy = x_cyl - x_floor, y_cyl - y_floor
                    
                    # 双线性插值
                    top_left = img[y_floor, x_floor] * (1 - dx) * (1 - dy)
                    top_right = img[y_floor, x_ceil] * dx * (1 - dy)
                    bottom_left = img[y_ceil, x_floor] * (1 - dx) * dy
                    bottom_right = img[y_ceil, x_ceil] * dx * dy
                    
                    output[y, x] = top_left + top_right + bottom_left + bottom_right
        
        return output

    def stitch_two_images_cylindrical(self, img1, img2):
        """使用柱面投影进行拼接，减少失真"""
        # 先进行柱面投影
        focal_length = max(img1.shape[1], img2.shape[1]) * 0.8
        
        # 对于街景拼接，我们使用简化的水平拼接方法
        return self.stitch_horizontal_simple(img1, img2)
    
    def stitch_horizontal_simple(self, img1, img2):
        """简化的水平拼接方法，适合街景序列"""
        h1, w1 = img1.shape[:2]
        h2, w2 = img2.shape[:2]
        
        # 确保两张图像高度相同
        if h1 != h2:
            target_height = min(h1, h2)
            if h1 != target_height:
                img1 = cv2.resize(img1, (int(w1 * target_height / h1), target_height))
                h1, w1 = img1.shape[:2]
            if h2 != target_height:
                img2 = cv2.resize(img2, (int(w2 * target_height / h2), target_height))
                h2, w2 = img2.shape[:2]
        
        # 特征检测和匹配
        kp1, desc1 = self.detect_and_describe(img1)
        kp2, desc2 = self.detect_and_describe(img2)
        
        if desc1 is None or desc2 is None or len(kp1) < 10 or len(kp2) < 10:
            # 如果特征点不足，使用简单的水平拼接
            overlap = min(w1, w2) // 4  # 假设25%重叠
            result_width = w1 + w2 - overlap
            result = np.zeros((h1, result_width, 3), dtype=np.uint8)
            
            # 放置第一张图像
            result[:, :w1] = img1
            
            # 放置第二张图像，带有简单的混合
            start_x = w1 - overlap
            for i in range(overlap):
                alpha = i / overlap
                x_pos = start_x + i
                if x_pos < result_width and i < w2:
                    result[:, x_pos] = (1 - alpha) * result[:, x_pos] + alpha * img2[:, i]
            
            # 放置第二张图像的剩余部分
            remaining_start = w1
            remaining_width = min(w2 - overlap, result_width - remaining_start)
            if remaining_width > 0:
                result[:, remaining_start:remaining_start + remaining_width] = img2[:, overlap:overlap + remaining_width]
            
            return result
        
        # 特征匹配
        matches = self.match_features(desc1, desc2, ratio=0.8)
        
        if len(matches) < 10:
            # 特征匹配不足，使用简单的重叠拼接
            overlap = min(w1, w2) // 4  # 假设25%重叠
            result_width = w1 + w2 - overlap
            result = np.zeros((h1, result_width, 3), dtype=np.uint8)
            
            # 放置第一张图像
            result[:, :w1] = img1
            
            # 放置第二张图像，带有简单的混合
            start_x = w1 - overlap
            for i in range(overlap):
                alpha = i / overlap
                x_pos = start_x + i
                if x_pos < result_width and i < w2:
                    result[:, x_pos] = (1 - alpha) * result[:, x_pos] + alpha * img2[:, i]
            
            # 放置第二张图像的剩余部分
            remaining_start = w1
            remaining_width = min(w2 - overlap, result_width - remaining_start)
            if remaining_width > 0:
                result[:, remaining_start:remaining_start + remaining_width] = img2[:, overlap:overlap + remaining_width]
            
            return result
        
        # 计算水平偏移
        src_pts = np.float32([kp1[m.queryIdx].pt for m in matches])
        dst_pts = np.float32([kp2[m.trainIdx].pt for m in matches])
        
        # 计算平均水平偏移
        offsets = src_pts - dst_pts
        median_offset_x = np.median(offsets[:, 0])
        
        # 限制偏移量在合理范围内
        max_offset = min(w1, w2) * 0.8
        offset_x = max(-max_offset, min(max_offset, median_offset_x))
        
        # 计算重叠区域
        if offset_x > 0:
            overlap = min(w1 - int(offset_x), w2)
        else:
            overlap = min(w1, w2 + int(offset_x))
        
        overlap = max(0, overlap)
        
        # 创建结果图像
        result_width = w1 + w2 - overlap
        result = np.zeros((h1, result_width, 3), dtype=np.uint8)
        
        # 放置第一张图像
        result[:, :w1] = img1
        
        # 计算第二张图像的起始位置
        start_x = w1 - overlap
        
        # 混合重叠区域
        if overlap > 0:
            for i in range(overlap):
                if start_x + i < result_width and i < w2:
                    alpha = i / overlap
                    result[:, start_x + i] = (1 - alpha) * result[:, start_x + i] + alpha * img2[:, i]
        
        # 放置第二张图像的非重叠部分
        non_overlap_start = start_x + overlap
        non_overlap_width = min(w2 - overlap, result_width - non_overlap_start)
        if non_overlap_width > 0:
            result[:, non_overlap_start:non_overlap_start + non_overlap_width] = img2[:, overlap:overlap + non_overlap_width]
        
        return result
    
    def stitch_two_images_natural(self, img1, img2):
        """自然拼接两张图像 - 避免过度变形"""
        # 预处理图像
        img1_proc = self.preprocess_image(img1)
        img2_proc = self.preprocess_image(img2)
        
        # 特征检测
        kp1, desc1 = self.detect_and_describe(img1_proc)
        kp2, desc2 = self.detect_and_describe(img2_proc)
        
        if desc1 is None or desc2 is None or len(kp1) < 20 or len(kp2) < 20:
            print(f"特征点不足: img1={len(kp1) if kp1 else 0}, img2={len(kp2) if kp2 else 0}")
            return None
            
        # 特征匹配
        matches = self.match_features(desc1, desc2, ratio=0.7)
        
        if len(matches) < 20:
            print(f"匹配点不足: {len(matches)}")
            return None
        
        # 提取匹配点
        src_pts = np.float32([kp1[m.queryIdx].pt for m in matches]).reshape(-1, 1, 2)
        dst_pts = np.float32([kp2[m.trainIdx].pt for m in matches]).reshape(-1, 1, 2)
        
        # 使用更严格的RANSAC参数计算单应性矩阵
        homography, mask = cv2.findHomography(
            src_pts, dst_pts, 
            cv2.RANSAC, 
            ransacReprojThreshold=3.0,  # 更严格的阈值
            confidence=0.99,
            maxIters=5000
        )
        
        if homography is None:
            print("无法计算单应性矩阵")
            return None
        
        # 检查单应性矩阵的合理性
        if not self._is_homography_reasonable(homography):
            print("单应性矩阵不合理，可能导致严重变形")
            return None
        
        # 计算输出图像尺寸 - 更保守的方法
        h1, w1 = img1.shape[:2]
        h2, w2 = img2.shape[:2]
        
        # 变换第二幅图像的四个角点
        corners2 = np.float32([[0, 0], [0, h2], [w2, h2], [w2, 0]]).reshape(-1, 1, 2)
        corners2_transformed = cv2.perspectiveTransform(corners2, homography)
        
        # 计算合并后的边界，但限制过度扩展
        corners1 = np.float32([[0, 0], [0, h1], [w1, h1], [w1, 0]]).reshape(-1, 1, 2)
        all_corners = np.concatenate((corners1, corners2_transformed), axis=0)
        
        [x_min, y_min] = np.int32(all_corners.min(axis=0).ravel())
        [x_max, y_max] = np.int32(all_corners.max(axis=0).ravel())
        
        # 限制输出尺寸，避免过度扩展
        max_width = max(w1, w2) * 2.5  # 限制最大宽度
        max_height = max(h1, h2) * 1.5  # 限制最大高度
        
        output_width = min(x_max - x_min, int(max_width))
        output_height = min(y_max - y_min, int(max_height))
        
        # 调整边界
        if output_width < x_max - x_min:
            x_max = x_min + output_width
        if output_height < y_max - y_min:
            y_max = y_min + output_height
        
        # 平移矩阵
        translation_dist = [-x_min, -y_min]
        H_translation = np.array([[1, 0, translation_dist[0]], 
                                 [0, 1, translation_dist[1]], 
                                 [0, 0, 1]])
        
        # 创建输出图像
        output_size = (output_width, output_height)
        result = np.zeros((output_height, output_width, 3), dtype=np.uint8)
        
        # 变换第二张图像
        warped_img2 = cv2.warpPerspective(
            img2_proc, 
            H_translation.dot(homography), 
            output_size,
            flags=cv2.INTER_LINEAR,  # 使用线性插值减少伪影
            borderMode=cv2.BORDER_REFLECT_101
        )
        
        # 放置第一张图像
        y1_start = max(0, translation_dist[1])
        y1_end = min(output_height, translation_dist[1] + h1)
        x1_start = max(0, translation_dist[0])
        x1_end = min(output_width, translation_dist[0] + w1)
        
        if y1_end > y1_start and x1_end > x1_start:
            result[y1_start:y1_end, x1_start:x1_end] = img1_proc[
                y1_start-translation_dist[1]:y1_end-translation_dist[1],
                x1_start-translation_dist[0]:x1_end-translation_dist[0]
            ]
        
        # 智能混合重叠区域
        img1_mask = np.zeros((output_height, output_width), dtype=np.uint8)
        img1_mask[y1_start:y1_end, x1_start:x1_end] = 255
        
        img2_mask = np.zeros((output_height, output_width), dtype=np.uint8)
        img2_gray = cv2.cvtColor(warped_img2, cv2.COLOR_BGR2GRAY)
        img2_mask[img2_gray > 0] = 255
        
        # 找到重叠区域
        overlap_mask = cv2.bitwise_and(img1_mask, img2_mask)
        
        if np.sum(overlap_mask) > 0:
            # 创建渐变混合掩码
            overlap_coords = np.where(overlap_mask > 0)
            
            if len(overlap_coords[0]) > 0:
                # 计算混合权重 - 基于到图像边缘的距离
                blend_mask = self._create_blend_mask(overlap_mask, img1_mask, img2_mask)
                
                # 应用混合
                for i in range(3):  # 对每个颜色通道
                    overlap_region = overlap_mask > 0
                    result[overlap_region, i] = (
                        result[overlap_region, i] * (1 - blend_mask[overlap_region]) +
                        warped_img2[overlap_region, i] * blend_mask[overlap_region]
                    ).astype(np.uint8)
        
        # 填充非重叠区域
        non_overlap_mask = (img2_mask > 0) & (img1_mask == 0)
        result[non_overlap_mask] = warped_img2[non_overlap_mask]
        
        # 裁剪黑边
        result = self._crop_black_borders(result)
        
        return result
    
    def _is_homography_reasonable(self, H):
        """检查单应性矩阵是否合理"""
        if H is None:
            return False
        
        # 检查行列式，避免过度缩放或镜像
        det = np.linalg.det(H[:2, :2])
        if det < 0.1 or det > 10:
            return False
        
        # 检查透视变换的合理性
        if abs(H[2, 0]) > 0.002 or abs(H[2, 1]) > 0.002:
            return False
        
        return True
    
    def _create_blend_mask(self, overlap_mask, img1_mask, img2_mask):
        """创建智能混合掩码"""
        # 计算到图像边缘的距离
        dist1 = cv2.distanceTransform(img1_mask, cv2.DIST_L2, 5)
        dist2 = cv2.distanceTransform(img2_mask, cv2.DIST_L2, 5)
        
        # 归一化距离
        total_dist = dist1 + dist2
        blend_mask = np.zeros_like(dist1)
        
        valid_mask = total_dist > 0
        blend_mask[valid_mask] = dist2[valid_mask] / total_dist[valid_mask]
        
        # 应用高斯平滑使过渡更自然
        blend_mask = cv2.GaussianBlur(blend_mask, (21, 21), 0)
        
        return blend_mask
    
    def _crop_black_borders(self, image):
        """裁剪黑边"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        _, thresh = cv2.threshold(gray, 1, 255, cv2.THRESH_BINARY)
        
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return image
            
        # 找到最大轮廓
        largest_contour = max(contours, key=cv2.contourArea)
        x, y, w, h = cv2.boundingRect(largest_contour)
        
        # 添加小边距
        border = 5
        x = max(0, x - border)
        y = max(0, y - border)
        w = min(image.shape[1] - x, w + 2*border)
        h = min(image.shape[0] - y, h + 2*border)
        
        return image[y:y+h, x:x+w]
    
    def detect_overlap_region_sequential(self, img1, img2):
        """检测两张图片的重叠区域（顺序拼接专用）"""
        # 特征检测
        gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)
        
        kp1, desc1 = self.detect_and_describe(gray1)
        kp2, desc2 = self.detect_and_describe(gray2)
        
        if desc1 is None or desc2 is None or len(kp1) < 10 or len(kp2) < 10:
            return 0
        
        # 特征匹配
        matches = self.match_features(desc1, desc2, ratio=0.7)
        
        if len(matches) < 10:
            return 0
        
        # 分析匹配点的位置分布
        img1_pts = [kp1[m.queryIdx].pt for m in matches]
        img2_pts = [kp2[m.trainIdx].pt for m in matches]
        
        # 计算img1右侧和img2左侧的匹配点
        img1_width = img1.shape[1]
        img2_width = img2.shape[1]
        
        # img1右侧1/3区域的匹配点
        img1_right_matches = [pt for pt in img1_pts if pt[0] > img1_width * 0.7]
        # img2左侧1/3区域的匹配点
        img2_left_matches = [pt for pt in img2_pts if pt[0] < img2_width * 0.3]
        
        if len(img1_right_matches) < 5 or len(img2_left_matches) < 5:
            return 0
        
        # 估算重叠宽度
        avg_img1_right_x = np.mean([pt[0] for pt in img1_right_matches])
        avg_img2_left_x = np.mean([pt[0] for pt in img2_left_matches])
        
        # 重叠宽度 = img1右侧距离边缘的距离 + img2左侧匹配点的位置
        overlap_width = (img1_width - avg_img1_right_x) + avg_img2_left_x
        overlap_width = max(0, min(overlap_width, min(img1_width, img2_width) * 0.5))
        
        return int(overlap_width)
    
    def blend_overlap_region_sequential(self, img1, img2, overlap_width):
        """混合重叠区域（顺序拼接专用）"""
        if overlap_width <= 0:
            return np.hstack([img1, img2])
        
        h1, w1 = img1.shape[:2]
        h2, w2 = img2.shape[:2]
        
        # 确保高度一致
        target_h = max(h1, h2)
        if h1 != target_h:
            img1 = cv2.resize(img1, (w1, target_h))
            w1 = img1.shape[1]  # 更新宽度
        if h2 != target_h:
            img2 = cv2.resize(img2, (w2, target_h))
            w2 = img2.shape[1]  # 更新宽度
        
        # 计算输出尺寸
        output_width = w1 + w2 - overlap_width
        result = np.zeros((target_h, output_width, 3), dtype=np.uint8)
        
        # 放置第一张图
        result[:, :w1] = img1
        
        # 处理重叠区域
        blend_start = w1 - overlap_width
        
        if blend_start >= 0 and overlap_width > 0:
            # 创建渐变混合
            for i in range(overlap_width):
                if blend_start + i < output_width and i < w2:
                    # 线性插值权重
                    alpha = i / overlap_width
                    
                    # 混合像素
                    result[:, blend_start + i] = (
                        img1[:, blend_start + i] * (1 - alpha) +
                        img2[:, i] * alpha
                    ).astype(np.uint8)
        
        # 放置第二张图的非重叠部分
        remaining_start = w1
        remaining_img2_start = overlap_width
        
        if remaining_start < output_width and remaining_img2_start < w2:
            remaining_width = min(output_width - remaining_start, w2 - remaining_img2_start)
            if remaining_width > 0:
                result[:, remaining_start:remaining_start+remaining_width] = img2[:, remaining_img2_start:remaining_img2_start+remaining_width]
        
        return result
    
    def check_image_similarity_sequential(self, img1, img2, threshold=0.8):
        """检查两张图片的相似度（顺序拼接专用）"""
        # 调整到相同尺寸进行比较
        h, w = 100, 100  # 缩略图比较
        img1_small = cv2.resize(img1, (w, h))
        img2_small = cv2.resize(img2, (w, h))
        
        # 转为灰度图
        gray1 = cv2.cvtColor(img1_small, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(img2_small, cv2.COLOR_BGR2GRAY)
        
        # 计算结构相似性
        mean1, mean2 = np.mean(gray1), np.mean(gray2)
        std1, std2 = np.std(gray1), np.std(gray2)
        
        if std1 == 0 or std2 == 0:
            return False
        
        # 计算相关系数
        try:
            correlation = np.corrcoef(gray1.flatten(), gray2.flatten())[0, 1]
            return correlation > threshold
        except:
                         return False
    
    def _remove_black_borders_sequential(self, image):
        """移除黑边（顺序拼接专用）"""
        if image is None:
            return image
            
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 找到非黑色区域
        _, thresh = cv2.threshold(gray, 5, 255, cv2.THRESH_BINARY)
        
        # 找到轮廓
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return image
        
        # 找到最大轮廓的边界矩形
        largest_contour = max(contours, key=cv2.contourArea)
        x, y, w, h = cv2.boundingRect(largest_contour)
        
        # 轻微扩展边界以确保不丢失内容
        border = 2
        x = max(0, x - border)
        y = max(0, y - border)
        w = min(image.shape[1] - x, w + 2*border)
        h = min(image.shape[0] - y, h + 2*border)
        
        return image[y:y+h, x:x+w]
    
    def create_single_panorama(self, image_folder, output_folder='output', target_height=None):
        """创建单张完整全景图 - 将所有图片按顺序拼接成一张图"""
        os.makedirs(output_folder, exist_ok=True)
        
        # 如果传入了目标高度，更新实例变量
        if target_height is not None:
            self.target_height = target_height
        
        # 加载图像
        extensions = ['jpg', 'jpeg', 'png', 'bmp', 'tiff']
        image_paths = []
        for ext in extensions:
            image_paths.extend(glob(os.path.join(image_folder, f'*.{ext}')))
            image_paths.extend(glob(os.path.join(image_folder, f'*.{ext.upper()}')))
        
        image_paths.sort()
        
        if not image_paths:
            print("未找到图像文件")
            return []
        
        print(f"加载 {len(image_paths)} 张图像，准备拼接成单张全景图...")
        images = []
        
        # 如果设置了目标高度，先收集所有图像尺寸信息
        if self.target_height is not None:
            print(f"统一高度处理: {self.target_height}px")
        
        for path in tqdm(image_paths):
            img = cv2.imread(path)
            if img is not None:
                h, w = img.shape[:2]
                
                # 如果设置了目标高度，统一所有图像高度
                if self.target_height is not None:
                    if h != self.target_height:
                        scale = self.target_height / h
                        new_width = int(w * scale)
                        img = cv2.resize(img, (new_width, self.target_height), interpolation=cv2.INTER_AREA)
                        print(f"  调整图像: {w}x{h} -> {new_width}x{self.target_height}")
                else:
                    # 如果没有设置目标高度，移除原来的宽度限制，保持原始尺寸
                    # 只在图像过大时适当缩小以提高处理效率
                    if w > 2400:  # 提高限制到2400像素
                        scale = 2400.0 / w
                        new_w = 2400
                        new_h = int(h * scale)
                        img = cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_AREA)
                        print(f"  缩放过大图像: {w}x{h} -> {new_w}x{new_h}")
                
                images.append(img)
        
        if not images:
            print("未能加载任何图像")
            return []
        
        if len(images) == 1:
            print("只有一张图像，直接保存")
            output_file = os.path.join(output_folder, "panorama_0.jpg")
            
            # 如果设置了目标高度，确保最终图像符合目标高度
            if self.target_height is not None:
                h, w = images[0].shape[:2]
                if h != self.target_height:
                    scale = self.target_height / h
                    new_width = int(w * scale)
                    images[0] = cv2.resize(images[0], (new_width, self.target_height), interpolation=cv2.INTER_AREA)
            
            cv2.imwrite(output_file, images[0], [cv2.IMWRITE_JPEG_QUALITY, 95])
            return [output_file]
        
        # 使用顺序拼接算法（类似 stitch_roma2_sequential.py）
        print(f"开始按上传顺序拼接 {len(images)} 张图像成一张完整全景图...")
        print(f"目标高度: {self.target_height}px")
        print("-" * 60)
        
        # 从第一张图开始
        result = images[0].copy()
        
        for i in range(1, len(images)):
            print(f"\n第 {i} 步：拼接图像 {i+1}")
            print(f"  当前结果尺寸: {result.shape[1]}×{result.shape[0]}")
            print(f"  待拼接图像尺寸: {images[i].shape[1]}×{images[i].shape[0]}")
            
            # 检查相似度，避免重复图像
            if i > 0 and self.check_image_similarity_sequential(images[i-1], images[i]):
                print(f"  检测到相似图像，跳过拼接")
                continue
            
            # 检测重叠区域
            overlap_width = self.detect_overlap_region_sequential(result, images[i])
            
            if overlap_width > 0:
                print(f"  检测到重叠区域: {overlap_width} 像素，使用混合拼接")
            else:
                print(f"  未检测到重叠，使用直接拼接")
                # 设置最小重叠以获得更好效果
                overlap_width = min(50, images[i].shape[1] // 10)
            
            # 混合拼接
            result = self.blend_overlap_region_sequential(result, images[i], overlap_width)
            
            print(f"  拼接完成，当前尺寸: {result.shape[1]}×{result.shape[0]}")
        
        panorama = result
        
        # 最终质量增强
        print(f"\n🎨 最终图像增强...")
        panorama = self._final_enhancement(panorama)
        
        # 移除黑边
        panorama = self._remove_black_borders_sequential(panorama)
        
        # 如果设置了目标高度，确保最终全景图符合目标高度
        if self.target_height is not None and panorama is not None:
            h, w = panorama.shape[:2]
            if h != self.target_height:
                scale = self.target_height / h
                new_width = int(w * scale)
                panorama = cv2.resize(panorama, (new_width, self.target_height), interpolation=cv2.INTER_AREA)
                print(f"最终调整全景图尺寸: {w}x{h} -> {new_width}x{self.target_height}")
        
        # 保存完整全景图
        output_file = os.path.join(output_folder, "panorama_0.jpg")
        cv2.imwrite(output_file, panorama, [cv2.IMWRITE_JPEG_QUALITY, 95])
        
        final_h, final_w = panorama.shape[:2]
        print(f"完成！创建了完整全景图: {final_w}x{final_h} - {output_file}")
        
        return [output_file]
    
    def create_google_streetview(self, image_folder, step=3, output_folder='output', target_height=None):
        """创建Google街景风格的全景图"""
        os.makedirs(output_folder, exist_ok=True)
        
        # 如果传入了目标高度，更新实例变量
        if target_height is not None:
            self.target_height = target_height
        
        # 加载图像
        extensions = ['jpg', 'jpeg', 'png', 'bmp', 'tiff']
        image_paths = []
        for ext in extensions:
            image_paths.extend(glob(os.path.join(image_folder, f'*.{ext}')))
            image_paths.extend(glob(os.path.join(image_folder, f'*.{ext.upper()}')))
        
        image_paths.sort()
        
        if not image_paths:
            print("未找到图像文件")
            return []
        
        print(f"加载 {len(image_paths)} 张图像...")
        images = []
        
        # 如果设置了目标高度，先收集所有图像尺寸信息
        if self.target_height is not None:
            print(f"统一高度处理: {self.target_height}px")
        
        for path in tqdm(image_paths):
            img = cv2.imread(path)
            if img is not None:
                h, w = img.shape[:2]
                
                # 如果设置了目标高度，统一所有图像高度
                if self.target_height is not None:
                    if h != self.target_height:
                        scale = self.target_height / h
                        new_width = int(w * scale)
                        img = cv2.resize(img, (new_width, self.target_height), interpolation=cv2.INTER_AREA)
                        print(f"  调整图像: {w}x{h} -> {new_width}x{self.target_height}")
                else:
                    # 如果没有设置目标高度，移除原来的宽度限制，保持原始尺寸
                    # 只在图像过大时适当缩小以提高处理效率
                    if w > 2400:  # 提高限制到2400像素
                        scale = 2400.0 / w
                        new_w = 2400
                        new_h = int(h * scale)
                        img = cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_AREA)
                        print(f"  缩放过大图像: {w}x{h} -> {new_w}x{new_h}")
                
                images.append(img)
        
        if not images:
            print("未能加载任何图像")
            return []
        
        # 创建全景图序列
        panorama_files = []
        total_groups = (len(images) + step - 1) // step
        
        for i in range(0, len(images), step):
            group_num = i // step + 1
            print(f"创建全景图 {group_num}/{total_groups}...")
            
            end = min(i + step, len(images))
            subset = images[i:end]
            
            if len(subset) < 2:
                # 如果只有一张图，直接保存
                if len(subset) == 1:
                    output_file = os.path.join(output_folder, f"panorama_{i//step}.jpg")
                    cv2.imwrite(output_file, subset[0], [cv2.IMWRITE_JPEG_QUALITY, 95])
                    panorama_files.append(output_file)
                continue
            
            # 拼接当前组的图像
            panorama = subset[0]
            
            for j in range(1, len(subset)):
                print(f"  拼接图像 {j+1}/{len(subset)}...")
                result = self.stitch_two_images_natural(panorama, subset[j])
                
                if result is not None:
                    panorama = result
                else:
                    print(f"  图像 {j+1} 拼接失败，尝试反向...")
                    result = self.stitch_two_images_natural(subset[j], panorama)
                    if result is not None:
                        panorama = result
                    else:
                        print(f"  图像 {j+1} 拼接失败")
            
            # 最终轻微增强
            panorama = self._final_enhancement(panorama)
            
            # 如果设置了目标高度，确保最终全景图符合目标高度
            if self.target_height is not None and panorama is not None:
                h, w = panorama.shape[:2]
                if h != self.target_height:
                    scale = self.target_height / h
                    new_width = int(w * scale)
                    panorama = cv2.resize(panorama, (new_width, self.target_height), interpolation=cv2.INTER_AREA)
                    print(f"  最终调整全景图尺寸: {w}x{h} -> {new_width}x{self.target_height}")
            
            # 保存全景图
            output_file = os.path.join(output_folder, f"panorama_{i//step}.jpg")
            cv2.imwrite(output_file, panorama, [cv2.IMWRITE_JPEG_QUALITY, 95])
            panorama_files.append(output_file)
        
        print(f"完成！创建了 {len(panorama_files)} 张全景图")
        return panorama_files
    
    def _final_enhancement(self, image):
        """最终轻微增强"""
        if image is None:
            return None
        
        # 轻微的锐化
        kernel = np.array([[0, -0.5, 0], [-0.5, 3, -0.5], [0, -0.5, 0]])
        sharpened = cv2.filter2D(image, -1, kernel)
        
        # 混合原图和锐化图
        result = cv2.addWeighted(image, 0.8, sharpened, 0.2, 0)
        
        return result 

    def create_enhanced_sequence(self, image_folder, step=5, output_folder='output', target_height=None):
        """
        创建增强版的可导航全景图序列
        使用改进的顺序拼接算法来创建高质量的全景图序列
        
        Args:
            image_folder (str): 图像文件夹路径
            step (int): 每张全景图包含的图像数量
            output_folder (str): 输出文件夹
            target_height (int, optional): 目标高度
            
        Returns:
            list: 全景图文件路径列表
        """
        os.makedirs(output_folder, exist_ok=True)
        
        # 如果传入了目标高度，更新实例变量
        if target_height is not None:
            self.target_height = target_height
        
        # 加载图像
        extensions = ['jpg', 'jpeg', 'png', 'bmp', 'tiff']
        image_paths = []
        for ext in extensions:
            image_paths.extend(glob(os.path.join(image_folder, f'*.{ext}')))
            image_paths.extend(glob(os.path.join(image_folder, f'*.{ext.upper()}')))
        
        image_paths.sort()
        
        if not image_paths:
            print("未找到图像文件")
            return []
        
        print(f"📁 加载 {len(image_paths)} 张图像，创建增强序列...")
        images = []
        
        # 加载和预处理图像
        if self.target_height is not None:
            print(f"📏 统一高度处理: {self.target_height}px")
        
        for path in tqdm(image_paths, desc="加载图像"):
            img = cv2.imread(path)
            if img is not None:
                h, w = img.shape[:2]
                
                # 如果设置了目标高度，统一所有图像高度
                if self.target_height is not None:
                    if h != self.target_height:
                        scale = self.target_height / h
                        new_width = int(w * scale)
                        img = cv2.resize(img, (new_width, self.target_height), interpolation=cv2.INTER_AREA)
                
                images.append(img)
        
        if not images:
            print("未能加载任何图像")
            return []
        
        # 计算合理的步长
        if len(images) < step:
            step = max(1, len(images) // 2)
            print(f"⚙️ 调整步长为: {step} (图像数量较少)")
        
        # 创建增强的全景图序列
        panorama_files = []
        total_groups = (len(images) + step - 1) // step
        
        print(f"🎯 创建 {total_groups} 张增强全景图序列")
        print("=" * 60)
        
        for i in range(0, len(images), step):
            group_num = i // step + 1
            print(f"\n📸 处理序列 {group_num}/{total_groups}")
            
            end = min(i + step, len(images))
            subset = images[i:end]
            
            print(f"   图像范围: {i+1}-{end} ({len(subset)} 张图像)")
            
            if len(subset) < 1:
                continue
            elif len(subset) == 1:
                # 如果只有一张图，直接保存
                output_file = os.path.join(output_folder, f"panorama_{i//step}.jpg")
                cv2.imwrite(output_file, subset[0], [cv2.IMWRITE_JPEG_QUALITY, 95])
                panorama_files.append(output_file)
                print(f"   💾 单张图像直接保存")
                continue
            
            # 使用增强的顺序拼接算法
            print(f"   🔧 使用增强算法拼接 {len(subset)} 张图像...")
            panorama = self._enhanced_sequential_stitch(subset, group_num)
            
            if panorama is not None:
                # 最终增强处理
                panorama = self._final_enhancement(panorama)
                
                # 如果设置了目标高度，确保最终全景图符合目标高度
                if self.target_height is not None:
                    h, w = panorama.shape[:2]
                    if h != self.target_height:
                        scale = self.target_height / h
                        new_width = int(w * scale)
                        panorama = cv2.resize(panorama, (new_width, self.target_height), interpolation=cv2.INTER_AREA)
                
                # 保存全景图
                output_file = os.path.join(output_folder, f"panorama_{i//step}.jpg")
                cv2.imwrite(output_file, panorama, [cv2.IMWRITE_JPEG_QUALITY, 95])
                panorama_files.append(output_file)
                
                h, w = panorama.shape[:2]
                file_size = os.path.getsize(output_file) / (1024 * 1024)  # MB
                print(f"   ✅ 完成! 尺寸: {w}×{h}, 大小: {file_size:.1f}MB")
            else:
                print(f"   ❌ 序列 {group_num} 拼接失败")
        
        print("\n" + "=" * 60)
        print(f"🎉 增强序列创建完成！生成了 {len(panorama_files)} 张高质量全景图")
        
        return panorama_files
    
    def _enhanced_sequential_stitch(self, images, group_num):
        """
        增强的顺序拼接算法
        使用类似create_single_panorama的高质量算法，但针对较小的图像组
        
        Args:
            images (list): 要拼接的图像列表
            group_num (int): 组号（用于日志）
            
        Returns:
            np.ndarray: 拼接后的全景图，失败时返回None
        """
        if not images:
            return None
        
        if len(images) == 1:
            return images[0]
        
        print(f"     🔄 开始顺序拼接 {len(images)} 张图像...")
        
        # 从第一张图开始
        result = images[0].copy()
        
        for i in range(1, len(images)):
            print(f"     📎 拼接图像 {i+1}/{len(images)}")
            
            # 检查相似度，避免重复图像
            if i > 0 and self.check_image_similarity_sequential(images[i-1], images[i], threshold=0.85):
                print(f"       🔍 检测到相似图像，跳过拼接")
                continue
            
            # 检测重叠区域
            overlap_width = self.detect_overlap_region_sequential(result, images[i])
            
            if overlap_width > 0:
                print(f"       🎯 检测到重叠区域: {overlap_width} 像素")
            else:
                print(f"       ➡️ 未检测到重叠，使用默认拼接")
                # 设置最小重叠以获得更好效果
                overlap_width = min(50, images[i].shape[1] // 10)
            
            # 混合拼接
            try:
                new_result = self.blend_overlap_region_sequential(result, images[i], overlap_width)
                
                if new_result is not None:
                    result = new_result
                    h, w = result.shape[:2]
                    print(f"       ✅ 拼接成功，当前尺寸: {w}×{h}")
                else:
                    print(f"       ⚠️ 拼接失败，保持原结果")
                    
            except Exception as e:
                print(f"       ❌ 拼接过程出错: {str(e)}")
                continue
        
        # 移除黑边
        result = self._remove_black_borders_sequential(result)
        
        if result is not None:
            h, w = result.shape[:2]
            print(f"     🎨 最终结果: {w}×{h}")
        
        return result 