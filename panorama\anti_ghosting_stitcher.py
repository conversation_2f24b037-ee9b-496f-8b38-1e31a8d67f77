import cv2
import numpy as np
import os
from glob import glob
from tqdm import tqdm
import re

class AntiGhostingPanoramaStitcher:
    def __init__(self, target_height=1500):
        """
        初始化防重影全景拼接器
        Args:
            target_height: 目标统一高度
        """
        self.target_height = target_height
        
        # 高精度特征检测器
        self.sift = cv2.SIFT_create(
            nfeatures=2000,  # 增加特征点数量
            contrastThreshold=0.03,  # 降低阈值获得更多特征
            edgeThreshold=20
        )
        
        # FLANN匹配器
        FLANN_INDEX_KDTREE = 1
        index_params = dict(algorithm=FLANN_INDEX_KDTREE, trees=5)
        search_params = dict(checks=100)  # 增加检查次数
        self.flann = cv2.FlannBasedMatcher(index_params, search_params)
    
    def load_images_sequentially(self, image_folder):
        """按数字顺序加载图片"""
        extensions = ['jpg', 'jpeg', 'png', 'bmp']
        image_files = []
        
        for ext in extensions:
            for pattern in [f'*.{ext}', f'*.{ext.upper()}']:
                files = glob(os.path.join(image_folder, pattern))
                image_files.extend(files)
        
        # 按文件名中的数字排序
        def extract_number(filename):
            basename = os.path.basename(filename)
            numbers = re.findall(r'\d+', basename)
            return int(numbers[0]) if numbers else 0
        
        image_files.sort(key=extract_number)
        
        # 去重处理
        unique_files = []
        seen_names = set()
        for file_path in image_files:
            name = os.path.basename(file_path)
            if name not in seen_names:
                unique_files.append(file_path)
                seen_names.add(name)
        
        print(f"发现 {len(unique_files)} 张唯一图片，按顺序加载...")
        
        images = []
        for i, img_path in enumerate(tqdm(unique_files)):
            img = cv2.imread(img_path)
            if img is not None:
                # 统一高度，保持宽高比
                h, w = img.shape[:2]
                if h != self.target_height:
                    scale = self.target_height / h
                    new_width = int(w * scale)
                    img = cv2.resize(img, (new_width, self.target_height), interpolation=cv2.INTER_AREA)
                
                images.append(img)
                print(f"  {i+1:2d}. {os.path.basename(img_path)} -> {img.shape[1]}×{img.shape[0]}")
        
        return images
    
    def precise_alignment(self, img1, img2):
        """
        精确对齐两张图像
        返回对齐后的图像和变换信息
        """
        # 特征检测
        gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)
        
        kp1, desc1 = self.sift.detectAndCompute(gray1, None)
        kp2, desc2 = self.sift.detectAndCompute(gray2, None)
        
        if desc1 is None or desc2 is None or len(kp1) < 20 or len(kp2) < 20:
            return None, None, 0
        
        # 特征匹配
        try:
            matches = self.flann.knnMatch(desc1, desc2, k=2)
            good_matches = []
            for match_pair in matches:
                if len(match_pair) == 2:
                    m, n = match_pair
                    if m.distance < 0.6 * n.distance:  # 更严格的比例测试
                        good_matches.append(m)
        except:
            return None, None, 0
        
        if len(good_matches) < 20:
            return None, None, 0
        
        # 提取匹配点
        src_pts = np.float32([kp1[m.queryIdx].pt for m in good_matches]).reshape(-1, 1, 2)
        dst_pts = np.float32([kp2[m.trainIdx].pt for m in good_matches]).reshape(-1, 1, 2)
        
        # 计算仿射变换矩阵（更稳定，避免透视变形）
        try:
            # 首先尝试单应性变换
            H, mask = cv2.findHomography(src_pts, dst_pts, 
                                       cv2.RANSAC, 
                                       ransacReprojThreshold=2.0,  # 更严格的阈值
                                       confidence=0.99,
                                       maxIters=3000)
            
            if H is not None:
                inliers = np.sum(mask)
                if inliers >= 15:
                    # 检查变换的合理性
                    if self._is_transform_reasonable(H):
                        return H, mask, inliers
            
            # 如果单应性不理想，尝试仿射变换
            A, mask_affine = cv2.estimateAffinePartial2D(src_pts, dst_pts,
                                                        ransacReprojThreshold=2.0,
                                                        confidence=0.99,
                                                        maxIters=3000)
            
            if A is not None:
                # 转换为3x3矩阵
                H = np.vstack([A, [0, 0, 1]])
                inliers = np.sum(mask_affine)
                return H, mask_affine, inliers
                
        except:
            pass
        
        return None, None, 0
    
    def _is_transform_reasonable(self, H):
        """检查变换矩阵是否合理"""
        if H is None:
            return False
        
        # 检查行列式，避免过度缩放
        det = np.linalg.det(H[:2, :2])
        if det < 0.3 or det > 3.0:
            return False
        
        # 检查透视变换的程度
        if abs(H[2, 0]) > 0.001 or abs(H[2, 1]) > 0.001:
            return False
        
        return True
    
    def detect_precise_overlap(self, img1, img2, transform_matrix):
        """
        精确检测重叠区域
        使用变换矩阵计算精确的重叠区域
        """
        h1, w1 = img1.shape[:2]
        h2, w2 = img2.shape[:2]
        
        if transform_matrix is None:
            return 0, None
        
        # 计算img2在img1坐标系中的位置
        corners2 = np.float32([[0, 0], [w2, 0], [w2, h2], [0, h2]]).reshape(-1, 1, 2)
        transformed_corners = cv2.perspectiveTransform(corners2, transform_matrix)
        
        # 计算重叠区域
        img1_rect = np.array([[0, 0], [w1, 0], [w1, h1], [0, h1]], dtype=np.float32)
        img2_transformed = transformed_corners.reshape(-1, 2)
        
        # 找到重叠的矩形区域
        x_min = max(0, np.min(img2_transformed[:, 0]))
        x_max = min(w1, np.max(img2_transformed[:, 0]))
        y_min = max(0, np.min(img2_transformed[:, 1]))
        y_max = min(h1, np.max(img2_transformed[:, 1]))
        
        if x_max > x_min and y_max > y_min:
            overlap_width = x_max - x_min
            overlap_info = {
                'x_min': x_min,
                'x_max': x_max,
                'y_min': y_min,
                'y_max': y_max,
                'width': overlap_width
            }
            return overlap_width, overlap_info
        
        return 0, None
    
    def advanced_blend_images(self, img1, img2, transform_matrix, overlap_info):
        """
        高级图像混合，减少重影
        """
        h1, w1 = img1.shape[:2]
        h2, w2 = img2.shape[:2]
        
        # 计算输出画布大小
        corners2 = np.float32([[0, 0], [w2, 0], [w2, h2], [0, h2]]).reshape(-1, 1, 2)
        transformed_corners = cv2.perspectiveTransform(corners2, transform_matrix)
        
        all_corners = np.vstack([
            [[0, 0], [w1, 0], [w1, h1], [0, h1]],
            transformed_corners.reshape(-1, 2)
        ])
        
        x_min = int(np.min(all_corners[:, 0]))
        x_max = int(np.max(all_corners[:, 0]))
        y_min = int(np.min(all_corners[:, 1]))
        y_max = int(np.max(all_corners[:, 1]))
        
        # 创建平移矩阵
        translation = np.array([[1, 0, -x_min], [0, 1, -y_min], [0, 0, 1]], dtype=np.float32)
        
        # 输出尺寸
        output_width = x_max - x_min
        output_height = y_max - y_min
        
        # 变换第二张图像
        final_transform = translation @ transform_matrix
        warped_img2 = cv2.warpPerspective(img2, final_transform, 
                                        (output_width, output_height),
                                        flags=cv2.INTER_LINEAR)
        
        # 创建输出图像
        result = np.zeros((output_height, output_width, 3), dtype=np.uint8)
        
        # 放置第一张图像
        img1_start_x = -x_min
        img1_start_y = -y_min
        img1_end_x = img1_start_x + w1
        img1_end_y = img1_start_y + h1
        
        result[img1_start_y:img1_end_y, img1_start_x:img1_end_x] = img1
        
        # 创建掩码
        mask1 = np.zeros((output_height, output_width), dtype=np.uint8)
        mask1[img1_start_y:img1_end_y, img1_start_x:img1_end_x] = 255
        
        mask2 = np.zeros((output_height, output_width), dtype=np.uint8)
        gray_warped = cv2.cvtColor(warped_img2, cv2.COLOR_BGR2GRAY)
        mask2[gray_warped > 0] = 255
        
        # 找到重叠区域
        overlap_mask = cv2.bitwise_and(mask1, mask2)
        
        if np.sum(overlap_mask) > 1000:  # 有足够的重叠区域
            # 使用泊松混合减少重影
            result = self._poisson_blend(result, warped_img2, mask1, mask2, overlap_mask)
        else:
            # 直接覆盖非重叠区域
            non_overlap = (mask2 > 0) & (mask1 == 0)
            result[non_overlap] = warped_img2[non_overlap]
        
        return result
    
    def _poisson_blend(self, img1, img2, mask1, mask2, overlap_mask):
        """
        泊松混合减少重影
        """
        result = img1.copy()
        
        # 计算混合权重 - 基于距离变换
        dist1 = cv2.distanceTransform(mask1, cv2.DIST_L2, 5)
        dist2 = cv2.distanceTransform(mask2, cv2.DIST_L2, 5)
        
        # 在重叠区域创建平滑的权重
        overlap_coords = np.where(overlap_mask > 0)
        if len(overlap_coords[0]) > 0:
            total_dist = dist1 + dist2
            weight2 = np.zeros_like(dist1, dtype=np.float32)
            
            valid_mask = total_dist > 0
            weight2[valid_mask] = dist2[valid_mask] / total_dist[valid_mask]
            
            # 应用高斯平滑使权重更平滑
            weight2 = cv2.GaussianBlur(weight2, (31, 31), 0)
            
            # 使用加权平均而不是简单混合
            for c in range(3):
                overlap_region = overlap_mask > 0
                result[overlap_region, c] = (
                    img1[overlap_region, c] * (1 - weight2[overlap_region]) +
                    img2[overlap_region, c] * weight2[overlap_region]
                ).astype(np.uint8)
        
        # 填充非重叠区域
        img2_only = (mask2 > 0) & (mask1 == 0)
        result[img2_only] = img2[img2_only]
        
        return result
    
    def create_anti_ghosting_panorama_from_images(self, images, output_file=None, debug=False):
        """
        从图像列表创建防重影全景图
        Args:
            images: 已加载的图像列表 (cv2格式)
            output_file: 输出文件路径
            debug: 是否启用调试模式
        """
        print("=" * 70)
        print("👻 防重影全景拼接 (从上传图片)")
        print("=" * 70)
        
        # 预处理上传的图片
        processed_images = []
        for i, img in enumerate(images):
            if img is not None:
                # 统一高度，保持宽高比
                h, w = img.shape[:2]
                if h != self.target_height:
                    scale = self.target_height / h
                    new_width = int(w * scale)
                    resized_img = cv2.resize(img, (new_width, self.target_height), interpolation=cv2.INTER_AREA)
                else:
                    resized_img = img.copy()
                
                processed_images.append(resized_img)
                print(f"  {i+1:2d}. 上传图片 {i+1} -> {resized_img.shape[1]}×{resized_img.shape[0]}")
        
        return self._create_anti_ghosting_from_processed_images(processed_images, output_file, debug)
    
    def create_anti_ghosting_panorama(self, image_folder, output_file=None, debug=False):
        """
        创建防重影全景图 (从文件夹)
        """
        print("=" * 70)
        print("👻 防重影全景拼接")
        print("=" * 70)
        
        # 加载图片
        images = self.load_images_sequentially(image_folder)
        
        return self._create_anti_ghosting_from_processed_images(images, output_file, debug)
    
    def _create_anti_ghosting_from_processed_images(self, images, output_file=None, debug=False):
        """
        从已处理的图像列表创建防重影全景图
        """
        if not images:
            print("❌ 未能加载任何图像")
            return None
        
        if len(images) == 1:
            return images[0]
        
        print(f"\n开始防重影拼接 {len(images)} 张图像...")
        print(f"目标高度: {self.target_height}px")
        print("-" * 70)
        
        # 从第一张图开始
        result = images[0].copy()
        
        for i in range(1, len(images)):
            print(f"\n第 {i} 步：精确拼接图像 {i+1}")
            print(f"  当前结果尺寸: {result.shape[1]}×{result.shape[0]}")
            print(f"  待拼接图像尺寸: {images[i].shape[1]}×{images[i].shape[0]}")
            
            # 精确对齐
            transform_matrix, mask, inliers = self.precise_alignment(result, images[i])
            
            if transform_matrix is not None and inliers >= 15:
                print(f"  ✅ 精确对齐成功，内点数: {inliers}")
                
                # 检测精确重叠区域
                overlap_width, overlap_info = self.detect_precise_overlap(result, images[i], transform_matrix)
                
                if overlap_width > 50:
                    print(f"  🎯 检测到重叠区域: {overlap_width:.0f} 像素")
                    # 高级混合
                    result = self.advanced_blend_images(result, images[i], transform_matrix, overlap_info)
                else:
                    print(f"  ➡️  重叠较小，使用直接拼接")
                    # 简单水平拼接
                    result = self._safe_horizontal_stitch(result, images[i])
            else:
                print(f"  ⚠️  对齐失败或质量不佳，使用安全拼接")
                result = self._safe_horizontal_stitch(result, images[i])
            
            print(f"  拼接完成，当前尺寸: {result.shape[1]}×{result.shape[0]}")
            
            # 保存中间结果（调试模式）
            if debug:
                debug_file = f"anti_ghost_step_{i:02d}.jpg"
                cv2.imwrite(debug_file, result, [cv2.IMWRITE_JPEG_QUALITY, 95])
        
        # 最终质量增强
        print(f"\n🎨 最终图像增强...")
        result = self._enhance_final_image(result)
        
        # 确保没有黑边
        result = self._remove_black_borders(result)
        
        # 保存结果
        if output_file:
            cv2.imwrite(output_file, result, [cv2.IMWRITE_JPEG_QUALITY, 95])
            print(f"✅ 防重影拼接完成，已保存到: {output_file}")
        
        return result
    
    def _safe_horizontal_stitch(self, img1, img2):
        """安全的水平拼接，最小重叠"""
        h1, w1 = img1.shape[:2]
        h2, w2 = img2.shape[:2]
        
        # 调整高度
        target_h = max(h1, h2)
        if h1 != target_h:
            img1 = cv2.resize(img1, (w1, target_h))
        if h2 != target_h:
            img2 = cv2.resize(img2, (w2, target_h))
        
        # 最小重叠
        overlap = min(30, w2 // 10)
        
        # 创建输出图像
        output_width = w1 + w2 - overlap
        result = np.zeros((target_h, output_width, 3), dtype=np.uint8)
        
        # 放置第一张图
        result[:, :w1] = img1
        
        # 混合重叠区域
        blend_start = w1 - overlap
        for i in range(overlap):
            alpha = i / overlap
            x = blend_start + i
            if x >= 0 and x < output_width and i < w2:
                result[:, x] = (img1[:, x] * (1-alpha) + img2[:, i] * alpha).astype(np.uint8)
        
        # 放置第二张图的剩余部分
        remaining_start = w1
        if remaining_start < output_width and overlap < w2:
            result[:, remaining_start:] = img2[:, overlap:]
        
        return result
    
    def _enhance_final_image(self, image):
        """最终图像增强，减少伪影"""
        if image is None:
            return None
        
        # 1. 轻微去噪
        denoised = cv2.bilateralFilter(image, 9, 75, 75)
        
        # 2. 轻微锐化
        kernel = np.array([[-0.5, -0.5, -0.5], 
                          [-0.5, 5.0, -0.5], 
                          [-0.5, -0.5, -0.5]]) / 1.5
        sharpened = cv2.filter2D(denoised, -1, kernel)
        
        # 3. 混合去噪和锐化结果
        result = cv2.addWeighted(denoised, 0.7, sharpened, 0.3, 0)
        
        # 4. 轻微对比度增强
        lab = cv2.cvtColor(result, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        clahe = cv2.createCLAHE(clipLimit=1.2, tileGridSize=(8, 8))
        l = clahe.apply(l)
        enhanced = cv2.merge([l, a, b])
        result = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
        
        return result
    
    def _remove_black_borders(self, image):
        """移除黑边"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 找到非黑色区域
        _, thresh = cv2.threshold(gray, 3, 255, cv2.THRESH_BINARY)
        
        # 找到轮廓
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return image
        
        # 找到最大轮廓的边界矩形
        largest_contour = max(contours, key=cv2.contourArea)
        x, y, w, h = cv2.boundingRect(largest_contour)
        
        # 轻微扩展边界
        border = 1
        x = max(0, x - border)
        y = max(0, y - border)
        w = min(image.shape[1] - x, w + 2*border)
        h = min(image.shape[0] - y, h + 2*border)
        
        return image[y:y+h, x:x+w] 