import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import axios from 'axios';

import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';

import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import IconButton from '@mui/material/IconButton';
import Button from '@mui/material/Button';
import Drawer from '@mui/material/Drawer';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Divider from '@mui/material/Divider';
import Fab from '@mui/material/Fab';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import TextField from '@mui/material/TextField';
import SendIcon from '@mui/icons-material/Send';
// import Box from '@mui/material/Box';
// import ReactMarkdown from 'react-markdown';

import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import NavigateBeforeIcon from '@mui/icons-material/NavigateBefore';
import FullscreenIcon from '@mui/icons-material/Fullscreen';
import ChatIcon from '@mui/icons-material/Chat';
import CloseIcon from '@mui/icons-material/Close';
import MenuIcon from '@mui/icons-material/Menu';
import RoomIcon from '@mui/icons-material/Room';
import AddLocationIcon from '@mui/icons-material/AddLocation';
import InfoIcon from '@mui/icons-material/Info';
import PhotoLibraryIcon from '@mui/icons-material/PhotoLibrary';

// 导入新的Street View查看器组件
import StreetViewPanorama from '../components/StreetViewPanorama';

const SceneViewer = () => {
  const { id } = useParams();
  const [scene, setScene] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPanoIndex, setCurrentPanoIndex] = useState(0);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [chatOpen, setChatOpen] = useState(false);
    const [hotspots, setHotspots] = useState([]);
  const [isConfiguring, setIsConfiguring] = useState(false);
  const [isAdding, setIsAdding] = useState(false);
  
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedHotspot, setSelectedHotspot] = useState(null);

  // 新增对话相关状态
  const [chatMessages, setChatMessages] = useState([]);
  const [userQuestion, setUserQuestion] = useState('');
  const [isSending, setIsSending] = useState(false);

  // 平铺查看器状态
  const [imagePosition, setImagePosition] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, imagePos: 0 });
  const [imageWidth, setImageWidth] = useState(0);
  const [containerWidth, setContainerWidth] = useState(0);
  const [isImageFullscreen, setIsImageFullscreen] = useState(false);
  
  // Street View 模式状态
  const [useStreetViewMode, setUseStreetViewMode] = useState(false);

  useEffect(() => {
    const fetchSceneData = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`/api/scenes/${id}`);
        setScene(response.data);

        // 根据场景类型设置Street View模式
        const isStreetViewScene = response.data.type === 'streetview';
        setUseStreetViewMode(isStreetViewScene);

        // Initialize hotspots (in real app, these would be loaded from API)
        setHotspots([]);
      } catch (err) {
        setError('Failed to load scene: ' + (err.response?.data?.message || err.message));
      } finally {
        setLoading(false);
      }
    };

    fetchSceneData();
  }, [id]);

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      if (!document.fullscreenElement) {
        setIsImageFullscreen(false);
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  useEffect(() => {
    const loadHotspots = async () => {
        try {
            const response = await axios.get(
                `/api/scenes/${id}/hotspots?pano_index=${currentPanoIndex}`
            );
            setHotspots(response.data);
        } catch (err) {
            console.error('Failed to load initial hotspots:', err);
        }
    };

    if (scene && scene.panoramas && scene.panoramas.length > 0) {
        loadHotspots();
    }
}, [id, currentPanoIndex, scene]);

  // Handle scene navigation for street view mode
  const handleNextPano = () => {
    if (scene && scene.panoramas && currentPanoIndex < scene.panoramas.length - 1) {
      setCurrentPanoIndex(currentPanoIndex + 1);
    }
  };

  const handlePrevPano = () => {
    if (scene && scene.panoramas && currentPanoIndex > 0) {
      setCurrentPanoIndex(currentPanoIndex - 1);
    }
  };

  const handleDrawerToggle = () => {
    setDrawerOpen(!drawerOpen);
  };

  const handleChatToggle = () => {
    setChatOpen(!chatOpen);
  };

  // 平铺查看器事件处理函数
  const handleImageLoad = (e) => {
    const img = e.target;
    setImageWidth(img.offsetWidth);
    setContainerWidth(img.parentElement.offsetWidth);
    // 居中图像
    if (img.offsetWidth > img.parentElement.offsetWidth) {
      const centerOffset = (img.offsetWidth - img.parentElement.offsetWidth) / 2;
      setImagePosition(-centerOffset);
    } else {
      setImagePosition(0);
    }
  };

  const handleMouseDown = (e) => {
    setIsDragging(true);
    setDragStart({
      x: e.clientX,
      imagePos: imagePosition
    });
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    
    const deltaX = e.clientX - dragStart.x;
    const newPosition = dragStart.imagePos + deltaX;
    
    // 限制拖拽范围
    const maxLeft = 0;
    const maxRight = -(imageWidth - containerWidth);
    const constrainedPosition = Math.max(maxRight, Math.min(maxLeft, newPosition));
    
    setImagePosition(constrainedPosition);
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleTouchStart = (e) => {
    setIsDragging(true);
    setDragStart({
      x: e.touches[0].clientX,
      imagePos: imagePosition
    });
  };

  const handleTouchMove = (e) => {
    if (!isDragging) return;
    e.preventDefault();
    
    const deltaX = e.touches[0].clientX - dragStart.x;
    const newPosition = dragStart.imagePos + deltaX;
    
    // 限制拖拽范围
    const maxLeft = 0;
    const maxRight = -(imageWidth - containerWidth);
    const constrainedPosition = Math.max(maxRight, Math.min(maxLeft, newPosition));
    
    setImagePosition(constrainedPosition);
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
  };

  // 新增对话相关函数
  const handleQuestionSubmit = async (e) => {
    e.preventDefault();
    if (!userQuestion.trim() || isSending) return;

    try {
      setIsSending(true);

      // 添加用户问题到消息列表
      const newUserMessage = {
        id: Date.now(),
        sender: 'user',
        text: userQuestion,
        timestamp: new Date()
      };

      setChatMessages(prev => [...prev, newUserMessage]);

      // 清空输入框
      setUserQuestion('');

      // 调用后端API获取回答
      const response = await axios.post(`/api/scenes/${id}/chat?message=${encodeURIComponent(userQuestion)}`);

      // 添加AI回答到消息列表
      const aiMessage = {
        id: Date.now() + 1,
        sender: 'ai',
        text: response.data.response,
        timestamp: new Date()
      };

      setChatMessages(prev => [...prev, aiMessage]);
    } catch (err) {
      console.error('Failed to send message:', err);
      const errorMessage = {
        id: Date.now() + 1,
        sender: 'ai',
        text: '抱歉，获取回答时出现错误，请稍后重试。',
        timestamp: new Date()
      };
      setChatMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsSending(false);
    }
  };

  const handlePanoSelect = async (index) => {
    setCurrentPanoIndex(index);
    setDrawerOpen(false);

    try {
        // 加载对应全景图的热点数据
        const response = await axios.get(
            `/api/scenes/${scene.id}/hotspots?pano_index=${index}`
        );
        setHotspots(response.data);
    } catch (err) {
        console.error('Failed to load hotspots:', err);
    }
};

  // Hotspot functionality - 暂时禁用
  const handleAddHotspot = () => {
    // 暂时禁用热点添加功能
    console.log('热点功能暂时禁用，需要重新设计坐标系统');
  };

  const handleHotspotClick = (evt, hotspotData) => {
    setSelectedHotspot(hotspotData);
    setOpenDialog(true);
  };



  // Handle image fullscreen
  const handleFullscreen = () => {
    if (!document.fullscreenElement) {
      // 进入全屏模式
      document.documentElement.requestFullscreen().then(() => {
        setIsImageFullscreen(true);
      }).catch(err => {
        console.log(`Error attempting to enable fullscreen: ${err.message}`);
      });
    } else {
      // 退出全屏模式
      handleExitFullscreen();
    }
  };

  // 退出全屏的专用函数
  const handleExitFullscreen = () => {
    document.exitFullscreen().then(() => {
      setIsImageFullscreen(false);
    }).catch(err => {
      console.log(`Error attempting to exit fullscreen: ${err.message}`);
    });
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  if (!scene) {
    return <Alert severity="warning">No scene found</Alert>;
  }

  const isStreetView = scene.type === 'streetview' && scene.panoramas && scene.panoramas.length > 1;
  const currentPanoUrl = scene.panoramas && scene.panoramas.length > 0
    ? `/static/${scene.panoramas[currentPanoIndex]}`
    : '';

  return (
    <Box sx={{ position: 'relative', height: '85vh' }}>
      <Paper elevation={3} sx={{ height: '100%', overflow: 'hidden', position: 'relative' }}>
        {/* Scene name and type */}
        <Box sx={{ 
          position: 'absolute', 
          top: 0, 
          left: 0, 
          right: 0, 
          zIndex: 10, 
          bgcolor: 'rgba(0,0,0,0.4)', 
          color: 'white',
          p: 1,
          display: 'flex',
          justifyContent: 'space-between'
        }}>
          <Box>
            <Typography variant="h6">{scene.name}</Typography>
            <Typography variant="caption">
              {scene.type === 'panorama' ? 'Panorama' : 'Street View Mode'}
            </Typography>
          </Box>
          <IconButton color="inherit" onClick={handleDrawerToggle}>
            <MenuIcon />
          </IconButton>
        </Box>
        
        {/* Panorama viewer */}
        <Box sx={{ height: '100%' }}>
          {currentPanoUrl && (
            <>
              {useStreetViewMode ? (
                // Street View模式 - 使用新的全景图查看器
                <Box
                  sx={{
                    width: '100%',
                    height: '100%',
                    position: isImageFullscreen ? 'fixed' : 'relative',
                    top: isImageFullscreen ? 0 : 'auto',
                    left: isImageFullscreen ? 0 : 'auto',
                    right: isImageFullscreen ? 0 : 'auto',
                    bottom: isImageFullscreen ? 0 : 'auto',
                    zIndex: isImageFullscreen ? 9999 : 'auto',
                  }}
                >
                  <StreetViewPanorama
                    imageUrl={currentPanoUrl}
                    isFullscreen={isImageFullscreen}
                    onExitFullscreen={handleExitFullscreen}
                    currentIndex={currentPanoIndex}
                    totalImages={scene.panoramas ? scene.panoramas.length : 1}
                    onNavigate={(direction) => {
                      // 处理Street View导航
                      if (direction === 'next') {
                        handleNextPano();
                      } else if (direction === 'prev') {
                        handlePrevPano();
                      }
                    }}
                  />
                </Box>
              ) : (
                // 传统模式 - 使用原来的图像查看器
                <Box 
                  sx={{ 
                    width: '100%', 
                    height: '100%', 
                    overflow: 'hidden',
                    position: isImageFullscreen ? 'fixed' : 'relative',
                    top: isImageFullscreen ? 0 : 'auto',
                    left: isImageFullscreen ? 0 : 'auto',
                    right: isImageFullscreen ? 0 : 'auto',
                    bottom: isImageFullscreen ? 0 : 'auto',
                    zIndex: isImageFullscreen ? 9999 : 'auto',
                    bgcolor: isImageFullscreen ? 'black' : 'transparent',
                    cursor: 'grab',
                    '&:active': {
                      cursor: 'grabbing'
                    }
                  }}
                  onMouseDown={handleMouseDown}
                  onMouseMove={handleMouseMove}
                  onMouseUp={handleMouseUp}
                  onMouseLeave={handleMouseUp}
                  onTouchStart={handleTouchStart}
                  onTouchMove={handleTouchMove}
                  onTouchEnd={handleTouchEnd}
                >
                  <img
                    src={currentPanoUrl}
                    alt="Panorama"
                    style={{
                      height: '100%',
                      maxWidth: 'none',
                      userSelect: 'none',
                      position: 'absolute',
                      left: imagePosition,
                      top: 0,
                      transition: isDragging ? 'none' : 'left 0.2s ease-out'
                    }}
                    draggable={false}
                    onLoad={handleImageLoad}
                  />
                  
                  {/* 退出全屏按钮 - 只在全屏模式下显示 */}
                  {isImageFullscreen && (
                    <IconButton
                      onClick={handleExitFullscreen}
                      sx={{
                        position: 'absolute',
                        top: 16,
                        right: 16,
                        bgcolor: 'rgba(0,0,0,0.5)',
                        color: 'white',
                        zIndex: 10,
                        '&:hover': {
                          bgcolor: 'rgba(0,0,0,0.7)'
                        }
                      }}
                    >
                      <CloseIcon />
                    </IconButton>
                  )}
                  
                  {/* 热点显示 - 暂时隐藏，因为需要重新设计坐标系统 */}
                  {false && hotspots
                    .filter(spot => spot.panorama_index === currentPanoIndex)
                    .map(spot => (
                      <Box
                        key={spot.id}
                        sx={{
                          position: 'absolute',
                          left: `${spot.x || 50}%`,
                          top: `${spot.y || 50}%`,
                          transform: 'translate(-50%, -50%)',
                          zIndex: 10,
                          cursor: 'pointer'
                        }}
                        onClick={() => handleHotspotClick(null, spot)}
                      >
                        <Box
                          sx={{
                            width: 20,
                            height: 20,
                            borderRadius: '50%',
                            bgcolor: 'primary.main',
                            border: '2px solid white',
                            boxShadow: 2,
                            '&:hover': {
                              transform: 'scale(1.2)',
                              transition: 'transform 0.2s'
                            }
                          }}
                        />
                        {spot.text && (
                          <Typography
                            variant="caption"
                            sx={{
                              position: 'absolute',
                              top: -30,
                              left: '50%',
                              transform: 'translateX(-50%)',
                              bgcolor: 'rgba(0,0,0,0.7)',
                              color: 'white',
                              px: 1,
                              py: 0.5,
                              borderRadius: 1,
                              whiteSpace: 'nowrap'
                            }}
                          >
                            {spot.text}
                          </Typography>
                        )}
                      </Box>
                    ))
                  }
                </Box>
              )}
            </>
          )}
        </Box>

        {/* Navigation controls for street view - 在Street View模式下调整位置 */}
        {isStreetView && (
          <Box sx={{ 
            position: 'absolute', 
            bottom: useStreetViewMode ? 80 : 16, 
            left: 0, 
            right: 0, 
            display: 'flex', 
            justifyContent: 'center', 
            zIndex: useStreetViewMode ? 5 : 10 
          }}>
            <Paper elevation={4} sx={{ 
              bgcolor: useStreetViewMode ? 'rgba(0,0,0,0.7)' : 'rgba(255,255,255,0.8)', 
              color: useStreetViewMode ? 'white' : 'inherit',
              px: 2, 
              py: 1, 
              borderRadius: 8, 
              display: 'flex', 
              alignItems: 'center' 
            }}>
              <IconButton 
                onClick={handlePrevPano} 
                disabled={currentPanoIndex === 0}
                color="primary"
                sx={{ color: useStreetViewMode ? 'white' : 'primary.main' }}
              >
                <NavigateBeforeIcon />
              </IconButton>
              
              <Typography sx={{ mx: 2 }}>
                {currentPanoIndex + 1} / {scene.panoramas.length}
              </Typography>
              
              <IconButton 
                onClick={handleNextPano} 
                disabled={currentPanoIndex === scene.panoramas.length - 1}
                color="primary"
                sx={{ color: useStreetViewMode ? 'white' : 'primary.main' }}
              >
                <NavigateNextIcon />
              </IconButton>
            </Paper>
          </Box>
        )}

        {/* Floating action buttons - 在Street View模式下部分隐藏 */}
        {!useStreetViewMode && (
          <Box sx={{ position: 'absolute', bottom: 16, right: 16, display: 'flex', flexDirection: 'column', gap: 1, zIndex: 10 }}>
            <Fab color="primary" size="medium" onClick={handleFullscreen}>
              <FullscreenIcon />
            </Fab>
            <Fab color="primary" size="medium" onClick={handleChatToggle}>
              <ChatIcon />
            </Fab>
            {/* 热点添加按钮暂时隐藏 */}
            {false && (
              <Fab color="secondary" size="medium" onClick={handleAddHotspot}>
                <AddLocationIcon />
              </Fab>
            )}
          </Box>
        )}
        
        {/* Street View模式下的控制按钮 */}
        {useStreetViewMode && !isImageFullscreen && (
          <Box sx={{ 
            position: 'absolute', 
            bottom: 16, 
            right: 100, 
            display: 'flex',
            flexDirection: 'column',
            gap: 1,
            zIndex: 10 
          }}>
            <Fab 
              color="primary" 
              size="medium" 
              onClick={handleFullscreen}
            >
              <FullscreenIcon />
            </Fab>
            <Fab 
              color="primary" 
              size="medium" 
              onClick={handleChatToggle}
            >
              <ChatIcon />
            </Fab>
          </Box>
        )}
        
        {/* Chat/Info panel */}
        <Drawer
          anchor="right"
          open={chatOpen}
          onClose={handleChatToggle}
          sx={{ zIndex: 1300 }}
          PaperProps={{
            sx: { width: 320 }
          }}
        >
          <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">场景对话</Typography>
            <IconButton onClick={handleChatToggle}>
              <CloseIcon />
            </IconButton>
          </Box>
          <Divider />
          
          {/* Chat messages */}
          <Box sx={{ 
            p: 2, 
            height: 'calc(100% - 120px)', 
            overflowY: 'auto',
            display: 'flex',
            flexDirection: 'column'
          }}>
            {chatMessages.length === 0 ? (
              <Box sx={{ 
                display: 'flex', 
                justifyContent: 'center', 
                alignItems: 'center', 
                height: '100%',
                textAlign: 'center'
              }}>
                <Typography variant="body2" color="text.secondary">
                  基础图像分析功能 - 可以获取场景的基本信息
                </Typography>
              </Box>
            ) : (
              chatMessages.map((message) => (
                <Box 
                  key={message.id} 
                  sx={{ 
                    mb: 2,
                    display: 'flex',
                    justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start'
                  }}
                >
                  <Card 
                    variant="outlined" 
                    sx={{ 
                      maxWidth: '80%',
                      bgcolor: message.sender === 'user' ? 'primary.light' : 'grey.100'
                    }}
                  >
                    <CardContent sx={{ py: 1, px: 2 }}>
                      <Typography variant="body2">
                        {message.text}
                      </Typography>
                    </CardContent>
                  </Card>
                </Box>
              ))
            )}
          </Box>
          
          {/* Chat input */}
          <Box sx={{ p: 2, borderTop: '1px solid rgba(0, 0, 0, 0.12)' }} component="form" onSubmit={handleQuestionSubmit}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <TextField
                fullWidth
                size="small"
                variant="outlined"
                placeholder="输入问题 (如: 这里是什么?)"
                value={userQuestion}
                onChange={(e) => setUserQuestion(e.target.value)}
                disabled={isSending}
              />
              <IconButton 
                type="submit"
                color="primary"
                disabled={isSending || !userQuestion.trim()}
              >
                <SendIcon />
              </IconButton>
            </Box>
          </Box>
        </Drawer>
        
        {/* Info panel */}
        <Drawer
          anchor="left"
          open={drawerOpen}
          onClose={handleDrawerToggle}
          sx={{ zIndex: 1200 }}
        >
          <Box sx={{ width: 250, display: 'flex', flexDirection: 'column', height: '100%' }}>
            <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h6">场景导航</Typography>
              <IconButton onClick={handleDrawerToggle}>
                <CloseIcon />
              </IconButton>
            </Box>
            <Divider />
            
            <Box sx={{ p: 2, flexGrow: 1, overflowY: 'auto' }}>
              <Card variant="outlined" sx={{ mb: 2 }}>
                <CardContent>
                  <Typography variant="subtitle2" color="text.secondary">场景名称</Typography>
                  <Typography variant="body1" gutterBottom>{scene.name}</Typography>
                  <Typography variant="subtitle2" color="text.secondary">创建时间</Typography>
                  <Typography variant="body1" gutterBottom>{scene.created_at}</Typography>
                  <Typography variant="subtitle2" color="text.secondary">类型</Typography>
                  <Typography variant="body1">{scene.type === 'panorama' ? '全景图' : '街景模式'}</Typography>
                </CardContent>
              </Card>

              <Typography variant="h6" gutterBottom>热点列表</Typography>
              
              {hotspots.length > 0 ? (
                <List>
                  {hotspots.map((hotspot) => (
                    <ListItem key={hotspot.id} disablePadding>
                      <ListItemButton>
                        <ListItemIcon>
                          <RoomIcon />
                        </ListItemIcon>
                        <ListItemText 
                          primary={hotspot.name || hotspot.text} 
                          secondary={hotspot.text}
                        />
                      </ListItemButton>
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ p: 2, textAlign: 'center' }}>
                  当前场景无热点
                </Typography>
              )}
              
              {isStreetView && (
                <>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="h6" gutterBottom>全景图列表</Typography>
                  <List>
                    {scene.panoramas.map((pano, index) => (
                      <ListItem key={index} disablePadding>
                        <ListItemButton 
                          selected={index === currentPanoIndex}
                          onClick={() => handlePanoSelect(index)}
                        >
                          <ListItemIcon>
                            <PhotoLibraryIcon />
                          </ListItemIcon>
                          <ListItemText 
                            primary={`全景图 ${index + 1}`} 
                            secondary={index === currentPanoIndex ? "当前视图" : ""}
                          />
                        </ListItemButton>
                      </ListItem>
                    ))}
                  </List>
                </>
              )}
            </Box>
            
            <Divider />
            <Box sx={{ p: 2 }}>
              <Button 
                fullWidth 
                variant="outlined" 
                startIcon={<InfoIcon />}
                onClick={() => {
                  // 可以添加更多信息展示逻辑
                }}
              >
                更多信息
              </Button>
            </Box>
          </Box>
        </Drawer>
        
        {/* Hotspot dialog */}
        <Dialog 
          open={openDialog} 
          onClose={() => setOpenDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          {selectedHotspot && (
            <>
              <DialogTitle>
                {selectedHotspot.name || selectedHotspot.text}
              </DialogTitle>
              <DialogContent>
                <Typography variant="body1">
                  {selectedHotspot.description || selectedHotspot.text}
                </Typography>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setOpenDialog(false)}>关闭</Button>
              </DialogActions>
            </>
          )}
        </Dialog>
      </Paper>
    </Box>
  );
};

export default SceneViewer;
