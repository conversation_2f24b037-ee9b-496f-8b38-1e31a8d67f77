import cv2
import numpy as np
import os
from glob import glob
from tqdm import tqdm
import re

class SequentialPanoramaStitcher:
    def __init__(self, target_height=1500):
        """
        初始化按顺序拼接的全景拼接器
        Args:
            target_height: 目标统一高度
        """
        self.target_height = target_height
        
        # 特征检测器 - 用于检测重叠区域
        self.sift = cv2.SIFT_create(
            nfeatures=1000,
            contrastThreshold=0.04,
            edgeThreshold=15
        )
        
        # FLANN匹配器
        FLANN_INDEX_KDTREE = 1
        index_params = dict(algorithm=FLANN_INDEX_KDTREE, trees=5)
        search_params = dict(checks=50)
        self.flann = cv2.FlannBasedMatcher(index_params, search_params)
    
    def load_images_sequentially(self, image_folder):
        """按数字顺序加载图片"""
        extensions = ['jpg', 'jpeg', 'png', 'bmp']
        image_files = []
        
        for ext in extensions:
            for pattern in [f'*.{ext}', f'*.{ext.upper()}']:
                files = glob(os.path.join(image_folder, pattern))
                image_files.extend(files)
        
        # 按文件名中的数字排序
        def extract_number(filename):
            basename = os.path.basename(filename)
            numbers = re.findall(r'\d+', basename)
            return int(numbers[0]) if numbers else 0
        
        image_files.sort(key=extract_number)
        
        print(f"发现 {len(image_files)} 张图片，按顺序加载...")
        
        images = []
        for i, img_path in enumerate(tqdm(image_files)):
            img = cv2.imread(img_path)
            if img is not None:
                # 统一高度，保持宽高比
                h, w = img.shape[:2]
                if h != self.target_height:
                    scale = self.target_height / h
                    new_width = int(w * scale)
                    img = cv2.resize(img, (new_width, self.target_height), interpolation=cv2.INTER_AREA)
                
                images.append(img)
                print(f"  {i+1:2d}. {os.path.basename(img_path)} -> {img.shape[1]}×{img.shape[0]}")
        
        return images
    
    def detect_overlap_region(self, img1, img2, overlap_threshold=0.3):
        """
        检测两张图片的重叠区域
        Returns:
            overlap_width: 重叠宽度，如果没有重叠返回0
        """
        # 特征检测
        gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)
        
        kp1, desc1 = self.sift.detectAndCompute(gray1, None)
        kp2, desc2 = self.sift.detectAndCompute(gray2, None)
        
        if desc1 is None or desc2 is None or len(kp1) < 10 or len(kp2) < 10:
            return 0
        
        # 特征匹配
        try:
            matches = self.flann.knnMatch(desc1, desc2, k=2)
            good_matches = []
            for match_pair in matches:
                if len(match_pair) == 2:
                    m, n = match_pair
                    if m.distance < 0.7 * n.distance:
                        good_matches.append(m)
        except:
            return 0
        
        if len(good_matches) < 10:
            return 0
        
        # 分析匹配点的位置分布
        img1_pts = [kp1[m.queryIdx].pt for m in good_matches]
        img2_pts = [kp2[m.trainIdx].pt for m in good_matches]
        
        # 计算img1右侧和img2左侧的匹配点
        img1_width = img1.shape[1]
        img2_width = img2.shape[1]
        
        # img1右侧1/3区域的匹配点
        img1_right_matches = [pt for pt in img1_pts if pt[0] > img1_width * 0.7]
        # img2左侧1/3区域的匹配点
        img2_left_matches = [pt for pt in img2_pts if pt[0] < img2_width * 0.3]
        
        if len(img1_right_matches) < 5 or len(img2_left_matches) < 5:
            return 0
        
        # 估算重叠宽度
        avg_img1_right_x = np.mean([pt[0] for pt in img1_right_matches])
        avg_img2_left_x = np.mean([pt[0] for pt in img2_left_matches])
        
        # 重叠宽度 = img1右侧距离边缘的距离 + img2左侧匹配点的位置
        overlap_width = (img1_width - avg_img1_right_x) + avg_img2_left_x
        overlap_width = max(0, min(overlap_width, min(img1_width, img2_width) * 0.5))
        
        print(f"    检测到重叠宽度: {overlap_width:.0f} 像素")
        return int(overlap_width)
    
    def blend_overlap_region(self, img1, img2, overlap_width):
        """混合重叠区域"""
        if overlap_width <= 0:
            return np.hstack([img1, img2])
        
        h1, w1 = img1.shape[:2]
        h2, w2 = img2.shape[:2]
        
        # 确保高度一致
        target_h = max(h1, h2)
        if h1 != target_h:
            img1 = cv2.resize(img1, (w1, target_h))
        if h2 != target_h:
            img2 = cv2.resize(img2, (w2, target_h))
        
        # 计算输出尺寸
        output_width = w1 + w2 - overlap_width
        result = np.zeros((target_h, output_width, 3), dtype=np.uint8)
        
        # 放置第一张图
        result[:, :w1] = img1
        
        # 处理重叠区域
        blend_start = w1 - overlap_width
        blend_end = w1
        
        if blend_start >= 0 and overlap_width > 0:
            # 创建渐变混合
            for i in range(overlap_width):
                if blend_start + i < output_width and i < w2:
                    # 线性插值权重
                    alpha = i / overlap_width
                    
                    # 混合像素
                    result[:, blend_start + i] = (
                        img1[:, blend_start + i] * (1 - alpha) +
                        img2[:, i] * alpha
                    ).astype(np.uint8)
        
        # 放置第二张图的非重叠部分
        remaining_start = w1
        remaining_img2_start = overlap_width
        
        if remaining_start < output_width and remaining_img2_start < w2:
            remaining_width = min(output_width - remaining_start, w2 - remaining_img2_start)
            result[:, remaining_start:remaining_start+remaining_width] = img2[:, remaining_img2_start:remaining_img2_start+remaining_width]
        
        return result
    
    def check_image_similarity(self, img1, img2, threshold=0.8):
        """检查两张图片的相似度"""
        # 调整到相同尺寸进行比较
        h, w = 100, 100  # 缩略图比较
        img1_small = cv2.resize(img1, (w, h))
        img2_small = cv2.resize(img2, (w, h))
        
        # 转为灰度图
        gray1 = cv2.cvtColor(img1_small, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(img2_small, cv2.COLOR_BGR2GRAY)
        
        # 计算结构相似性
        mean1, mean2 = np.mean(gray1), np.mean(gray2)
        std1, std2 = np.std(gray1), np.std(gray2)
        
        if std1 == 0 or std2 == 0:
            return False
        
        # 计算相关系数
        correlation = np.corrcoef(gray1.flatten(), gray2.flatten())[0, 1]
        
        return correlation > threshold
    
    def enhance_image_quality(self, image):
        """增强图像质量"""
        # 轻微锐化
        kernel = np.array([[-0.5, -1, -0.5], 
                          [-1, 6, -1], 
                          [-0.5, -1, -0.5]]) / 2
        sharpened = cv2.filter2D(image, -1, kernel)
        result = cv2.addWeighted(image, 0.8, sharpened, 0.2, 0)
        
        # 轻微对比度增强
        lab = cv2.cvtColor(result, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        clahe = cv2.createCLAHE(clipLimit=1.5, tileGridSize=(8, 8))
        l = clahe.apply(l)
        enhanced = cv2.merge([l, a, b])
        result = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
        
        return result
    
    def create_sequential_panorama_from_images(self, images, output_file=None, debug=False):
        """
        从图像列表创建按顺序拼接的全景图
        Args:
            images: 已加载的图像列表 (cv2格式)
            output_file: 输出文件路径
            debug: 是否启用调试模式
        """
        print("=" * 70)
        print("🔄 按顺序拼接全景图 (从上传图片)")
        print("=" * 70)
        
        # 预处理上传的图片
        processed_images = []
        for i, img in enumerate(images):
            if img is not None:
                # 统一高度，保持宽高比
                h, w = img.shape[:2]
                if h != self.target_height:
                    scale = self.target_height / h
                    new_width = int(w * scale)
                    resized_img = cv2.resize(img, (new_width, self.target_height), interpolation=cv2.INTER_AREA)
                else:
                    resized_img = img.copy()
                
                processed_images.append(resized_img)
                print(f"  {i+1:2d}. 上传图片 {i+1} -> {resized_img.shape[1]}×{resized_img.shape[0]}")
        
        return self._create_panorama_from_processed_images(processed_images, output_file, debug)
    
    def create_sequential_panorama(self, image_folder, output_file=None, debug=False):
        """
        创建按顺序拼接的全景图 (从文件夹)
        """
        print("=" * 70)
        print("🔄 按顺序拼接全景图")
        print("=" * 70)
        
        # 加载图片
        images = self.load_images_sequentially(image_folder)
        
        return self._create_panorama_from_processed_images(images, output_file, debug)
    
    def _create_panorama_from_processed_images(self, images, output_file=None, debug=False):
        """
        从已处理的图像列表创建全景图
        """
        if not images:
            print("❌ 未能加载任何图像")
            return None
        
        if len(images) == 1:
            return images[0]
        
        print(f"\n开始按顺序拼接 {len(images)} 张图像...")
        print(f"目标高度: {self.target_height}px")
        print("-" * 70)
        
        # 从第一张图开始
        result = images[0].copy()
        
        for i in range(1, len(images)):
            print(f"\n第 {i} 步：拼接图像 {i+1}")
            print(f"  当前结果尺寸: {result.shape[1]}×{result.shape[0]}")
            print(f"  待拼接图像尺寸: {images[i].shape[1]}×{images[i].shape[0]}")
            
            # 检查相似度
            if i > 0 and self.check_image_similarity(images[i-1], images[i]):
                print(f"  检测到相似图像，跳过拼接")
                continue
            
            # 检测重叠区域
            overlap_width = self.detect_overlap_region(result, images[i])
            
            if overlap_width > 0:
                print(f"  检测到重叠区域，使用混合拼接")
            else:
                print(f"  未检测到重叠，使用直接拼接")
                # 设置最小重叠以获得更好效果
                overlap_width = min(50, images[i].shape[1] // 10)
            
            # 混合拼接
            result = self.blend_overlap_region(result, images[i], overlap_width)
            
            print(f"  拼接完成，当前尺寸: {result.shape[1]}×{result.shape[0]}")
            
            # 保存中间结果（调试模式）
            if debug:
                debug_file = f"debug_step_{i:02d}.jpg"
                cv2.imwrite(debug_file, result, [cv2.IMWRITE_JPEG_QUALITY, 95])
        
        # 最终质量增强
        print(f"\n🎨 最终图像增强...")
        result = self.enhance_image_quality(result)
        
        # 确保没有黑边
        result = self._remove_black_borders(result)
        
        # 保存结果
        if output_file:
            cv2.imwrite(output_file, result, [cv2.IMWRITE_JPEG_QUALITY, 95])
            print(f"✅ 拼接完成，已保存到: {output_file}")
        
        return result
    
    def _remove_black_borders(self, image):
        """移除黑边"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 找到非黑色区域
        _, thresh = cv2.threshold(gray, 5, 255, cv2.THRESH_BINARY)
        
        # 找到轮廓
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return image
        
        # 找到最大轮廓的边界矩形
        largest_contour = max(contours, key=cv2.contourArea)
        x, y, w, h = cv2.boundingRect(largest_contour)
        
        # 轻微扩展边界以确保不丢失内容
        border = 2
        x = max(0, x - border)
        y = max(0, y - border)
        w = min(image.shape[1] - x, w + 2*border)
        h = min(image.shape[0] - y, h + 2*border)
        
        return image[y:y+h, x:x+w] 