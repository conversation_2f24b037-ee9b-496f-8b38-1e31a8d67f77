/* Street View Viewer Styles */
.viewer-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #000;
}

.viewer-header {
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 10;
}

.viewer-title h1 {
    font-size: 1.5rem;
    margin: 0;
    color: white;
}

.back-link {
    color: #ccc;
    text-decoration: none;
    font-size: 0.9rem;
    margin-top: 5px;
    display: inline-block;
}

.back-link:hover {
    color: white;
}

.viewer-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.nav-button {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.nav-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.nav-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.progress-container {
    width: 200px;
    height: 6px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background-color: var(--primary-color);
    width: 0;
    transition: width 0.3s;
}

.panorama-viewer {
    flex: 1;
    position: relative;
    overflow: hidden;
    background-color: #111;
    cursor: grab;
}

.panorama-viewer:active {
    cursor: grabbing;
}

.panorama-image {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    max-width: none;
    user-select: none;
    transition: transform 0.2s ease-out;
}

.navigation-overlay {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 10px 20px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    border-radius: 4px;
    z-index: 5;
    text-align: center;
    opacity: 0.8;
    transition: opacity 0.3s;
    pointer-events: none;
}

.navigation-overlay:hover,
.panorama-viewer:hover .navigation-overlay {
    opacity: 0;
}

.viewer-footer {
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 10;
}

.location-info {
    font-size: 0.9rem;
}

.action-button {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

.action-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Fullscreen mode */
.fullscreen .viewer-header,
.fullscreen .viewer-footer {
    position: absolute;
    left: 0;
    right: 0;
    opacity: 0.5;
    transition: opacity 0.3s;
}

.fullscreen .viewer-header {
    top: 0;
}

.fullscreen .viewer-footer {
    bottom: 0;
}

.fullscreen .viewer-header:hover,
.fullscreen .viewer-footer:hover {
    opacity: 1;
} 