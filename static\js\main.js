document.addEventListener('DOMContentLoaded', () => {
    // Elements
    const dropZone = document.getElementById('drop-zone');
    const fileInput = document.getElementById('file-input');
    const uploadForm = document.getElementById('upload-form');
    const uploadButton = document.getElementById('upload-button');
    const fileList = document.getElementById('file-list');
    const uploadStatus = document.getElementById('upload-status');
    const processSection = document.getElementById('process-section');
    const modeCards = document.querySelectorAll('.mode-card');
    const optionsPanels = document.querySelectorAll('.options-panel');
    const processButton = document.getElementById('process-button');
    const resultsSection = document.getElementById('results-section');
    const processingLoader = document.getElementById('processing-loader');
    const resultContents = document.querySelectorAll('.result-content');
    
    // State
    let selectedFiles = [];
    let sessionId = null;
    let selectedMode = null;
    
    // Initialize
    init();
    
    // Functions
    function init() {
        // Set up event listeners
        setupDropZone();
        setupFileInput();
        setupUploadForm();
        setupModeSelection();
        setupProcessButton();
    }
    
    function setupDropZone() {
        // Prevent default behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
            document.body.addEventListener(eventName, preventDefaults, false);
        });
        
        // Highlight drop zone when dragging over
        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });
        
        // Handle dropped files
        dropZone.addEventListener('drop', handleDrop, false);
    }
    
    function setupFileInput() {
        fileInput.addEventListener('change', () => {
            handleFiles(fileInput.files);
        });
    }
    
    function setupUploadForm() {
        uploadForm.addEventListener('submit', uploadFiles);
    }
    
    function setupModeSelection() {
        modeCards.forEach(card => {
            card.addEventListener('click', () => {
                // Remove selected class from all cards
                modeCards.forEach(c => c.classList.remove('selected'));
                
                // Add selected class to clicked card
                card.classList.add('selected');
                
                // Set selected mode
                selectedMode = card.getAttribute('data-mode');
                
                // Hide all option panels
                optionsPanels.forEach(panel => panel.classList.remove('active'));
                
                // Show corresponding options panel
                const optionsPanel = document.getElementById(`${selectedMode}-options`);
                if (optionsPanel) {
                    optionsPanel.classList.add('active');
                }
            });
        });
    }
    
    function setupProcessButton() {
        processButton.addEventListener('click', processImages);
    }
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    function highlight() {
        dropZone.classList.add('drop-zone-active');
    }
    
    function unhighlight() {
        dropZone.classList.remove('drop-zone-active');
    }
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        handleFiles(files);
    }
    
    function handleFiles(files) {
        if (files.length === 0) return;
        
        selectedFiles = Array.from(files);
        
        // Update UI
        updateFileList();
        
        // Enable upload button
        uploadButton.disabled = false;
    }
    
    function updateFileList() {
        // Clear current list
        fileList.innerHTML = '';
        
        // Add file items
        selectedFiles.forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            
            const fileName = document.createElement('div');
            fileName.className = 'file-name';
            fileName.textContent = file.name;
            
            fileItem.appendChild(fileName);
            fileList.appendChild(fileItem);
        });
    }
    
    async function uploadFiles(e) {
        e.preventDefault();
        
        if (selectedFiles.length === 0) return;
        
        // Create form data
        const formData = new FormData();
        selectedFiles.forEach(file => {
            formData.append('files[]', file);
        });
        
        // Update status
        uploadStatus.innerHTML = 'Uploading images...';
        uploadStatus.className = 'upload-status';
        uploadButton.disabled = true;
        
        try {
            // Send request
            const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                throw new Error('Upload failed');
            }
            
            const data = await response.json();
            
            // Update status
            uploadStatus.innerHTML = `${data.message}`;
            uploadStatus.className = 'upload-status status-success';
            
            // Save session ID
            sessionId = data.session_id;
            
            // Show processing section
            processSection.style.display = 'block';
            
            // Scroll to process section
            processSection.scrollIntoView({ behavior: 'smooth' });
            
        } catch (error) {
            console.error('Error uploading files:', error);
            uploadStatus.innerHTML = `Error: ${error.message}`;
            uploadStatus.className = 'upload-status status-error';
            uploadButton.disabled = false;
        }
    }
    
    async function processImages() {
        if (!sessionId || !selectedMode) return;
        
        // Get parameters based on selected mode
        const params = getProcessingParams();
        
        // Show results section with loader
        resultsSection.style.display = 'block';
        processingLoader.style.display = 'flex';
        resultContents.forEach(content => content.classList.remove('active'));
        
        // Scroll to results section
        resultsSection.scrollIntoView({ behavior: 'smooth' });
        
        try {
            // Send processing request
            const response = await fetch('/process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    session_id: sessionId,
                    mode: selectedMode,
                    params: params
                })
            });
            
            if (!response.ok) {
                throw new Error('Processing failed');
            }
            
            const data = await response.json();
            
            // Hide loader
            processingLoader.style.display = 'none';
            
            // Show results based on mode
            showResults(data.results);
            
        } catch (error) {
            console.error('Error processing images:', error);
            processingLoader.style.display = 'none';
            
            // Show error in results section
            resultsSection.innerHTML = `
                <div class="error-message">
                    <h4>Error Processing Images</h4>
                    <p>${error.message}</p>
                    <button class="secondary-button" onclick="location.reload()">Try Again</button>
                </div>
            `;
        }
    }
    
    function getProcessingParams() {
        const params = {};
        
        if (selectedMode === 'panorama') {
            const width = parseInt(document.getElementById('panorama-width').value);
            const height = parseInt(document.getElementById('panorama-height').value);
            params.max_size = [width, height];
        }
        else if (selectedMode === 'streetview') {
            params.step = parseInt(document.getElementById('streetview-step').value);
        }
        else if (selectedMode === 'hyperlapse') {
            params.fps = parseInt(document.getElementById('hyperlapse-fps').value);
            params.stabilize = document.getElementById('hyperlapse-stabilize').checked;
        }
        
        return params;
    }
    
    function showResults(results) {
        if (selectedMode === 'panorama') {
            const panoramaResult = document.getElementById('panorama-result');
            const panoramaImg = document.getElementById('panorama-img');
            const panoramaDownload = document.getElementById('panorama-download');
            
            // Set image source
            panoramaImg.src = results.panorama_url;
            
            // Set download link
            panoramaDownload.href = results.panorama_url;
            
            // Show result
            panoramaResult.classList.add('active');
        }
        else if (selectedMode === 'streetview') {
            const streetviewResult = document.getElementById('streetview-result');
            const streetviewLink = document.getElementById('streetview-link');
            
            // Set link URL
            streetviewLink.href = results.viewer_url;
            
            // Show result
            streetviewResult.classList.add('active');
        }
        else if (selectedMode === 'hyperlapse') {
            const hyperlapseResult = document.getElementById('hyperlapse-result');
            const hyperlapseVideo = document.getElementById('hyperlapse-video');
            const hyperlapseDownload = document.getElementById('hyperlapse-download');
            
            // Set video source
            hyperlapseVideo.src = results.video_url;
            
            // Set download link
            hyperlapseDownload.href = results.video_url;
            
            // Show result
            hyperlapseResult.classList.add('active');
        }
    }
}); 