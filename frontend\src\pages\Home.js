import React from 'react';
import { Link as RouterLink } from 'react-router-dom';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Box from '@mui/material/Box';
import PanoramaIcon from '@mui/icons-material/Panorama';
import PhotoLibraryIcon from '@mui/icons-material/PhotoLibrary';
import UploadFileIcon from '@mui/icons-material/UploadFile';

const Home = () => {
  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Paper 
          elevation={3} 
          sx={{ 
            p: 6, 
            textAlign: 'center',
            background: 'linear-gradient(to right, #3f51b5, #2196f3)',
            color: 'white'
          }}
        >
          <Typography variant="h3" component="h1" gutterBottom>
            Panorama Image Viewer
          </Typography>
          <Typography variant="h4" component="h2" gutterBottom>
            Panorama Viewer
          </Typography>
          <Typography variant="subtitle1" sx={{ mb: 4 }}>
            Create immersive panoramic experiences with easy scene roaming and interaction
          </Typography>
          <Button 
            variant="contained" 
            size="large"
            component={RouterLink}
            to="/create"
            sx={{ 
              bgcolor: 'white', 
              color: '#3f51b5',
              '&:hover': { bgcolor: '#e0e0e0' }
            }}
          >
            Start Creating
          </Button>
        </Paper>
      </Grid>

      <Grid item xs={12} md={4}>
        <Paper 
          elevation={2} 
          sx={{ 
            p: 4, 
            height: '100%', 
            display: 'flex', 
            flexDirection: 'column',
            alignItems: 'center'
          }}
        >
          <UploadFileIcon sx={{ fontSize: 60, color: '#3f51b5', mb: 2 }} />
          <Typography variant="h6" component="h3" gutterBottom>
            Upload Images
          </Typography>
          <Typography variant="body1" color="textSecondary" sx={{ mb: 2 }}>
            Supports multiple file uploads, real-time progress feedback, and visualized upload process
          </Typography>
        </Paper>
      </Grid>

      <Grid item xs={12} md={4}>
        <Paper 
          elevation={2} 
          sx={{ 
            p: 4, 
            height: '100%', 
            display: 'flex', 
            flexDirection: 'column',
            alignItems: 'center'
          }}
        >
          <PanoramaIcon sx={{ fontSize: 60, color: '#3f51b5', mb: 2 }} />
          <Typography variant="h6" component="h3" gutterBottom>
            Generate Panorama
          </Typography>
          <Typography variant="body1" color="textSecondary" sx={{ mb: 2 }}>
            Automatically stitch panoramic images, supporting single panorama and street view modes
          </Typography>
        </Paper>
      </Grid>

      <Grid item xs={12} md={4}>
        <Paper 
          elevation={2} 
          sx={{ 
            p: 4, 
            height: '100%', 
            display: 'flex', 
            flexDirection: 'column',
            alignItems: 'center'
          }}
        >
          <PhotoLibraryIcon sx={{ fontSize: 60, color: '#3f51b5', mb: 2 }} />
          <Typography variant="h6" component="h3" gutterBottom>
            Scene Management
          </Typography>
          <Typography variant="body1" color="textSecondary" sx={{ mb: 2 }}>
            View, manage, and share your created panorama scenes. Supports hotspot marking and scene switching.
          </Typography>
        </Paper>
      </Grid>
    </Grid>
  );
};

export default Home; 