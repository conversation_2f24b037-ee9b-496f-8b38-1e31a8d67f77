#!/usr/bin/env python3
"""
测试街景拼接整合功能
验证新的API端点是否正常工作
"""

import cv2
import numpy as np
import os
import sys

def test_imports():
    """测试所有导入是否正常"""
    print("🔧 测试导入...")
    
    try:
        from panorama.sequential_stitcher import SequentialPanoramaStitcher
        print("✅ SequentialPanoramaStitcher 导入成功")
    except Exception as e:
        print(f"❌ SequentialPanoramaStitcher 导入失败: {e}")
        return False
    
    try:
        from panorama.anti_ghosting_stitcher import AntiGhostingPanoramaStitcher
        print("✅ AntiGhostingPanoramaStitcher 导入成功")
    except Exception as e:
        print(f"❌ AntiGhostingPanoramaStitcher 导入失败: {e}")
        return False
    
    return True

def test_sequential_stitcher():
    """测试顺序拼接器的新方法"""
    print("\n🔄 测试顺序拼接器...")
    
    try:
        from panorama.sequential_stitcher import SequentialPanoramaStitcher
        
        # 创建实例
        stitcher = SequentialPanoramaStitcher(target_height=800)
        print(f"✅ 创建SequentialPanoramaStitcher实例成功，目标高度: {stitcher.target_height}")
        
        # 检查新方法是否存在
        if hasattr(stitcher, 'create_sequential_panorama_from_images'):
            print("✅ create_sequential_panorama_from_images 方法存在")
        else:
            print("❌ create_sequential_panorama_from_images 方法不存在")
            return False
        
        # 创建测试图像
        test_images = []
        for i in range(3):
            # 创建500x800的测试图像
            img = np.random.randint(0, 255, (800, 500, 3), dtype=np.uint8)
            # 添加一些识别特征
            cv2.putText(img, f"Test {i+1}", (50, 400), cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
            test_images.append(img)
        
        print(f"✅ 创建了 {len(test_images)} 张测试图像")
        
        # 测试拼接功能（不保存文件）
        try:
            result = stitcher.create_sequential_panorama_from_images(test_images, debug=False)
            if result is not None:
                h, w = result.shape[:2]
                print(f"✅ 拼接成功，结果尺寸: {w}×{h}")
                return True
            else:
                print("❌ 拼接返回None")
                return False
        except Exception as e:
            print(f"❌ 拼接过程出错: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 测试顺序拼接器失败: {e}")
        return False

def test_anti_ghosting_stitcher():
    """测试防重影拼接器的新方法"""
    print("\n👻 测试防重影拼接器...")
    
    try:
        from panorama.anti_ghosting_stitcher import AntiGhostingPanoramaStitcher
        
        # 创建实例
        stitcher = AntiGhostingPanoramaStitcher(target_height=800)
        print(f"✅ 创建AntiGhostingPanoramaStitcher实例成功，目标高度: {stitcher.target_height}")
        
        # 检查新方法是否存在
        if hasattr(stitcher, 'create_anti_ghosting_panorama_from_images'):
            print("✅ create_anti_ghosting_panorama_from_images 方法存在")
        else:
            print("❌ create_anti_ghosting_panorama_from_images 方法不存在")
            return False
        
        # 创建测试图像
        test_images = []
        for i in range(2):  # 防重影拼接比较重，用2张图测试
            # 创建500x800的测试图像
            img = np.random.randint(100, 200, (800, 500, 3), dtype=np.uint8)
            # 添加一些SIFT特征点
            cv2.circle(img, (250, 400), 50, (255, 255, 255), -1)
            cv2.putText(img, f"AG {i+1}", (200, 420), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
            test_images.append(img)
        
        print(f"✅ 创建了 {len(test_images)} 张测试图像")
        
        # 测试拼接功能（不保存文件）
        try:
            result = stitcher.create_anti_ghosting_panorama_from_images(test_images, debug=False)
            if result is not None:
                h, w = result.shape[:2]
                print(f"✅ 防重影拼接成功，结果尺寸: {w}×{h}")
                return True
            else:
                print("❌ 防重影拼接返回None")
                return False
        except Exception as e:
            print(f"❌ 防重影拼接过程出错: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 测试防重影拼接器失败: {e}")
        return False

def test_main_integration():
    """测试main.py中的整合"""
    print("\n🌐 测试main.py整合...")
    
    try:
        # 导入main.py中的组件
        from main import sequential_stitcher, anti_ghosting_stitcher
        print("✅ 从main.py成功导入拼接器实例")
        
        # 检查实例类型
        print(f"✅ sequential_stitcher 类型: {type(sequential_stitcher).__name__}")
        print(f"✅ anti_ghosting_stitcher 类型: {type(anti_ghosting_stitcher).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ main.py整合测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 70)
    print("🧪 街景拼接整合功能测试")
    print("=" * 70)
    
    tests = [
        ("导入测试", test_imports),
        ("顺序拼接器测试", test_sequential_stitcher),
        ("防重影拼接器测试", test_anti_ghosting_stitcher),
        ("main.py整合测试", test_main_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n{'='*70}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    print(f"{'='*70}")
    
    if passed == total:
        print("🎉 所有测试通过！街景拼接整合成功！")
        print("\n💡 使用指南:")
        print("1. 启动服务器: python main.py")
        print("2. 使用POST /api/scenes/{scene_id}/process")
        print("3. 设置mode为'sequential'或'anti_ghosting'")
        print("4. 享受新的拼接功能！")
        return True
    else:
        print("⚠️  部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 