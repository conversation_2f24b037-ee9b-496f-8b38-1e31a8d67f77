import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDropzone } from 'react-dropzone';
import axios from 'axios';

import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Paper from '@mui/material/Paper';
import Box from '@mui/material/Box';
import TextField from '@mui/material/TextField';
import CircularProgress from '@mui/material/CircularProgress';
import LinearProgress from '@mui/material/LinearProgress';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import FormControl from '@mui/material/FormControl';
import FormLabel from '@mui/material/FormLabel';
import RadioGroup from '@mui/material/RadioGroup';
import Radio from '@mui/material/Radio';
import FormControlLabel from '@mui/material/FormControlLabel';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import Alert from '@mui/material/Alert';
import ImageList from '@mui/material/ImageList';
import ImageListItem from '@mui/material/ImageListItem';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';

const CreateScene = () => {
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(0);
  const [sceneName, setSceneName] = useState('');
  const [scene, setScene] = useState(null);
  const [files, setFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [processingMode, setProcessingMode] = useState('panorama');
  const [stitcherType, setStitcherType] = useState('real'); // 默认使用真实街景算法
  const stitchMode = 'single'; // 拼接模式：固定使用完整拼接（单张全景图）
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState('');
  const [processingInterval, setProcessingInterval] = useState(null);

  // File dropzone configuration
  const onDrop = useCallback((acceptedFiles) => {
    // Filter for image files and add preview URLs
    const imageFiles = acceptedFiles.filter(file => file.type.startsWith('image/'));
    
    setFiles(imageFiles.map(file => 
      Object.assign(file, {
        preview: URL.createObjectURL(file)
      })
    ));
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': []
    },
    multiple: true
  });

  // Create a new scene
  const handleCreateScene = async () => {
    if (!sceneName.trim()) {
      setError('Please enter a scene name');
      return;
    }

    try {
      const formData = new FormData();
      formData.append('name', sceneName);

      const response = await axios.post('/api/scenes', formData);
      setScene(response.data);
      setActiveStep(1);
      setError('');
    } catch (err) {
      setError('Failed to create scene: ' + (err.response?.data?.message || err.message));
    }
  };

  // Upload files to the scene
  const handleUploadFiles = async () => {
    if (files.length === 0) {
      setError('Please select files first');
      return;
    }

    setUploading(true);
    setUploadProgress(0);
    
    try {
      const formData = new FormData();
      files.forEach(file => {
        formData.append('files', file);
      });

      await axios.post(`/api/scenes/${scene.id}/upload`, formData, {
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(percentCompleted);
        }
      });

      setActiveStep(2);
      setError('');
    } catch (err) {
      setError('Upload failed: ' + (err.response?.data?.message || err.message));
    } finally {
      setUploading(false);
    }
  };

  // Process the uploaded images
  const handleProcess = async () => {
    setProcessing(true);
    setError('');

    try {
      // 构建处理参数
      const params = {
        stitcher_type: stitcherType
      };
      
      // 如果是 Street View 模式，添加相关参数
      if (processingMode === 'streetview') {
        // 添加拼接模式参数
        params.stitch_mode = stitchMode;
      }

      // Start processing
      await axios.post(`/api/scenes/${scene.id}/process`, {
        mode: processingMode,
        params: params
      });

      // Set up polling to check status
      const interval = setInterval(async () => {
        try {
          const statusResponse = await axios.get(`/api/scenes/${scene.id}/status`);
          const { status, progress } = statusResponse.data;
          
          setUploadProgress(progress);
          
          if (status === 'completed') {
            clearInterval(interval);
            setProcessing(false);
            
            // After processing is completed, detect landmarks automatically
            try {
              await axios.post(`/api/scenes/${scene.id}/detect_landmarks`);
            } catch (landmarkError) {
              console.error('Landmark detection failed:', landmarkError);
            }
            
            setActiveStep(3);
            
            // Get updated scene data
            const sceneResponse = await axios.get(`/api/scenes/${scene.id}`);
            setScene(sceneResponse.data);
          } else if (status === 'error') {
            clearInterval(interval);
            setProcessing(false);
            setError('Processing failed: ' + (statusResponse.data.error || 'Unknown error'));
          }
        } catch (err) {
          console.error('Polling error:', err);
        }
      }, 2000);

      setProcessingInterval(interval);
    } catch (err) {
      setError('Processing request failed: ' + (err.response?.data?.message || err.message));
      setProcessing(false);
    }
  };

  // Cleanup when unmounting
  React.useEffect(() => {
    return () => {
      // Clear any object URLs to avoid memory leaks
      files.forEach(file => URL.revokeObjectURL(file.preview));
      
      // Clear polling interval if exists
      if (processingInterval) {
        clearInterval(processingInterval);
      }
    };
  }, [files, processingInterval]);

  // View the scene after processing
  const handleViewScene = () => {
    navigate(`/scenes/${scene.id}`);
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>Create New Scene</Typography>
      
      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
        <Step>
          <StepLabel>Create Scene</StepLabel>
        </Step>
        <Step>
          <StepLabel>Upload Images</StepLabel>
        </Step>
        <Step>
          <StepLabel>Processing Options</StepLabel>
        </Step>
        <Step>
          <StepLabel>View Result</StepLabel>
        </Step>
      </Stepper>

      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
      
      {activeStep === 0 && (
        <Paper elevation={2} sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>Scene Information</Typography>
          <TextField
            label="Scene Name"
            variant="outlined"
            fullWidth
            value={sceneName}
            onChange={(e) => setSceneName(e.target.value)}
            margin="normal"
          />
          <Box sx={{ mt: 2 }}>
            <Button 
              variant="contained" 
              onClick={handleCreateScene}
            >
              Create Scene
            </Button>
          </Box>
        </Paper>
      )}

      {activeStep === 1 && (
        <Paper elevation={2} sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>Upload Images</Typography>
          <Box 
            {...getRootProps()} 
            sx={{ 
              border: '2px dashed #ccc',
              borderRadius: 2,
              p: 3,
              mb: 2,
              textAlign: 'center',
              cursor: 'pointer',
              bgcolor: isDragActive ? 'rgba(63, 81, 181, 0.1)' : 'transparent',
              '&:hover': {
                bgcolor: 'rgba(63, 81, 181, 0.05)'
              }
            }}
          >
            <input {...getInputProps()} />
            <CloudUploadIcon sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />
            <Typography variant="body1">
              {isDragActive ? 'Release files here...' : 'Drag and drop files here, or click to select files'}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              Supports uploading multiple image files
            </Typography>
          </Box>
          
          {files.length > 0 && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Selected {files.length} files
              </Typography>
              <ImageList sx={{ height: 220 }} cols={6}>
                {files.map((file, index) => (
                  <ImageListItem key={index}>
                    <img
                      src={file.preview}
                      alt={`Preview ${index}`}
                      loading="lazy"
                    />
                  </ImageListItem>
                ))}
              </ImageList>
            </Box>
          )}

          {uploading && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" gutterBottom>
                Upload Progress: {uploadProgress}%
              </Typography>
              <LinearProgress variant="determinate" value={uploadProgress} />
            </Box>
          )}
          
          <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
            <Button 
              variant="outlined" 
              onClick={() => setActiveStep(0)}
            >
              Previous
            </Button>
            <Button 
              variant="contained" 
              onClick={handleUploadFiles}
              disabled={files.length === 0 || uploading}
              startIcon={uploading ? <CircularProgress size={20} /> : null}
            >
              {uploading ? 'Uploading...' : 'Upload Files'}
            </Button>
          </Box>
        </Paper>
      )}

      {activeStep === 2 && (
        <Paper elevation={2} sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>Processing Options</Typography>
          
          <FormControl component="fieldset" sx={{ mb: 3 }}>
            <FormLabel component="legend">Processing Mode</FormLabel>
            <RadioGroup 
              value={processingMode} 
              onChange={(e) => setProcessingMode(e.target.value)}
            >
              <FormControlLabel 
                value="panorama" 
                control={<Radio />} 
                label="Panorama - Stitch all images into a single panorama" 
              />
              <FormControlLabel 
                value="streetview" 
                control={<Radio />} 
                label="Street View Mode - Create a navigable panorama sequence" 
              />
            </RadioGroup>
          </FormControl>

          {processing && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" gutterBottom>
                Processing Progress: {uploadProgress}%
              </Typography>
              <LinearProgress 
                variant={uploadProgress > 0 ? "determinate" : "indeterminate"} 
                value={uploadProgress} 
              />
            </Box>
          )}
          
          <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
            <Button 
              variant="outlined" 
              onClick={() => setActiveStep(1)}
              disabled={processing}
            >
              Previous
            </Button>
            <Button 
              variant="contained" 
              onClick={handleProcess}
              disabled={processing}
              startIcon={processing ? <CircularProgress size={20} /> : null}
            >
              {processing ? 'Processing...' : 'Start Processing'}
            </Button>
          </Box>
        </Paper>
      )}

      {activeStep === 3 && scene && (
        <Paper elevation={2} sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>Processing Complete</Typography>
          
          <Alert severity="success" sx={{ mb: 3 }}>
            Scene processing is complete! You can now view the results.
          </Alert>
          
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="subtitle1">
                Scene Name: {scene.name}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Type: {scene.type === 'panorama' ? 'Panorama' : 'Street View Mode'}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Created At: {scene.created_at}
              </Typography>
            </CardContent>
          </Card>
          
          <Button 
            variant="contained" 
            onClick={handleViewScene}
            color="primary"
          >
            View Scene
          </Button>
        </Paper>
      )}
    </Box>
  );
};

export default CreateScene; 