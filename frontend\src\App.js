import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import Container from '@mui/material/Container';

import AppHeader from './components/AppHeader';
import Home from './pages/Home';
import SceneList from './pages/SceneList';
import SceneViewer from './pages/SceneViewer';
import CreateScene from './pages/CreateScene';

// Create a theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#3f51b5',
    },
    secondary: {
      main: '#f50057',
    },
  },
  typography: {
    fontFamily: [
      'Roboto',
      'Arial',
      'sans-serif'
    ].join(','),
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <AppHeader />
        <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/scenes" element={<SceneList />} />
            <Route path="/scenes/:id" element={<SceneViewer />} />
            <Route path="/create" element={<CreateScene />} />
          </Routes>
        </Container>
      </Router>
    </ThemeProvider>
  );
}

export default App; 