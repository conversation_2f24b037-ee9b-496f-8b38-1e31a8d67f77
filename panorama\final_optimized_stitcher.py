import cv2
import numpy as np
import os
from glob import glob
from tqdm import tqdm
import math

class FinalOptimizedPanoramaStitcher:
    def __init__(self):
        """初始化最终优化的递归全景拼接器"""
        # 优化的SIFT参数
        self.sift = cv2.SIFT_create(
            nfeatures=3000,
            contrastThreshold=0.04,
            edgeThreshold=12,
            sigma=1.6
        )
        
        # FLANN匹配器
        FLANN_INDEX_KDTREE = 1
        index_params = dict(algorithm=FLANN_INDEX_KDTREE, trees=5)
        search_params = dict(checks=50)
        self.flann = cv2.FlannBasedMatcher(index_params, search_params)
    
    def load_unique_images(self, image_folder):
        """加载唯一的图像文件，避免重复"""
        extensions = ['jpg', 'jpeg', 'png', 'bmp', 'tiff']
        image_paths = set()  # 使用set避免重复路径
        
        for ext in extensions:
            for pattern in [f'*.{ext}', f'*.{ext.upper()}']:
                paths = glob(os.path.join(image_folder, pattern))
                image_paths.update(paths)
        
        # 转换为列表并排序
        image_paths = sorted(list(image_paths))
        
        if not image_paths:
            print("未找到图像文件")
            return []
        
        print(f"发现 {len(image_paths)} 个唯一图像文件...")
        images = []
        loaded_hashes = set()  # 存储图像哈希以避免重复内容
        
        for path in tqdm(image_paths):
            img = cv2.imread(path)
            if img is not None:
                # 计算图像哈希以检测重复内容
                img_hash = hash(img.tobytes())
                if img_hash not in loaded_hashes:
                    # 调整图像大小
                    h, w = img.shape[:2]
                    max_size = 1200
                    if w > max_size:
                        scale = max_size / w
                        new_w = max_size
                        new_h = int(h * scale)
                        img = cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_AREA)
                    
                    images.append(img)
                    loaded_hashes.add(img_hash)
                    print(f"    加载: {os.path.basename(path)} ({img.shape[1]}×{img.shape[0]})")
                else:
                    print(f"    跳过重复: {os.path.basename(path)}")
        
        print(f"成功加载 {len(images)} 张唯一图像")
        return images
    
    def preprocess_image(self, image):
        """预处理图像"""
        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        l = clahe.apply(l)
        
        enhanced = cv2.merge([l, a, b])
        result = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
        
        return result
    
    def detect_and_describe(self, image):
        """特征检测和描述"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
            
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        gray = clahe.apply(gray)
        
        keypoints, descriptors = self.sift.detectAndCompute(gray, None)
        return keypoints, descriptors
    
    def match_features(self, desc1, desc2, ratio=0.75):
        """特征匹配"""
        if desc1 is None or desc2 is None:
            return []
            
        try:
            matches = self.flann.knnMatch(desc1, desc2, k=2)
            good_matches = []
            for match_pair in matches:
                if len(match_pair) == 2:
                    m, n = match_pair
                    if m.distance < ratio * n.distance:
                        good_matches.append(m)
            return good_matches
        except:
            return []
    
    def check_image_similarity(self, img1, img2):
        """检查两张图像的相似度"""
        # 计算结构相似性
        gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)
        
        # 调整到相同尺寸
        h, w = min(gray1.shape[0], gray2.shape[0]), min(gray1.shape[1], gray2.shape[1])
        gray1_resized = cv2.resize(gray1, (w, h))
        gray2_resized = cv2.resize(gray2, (w, h))
        
        # 计算均方误差
        mse = np.mean((gray1_resized - gray2_resized) ** 2)
        
        # 如果MSE很小，说明图像非常相似
        return mse < 100  # 阈值可以调整
    
    def stitch_two_images_optimized(self, img1, img2, debug=False):
        """
        最终优化的两图像拼接方法
        """
        # 检查图像相似度
        if self.check_image_similarity(img1, img2):
            print(f"    检测到相似图像，使用第一张图像")
            return img1.copy()
        
        # 预处理图像
        img1_proc = self.preprocess_image(img1.copy())
        img2_proc = self.preprocess_image(img2.copy())
        
        # 特征检测
        kp1, desc1 = self.detect_and_describe(img1_proc)
        kp2, desc2 = self.detect_and_describe(img2_proc)
        
        print(f"    特征点数量: img1={len(kp1) if kp1 else 0}, img2={len(kp2) if kp2 else 0}")
        
        if desc1 is None or desc2 is None or len(kp1) < 20 or len(kp2) < 20:
            print(f"    特征点不足，使用水平拼接")
            return self._horizontal_stitch(img1, img2)
        
        # 特征匹配
        matches = self.match_features(desc1, desc2, ratio=0.7)
        
        print(f"    匹配点数量: {len(matches)}")
        
        if len(matches) < 15:
            print(f"    匹配点不足，使用水平拼接")
            return self._horizontal_stitch(img1, img2)
        
        # 提取匹配点
        src_pts = np.float32([kp1[m.queryIdx].pt for m in matches]).reshape(-1, 1, 2)
        dst_pts = np.float32([kp2[m.trainIdx].pt for m in matches]).reshape(-1, 1, 2)
        
        # 计算单应性矩阵
        homography, mask = cv2.findHomography(
            src_pts, dst_pts, 
            cv2.RANSAC, 
            ransacReprojThreshold=4.0,
            confidence=0.99,
            maxIters=2000
        )
        
        if homography is None:
            print(f"    无法计算单应性矩阵，使用水平拼接")
            return self._horizontal_stitch(img1, img2)
        
        # 计算内点数量
        inlier_count = np.sum(mask)
        inlier_ratio = inlier_count / len(matches)
        print(f"    内点数量: {inlier_count} ({inlier_ratio:.2f})")
        
        if inlier_count < 10 or inlier_ratio < 0.3:
            print(f"    内点质量不佳，使用水平拼接")
            return self._horizontal_stitch(img1, img2)
        
        # 使用高质量拼接
        try:
            result = self._quality_stitch_with_homography(img1_proc, img2_proc, homography, debug)
            if result is not None:
                return result
            else:
                print(f"    高质量拼接失败，使用水平拼接")
                return self._horizontal_stitch(img1, img2)
        except Exception as e:
            print(f"    拼接过程出错: {str(e)}，使用水平拼接")
            return self._horizontal_stitch(img1, img2)
    
    def _quality_stitch_with_homography(self, img1, img2, H, debug=False):
        """高质量单应性拼接"""
        h1, w1 = img1.shape[:2]
        h2, w2 = img2.shape[:2]
        
        # 计算变换后的角点
        corners1 = np.float32([[0, 0], [0, h1], [w1, h1], [w1, 0]]).reshape(-1, 1, 2)
        corners2 = np.float32([[0, 0], [0, h2], [w2, h2], [w2, 0]]).reshape(-1, 1, 2)
        corners2_transformed = cv2.perspectiveTransform(corners2, H)
        
        # 计算输出画布大小
        all_corners = np.concatenate((corners1, corners2_transformed), axis=0)
        [x_min, y_min] = np.int32(all_corners.min(axis=0).ravel())
        [x_max, y_max] = np.int32(all_corners.max(axis=0).ravel())
        
        # 限制输出尺寸，避免过度拉伸
        max_width = max(w1, w2) * 3
        max_height = max(h1, h2) * 2
        
        output_width = min(x_max - x_min, max_width)
        output_height = min(y_max - y_min, max_height)
        
        # 创建平移矩阵
        translation_dist = [-x_min, -y_min]
        H_translation = np.array([[1, 0, translation_dist[0]], 
                                 [0, 1, translation_dist[1]], 
                                 [0, 0, 1]])
        
        # 创建输出画布
        result = np.zeros((output_height, output_width, 3), dtype=np.uint8)
        
        # 变换第二张图像
        warped_img2 = cv2.warpPerspective(
            img2, 
            H_translation.dot(H), 
            (output_width, output_height),
            flags=cv2.INTER_LINEAR
        )
        
        # 放置第一张图像
        y1_start = max(0, translation_dist[1])
        y1_end = min(output_height, translation_dist[1] + h1)
        x1_start = max(0, translation_dist[0])
        x1_end = min(output_width, translation_dist[0] + w1)
        
        if y1_end > y1_start and x1_end > x1_start:
            # 计算在原图中的对应区域
            orig_y_start = max(0, -translation_dist[1])
            orig_y_end = orig_y_start + (y1_end - y1_start)
            orig_x_start = max(0, -translation_dist[0])
            orig_x_end = orig_x_start + (x1_end - x1_start)
            
            result[y1_start:y1_end, x1_start:x1_end] = img1[orig_y_start:orig_y_end, orig_x_start:orig_x_end]
        
        # 创建掩码
        img1_mask = np.zeros((output_height, output_width), dtype=np.uint8)
        img1_mask[y1_start:y1_end, x1_start:x1_end] = 255
        
        img2_gray = cv2.cvtColor(warped_img2, cv2.COLOR_BGR2GRAY)
        img2_mask = (img2_gray > 0).astype(np.uint8) * 255
        
        # 找到重叠区域
        overlap_mask = cv2.bitwise_and(img1_mask, img2_mask)
        overlap_area = np.sum(overlap_mask > 0)
        
        print(f"      重叠区域像素数: {overlap_area}")
        
        # 智能混合
        if overlap_area > 500:  # 有足够的重叠区域
            # 创建平滑的混合掩码
            blend_mask = self._create_smooth_blend_mask(img1_mask, img2_mask, overlap_mask)
            
            # 混合重叠区域
            overlap_region = overlap_mask > 0
            for i in range(3):
                result[overlap_region, i] = (
                    result[overlap_region, i] * (1 - blend_mask[overlap_region]) +
                    warped_img2[overlap_region, i] * blend_mask[overlap_region]
                ).astype(np.uint8)
        
        # 填充非重叠区域
        img2_only = (img2_mask > 0) & (img1_mask == 0)
        result[img2_only] = warped_img2[img2_only]
        
        # 保守裁剪
        result = self._smart_crop(result)
        
        return result
    
    def _create_smooth_blend_mask(self, mask1, mask2, overlap_mask):
        """创建平滑的混合掩码"""
        # 计算到边界的距离
        dist1 = cv2.distanceTransform(mask1, cv2.DIST_L2, 5)
        dist2 = cv2.distanceTransform(mask2, cv2.DIST_L2, 5)
        
        # 计算权重
        total_dist = dist1 + dist2
        blend_mask = np.zeros_like(dist1, dtype=np.float32)
        
        valid_mask = (total_dist > 0) & (overlap_mask > 0)
        blend_mask[valid_mask] = dist2[valid_mask] / total_dist[valid_mask]
        
        # 应用大范围的高斯平滑
        blend_mask = cv2.GaussianBlur(blend_mask, (51, 51), 0)
        
        return np.clip(blend_mask, 0, 1)
    
    def _horizontal_stitch(self, img1, img2):
        """水平拼接备选方案"""
        h1, w1 = img1.shape[:2]
        h2, w2 = img2.shape[:2]
        
        # 调整到相同高度
        target_height = max(h1, h2)
        
        if h1 != target_height:
            img1 = cv2.resize(img1, (w1, target_height))
        if h2 != target_height:
            img2 = cv2.resize(img2, (w2, target_height))
        
        # 计算重叠宽度
        overlap_width = min(w1, w2) // 4  # 重叠1/4宽度
        
        if overlap_width > 20:
            # 创建带重叠的拼接
            result_width = w1 + w2 - overlap_width
            result = np.zeros((target_height, result_width, 3), dtype=np.uint8)
            
            # 放置第一张图
            result[:, :w1] = img1
            
            # 创建混合区域
            blend_start = w1 - overlap_width
            blend_end = w1
            
            for x in range(overlap_width):
                alpha = x / overlap_width
                blend_x = blend_start + x
                img2_x = x
                
                result[:, blend_x] = (
                    img1[:, blend_x] * (1 - alpha) + 
                    img2[:, img2_x] * alpha
                ).astype(np.uint8)
            
            # 放置第二张图的剩余部分
            result[:, blend_end:] = img2[:, overlap_width:]
            
            print(f"      使用重叠水平拼接: {result.shape[1]}×{result.shape[0]}")
            return result
        else:
            # 简单水平拼接
            result = np.hstack([img1, img2])
            print(f"      使用简单水平拼接: {result.shape[1]}×{result.shape[0]}")
            return result
    
    def _smart_crop(self, image):
        """智能裁剪"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        _, thresh = cv2.threshold(gray, 1, 255, cv2.THRESH_BINARY)
        
        coords = cv2.findNonZero(thresh)
        if coords is None:
            return image
        
        x, y, w, h = cv2.boundingRect(coords)
        
        # 添加小边框
        border = 5
        x = max(0, x - border)
        y = max(0, y - border)
        w = min(image.shape[1] - x, w + 2*border)
        h = min(image.shape[0] - y, h + 2*border)
        
        return image[y:y+h, x:x+w]
    
    def recursive_stitch_images(self, images, debug=False):
        """最终优化的递归拼接"""
        if not images:
            return None
            
        if len(images) == 1:
            return images[0]
            
        print(f"开始最终优化递归拼接 {len(images)} 张图像...")
        
        current_level = images.copy()
        level = 1
        
        while len(current_level) > 1:
            print(f"第 {level} 轮拼接：处理 {len(current_level)} 张图像")
            next_level = []
            
            for i in range(0, len(current_level), 2):
                if i + 1 < len(current_level):
                    img1 = current_level[i]
                    img2 = current_level[i + 1]
                    
                    print(f"  拼接图像对 {i//2 + 1}: {i+1} + {i+2}")
                    print(f"    图像1尺寸: {img1.shape[1]}×{img1.shape[0]}")
                    print(f"    图像2尺寸: {img2.shape[1]}×{img2.shape[0]}")
                    
                    stitched = self.stitch_two_images_optimized(img1, img2, debug=debug)
                    
                    if stitched is not None:
                        next_level.append(stitched)
                        print(f"    拼接成功: {stitched.shape[1]}×{stitched.shape[0]}")
                        if debug:
                            cv2.imwrite(f"debug_final_level_{level}_pair_{i//2 + 1}.jpg", stitched)
                    else:
                        print(f"    拼接失败，保留第一张图像")
                        next_level.append(img1)
                else:
                    print(f"  保留单张图像 {i+1} 到下一轮")
                    next_level.append(current_level[i])
            
            current_level = next_level
            level += 1
            
            print(f"第 {level-1} 轮完成，得到 {len(current_level)} 张图像")
        
        final_result = current_level[0] if current_level else None
        if final_result is not None:
            print(f"最终优化递归拼接完成！最终尺寸: {final_result.shape[1]}×{final_result.shape[0]}")
            # 最终增强
            final_result = self._final_enhancement(final_result)
        
        return final_result
    
    def _final_enhancement(self, image):
        """最终图像增强"""
        if image is None:
            return None
        
        # 轻微锐化
        kernel = np.array([[-0.5, -0.5, -0.5], 
                          [-0.5, 5.0, -0.5], 
                          [-0.5, -0.5, -0.5]])
        sharpened = cv2.filter2D(image, -1, kernel)
        result = cv2.addWeighted(image, 0.85, sharpened, 0.15, 0)
        
        return result
    
    def create_final_panorama(self, image_folder, output_file=None, debug=False):
        """创建最终优化的全景图"""
        # 加载唯一图像
        images = self.load_unique_images(image_folder)
        
        if not images:
            print("未能加载任何有效图像")
            return None
        
        # 最终优化递归拼接
        result = self.recursive_stitch_images(images, debug=debug)
        
        if result is not None and output_file:
            cv2.imwrite(output_file, result, [cv2.IMWRITE_JPEG_QUALITY, 95])
            print(f"最终优化拼接结果已保存到: {output_file}")
        
        return result 