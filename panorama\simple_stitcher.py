import numpy as np
import cv2
import os
from .utils import match_histograms, enhance_image_contrast, enhance_image_quality


class SimpleStitcher:
    def __init__(self):
        # 初始化多种特征检测器，便于后续选择最佳结果
        self.sift = cv2.SIFT_create(nfeatures=10000, contrastThreshold=0.04)
        
        # 初始化ORB检测器作为备选
        self.orb = cv2.ORB_create(nfeatures=10000, 
                                  scaleFactor=1.2, 
                                  WTA_K=3, 
                                  scoreType=cv2.ORB_HARRIS_SCORE)
        
        # 初始化AKAZE检测器，对于低纹理区域有更好的表现
        self.akaze = cv2.AKAZE_create()
        
        # 初始化FLANN匹配器，用于SIFT特征
        FLANN_INDEX_KDTREE = 1
        index_params = dict(algorithm=FLANN_INDEX_KDTREE, trees=5)
        search_params = dict(checks=50)
        self.flann_matcher = cv2.FlannBasedMatcher(index_params, search_params)
        
        # 初始化暴力匹配器，用于ORB和AKAZE特征
        self.bf_matcher = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=False)

    def detect_and_describe(self, image, detector='sift'):
        """
        检测关键点并计算特征描述子
        :param image: 输入图像
        :param detector: 特征检测器类型 ('sift', 'orb', 'akaze')
        :return: 关键点和特征描述子
        """
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
            
        # 应用自适应直方图均衡化增强对比度
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        gray = clahe.apply(gray)
        
        # 根据指定的检测器类型检测关键点和计算描述子
        if detector == 'sift':
            kps, features = self.sift.detectAndCompute(gray, None)
        elif detector == 'orb':
            kps, features = self.orb.detectAndCompute(gray, None)
        elif detector == 'akaze':
            kps, features = self.akaze.detectAndCompute(gray, None)
        else:
            kps, features = self.sift.detectAndCompute(gray, None)
            
        # 将关键点坐标转换为NumPy数组
        if kps:
            kps = np.float32([kp.pt for kp in kps])
            
        return kps, features
        
    def match_keypoints(self, kps_a, kps_b, features_a, features_b, ratio=0.75, reproj_thresh=4.0, detector='sift'):
        """
        匹配关键点
        :param kps_a: 图片A的关键点
        :param kps_b: 图片B的关键点
        :param features_a: 图片A的特征描述子
        :param features_b: 图片B的特征描述子
        :param ratio: Lowe's ratio test的参数
        :param reproj_thresh: RANSAC重投影误差阈值
        :param detector: 特征检测器类型
        :return: 匹配点、单应性矩阵和状态
        """
        try:
            # 根据检测器类型选择合适的匹配器
            if detector == 'sift':
                matcher = self.flann_matcher
                raw_matches = matcher.knnMatch(features_a, features_b, 2)
            else:
                matcher = self.bf_matcher
                raw_matches = matcher.knnMatch(features_a, features_b, 2)

            matches = []
            for m_n in raw_matches:
                # 确保匹配结果包含两个元素
                if len(m_n) == 2:
                    m, n = m_n
                    if m.distance < ratio * n.distance:
                        matches.append((m.trainIdx, m.queryIdx))
            
            print(f"找到 {len(matches)} 个匹配点")
            
            if len(matches) >= 4:
                # 提取匹配点坐标
                pts_a = np.float32([kps_a[i] for (_, i) in matches])
                pts_b = np.float32([kps_b[i] for (i, _) in matches])
                
                # 使用RANSAC计算单应性矩阵，增加迭代次数提高精度
                homography, status = cv2.findHomography(pts_a, pts_b, cv2.RANSAC, reproj_thresh, maxIters=10000, confidence=0.995)
                
                # 计算内点比例
                inliers = np.sum(status)
                inlier_ratio = inliers / len(matches)
                print(f"内点比例: {inlier_ratio:.2f} ({inliers}/{len(matches)})")
                
                # 如果内点比例太低，可能拼接效果不佳
                if inlier_ratio < 0.3:
                    print("警告: 内点比例较低，拼接效果可能不佳")
                
                return matches, homography, status
            else:
                print(f"匹配点数量不足: {len(matches)}")
                return None, None, None
        except Exception as e:
            print(f"匹配关键点时出错: {str(e)}")
            return None, None, None
            
    def _match_histograms(self, source, reference):
        """
        匹配两张图片的直方图以保持色彩一致性
        """
        return match_histograms(source, reference)
        
    def stitch(self, images, ratio=0.75, reproj_thresh=4.0, show_matches=False):
        """
        拼接两张图片
        :param images: 包含两张图片的列表 [左图, 右图]
        :param ratio: Lowe's ratio test的参数
        :param reproj_thresh: RANSAC重投影误差阈值
        :param show_matches: 是否显示匹配点
        :return: 拼接后的图片，如果show_matches为True，则返回拼接后的图片和匹配点可视化图
        """
        if len(images) != 2:
            print("需要提供两张图像进行拼接")
            return None
            
        # 保存原始图像的深拷贝，避免修改原图
        imageA = images[0].copy()  # 左图
        imageB = images[1].copy()  # 右图
        
        # 应用图像增强
        imageA = enhance_image_quality(imageA)
        imageB = enhance_image_quality(imageB)
        
        # 确保两张图像具有相似的亮度和对比度
        # 将右图的直方图匹配到左图
        imageB_matched = self._match_histograms(imageB, imageA)
        
        # 尝试多种特征检测器，选择最佳结果
        best_result = None
        best_inlier_count = 0
        
        detectors = ['sift', 'akaze', 'orb']
        
        for detector in detectors:
            print(f"尝试使用 {detector} 检测器...")
            
            # 检测关键点并计算特征描述子
            kpsA, featuresA = self.detect_and_describe(imageA, detector)
            kpsB, featuresB = self.detect_and_describe(imageB_matched, detector)

            if kpsA is None or kpsB is None or len(kpsA) < 4 or len(kpsB) < 4:
                print(f"检测到的关键点不足: A={len(kpsA) if kpsA is not None else 0}, B={len(kpsB) if kpsB is not None else 0}")
                continue

            # 匹配关键点
            matches, homography, status = self.match_keypoints(kpsA, kpsB, featuresA, featuresB, ratio, reproj_thresh, detector)

            if homography is None:
                print(f"{detector} 匹配失败 - 找到 {len(matches) if matches else 0} 个，需要至少4个")
                continue
                
            # 计算内点数量
            inlier_count = np.sum(status)
            
            if inlier_count > best_inlier_count:
                best_inlier_count = inlier_count
                best_result = {
                    'homography': homography,
                    'kpsA': kpsA,
                    'kpsB': kpsB,
                    'matches': matches,
                    'status': status,
                    'detector': detector
                }
                
            print(f"{detector} 检测器找到 {inlier_count} 个内点")
            
        # 如果所有检测器都失败，返回None
        if best_result is None:
            print("所有特征检测器都无法找到足够的匹配点")
            return None
            
        # 使用最佳结果进行拼接
        homography = best_result['homography']
        kpsA = best_result['kpsA']
        kpsB = best_result['kpsB']
        matches = best_result['matches']
        status = best_result['status']
        
        print(f"使用 {best_result['detector']} 检测器的结果进行拼接")

        # 计算适当的输出尺寸
        hA, wA = imageA.shape[:2]
        hB, wB = imageB.shape[:2]
        
        # 计算透视变换后B图的边界
        ptsB = np.float32([[0, 0], [0, hB-1], [wB-1, hB-1], [wB-1, 0]]).reshape(-1, 1, 2)
        dst = cv2.perspectiveTransform(ptsB, homography)
        dst = dst.reshape(4, 2)
        
        # 计算结果图像的大小和偏移量
        x_min = min(0, np.min(dst[:, 0]))
        y_min = min(0, np.min(dst[:, 1]))
        x_max = max(wA, np.max(dst[:, 0]))
        y_max = max(hA, np.max(dst[:, 1]))
        
        # 创建平移矩阵
        translation_matrix = np.array([
            [1, 0, -x_min],
            [0, 1, -y_min],
            [0, 0, 1]
        ])
        
        # 结合单应性矩阵和平移矩阵
        homography_transformed = translation_matrix.dot(homography)
        
        # 计算输出尺寸
        output_width = int(round(x_max - x_min))
        output_height = int(round(y_max - y_min))
        
        # 创建输出图像
        result = np.zeros((output_height, output_width, 3), dtype=np.uint8)
        
        # 将右图(B)投影到结果图像，使用更高质量的插值
        cv2.warpPerspective(imageB, homography_transformed, (output_width, output_height), 
                           dst=result, flags=cv2.INTER_LANCZOS4)
        
        # 将左图(A)直接复制到结果图像上
        # 计算左图在结果图中的位置
        x_offset = int(round(-x_min))
        y_offset = int(round(-y_min))
        
        # 创建左图的掩码(只取左图非透明部分)
        roi = result[y_offset:y_offset+hA, x_offset:x_offset+wA]
        gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        _, mask_roi = cv2.threshold(gray_roi, 1, 255, cv2.THRESH_BINARY_INV)
        mask_roi_3ch = cv2.merge([mask_roi, mask_roi, mask_roi])
        
        # 只在右图没有内容的区域放置左图，确保左图不会被覆盖或模糊
        roi_with_mask = cv2.bitwise_and(imageA, mask_roi_3ch)
        result[y_offset:y_offset+hA, x_offset:x_offset+wA] = cv2.add(roi, roi_with_mask)
        
        # 确保无缝融合 - 在边界处进行模糊过渡
        # 创建一个高斯核进行过渡区域的处理
        blend_width = 50  # 增加混合宽度以获得更平滑的过渡
        mask = np.zeros((output_height, output_width), dtype=np.uint8)
        mask[y_offset:y_offset+hA, x_offset:x_offset+wA] = 255
        
        # 创建混合掩码
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (blend_width, blend_width))
        blend_mask = cv2.erode(mask, kernel)
        blend_mask = cv2.GaussianBlur(blend_mask, (blend_width*2+1, blend_width*2+1), 0)
        
        # 将混合掩码归一化为[0,1]
        blend_mask = blend_mask.astype(np.float32) / 255.0
        blend_mask = np.dstack([blend_mask, blend_mask, blend_mask])
        
        # 创建一个包含左图的全尺寸图像
        img_with_A = np.zeros_like(result)
        img_with_A[y_offset:y_offset+hA, x_offset:x_offset+wA] = imageA
        
        # 根据混合掩码混合两个图像
        result = img_with_A * blend_mask + result * (1 - blend_mask)
        result = result.astype(np.uint8)
        
        # 裁剪黑边
        result = self._crop_black_borders(result)
        
        # 应用多阶段后处理
        result = self._post_process(result)

        if show_matches:
            # 生成匹配点的可视化图
            vis = self.draw_matches(imageA, imageB_matched, kpsA, kpsB, matches, status)
            return result, vis

        return result
    
    def _post_process(self, image):
        """
        多阶段后处理以提高拼接质量
        """
        if image is None:
            return None
            
        # 1. 应用锐化以提高整体清晰度
        kernel_sharpen = np.array([
            [0, -0.3, 0],
            [-0.3, 2.2, -0.3],
            [0, -0.3, 0]
        ])
        sharpened = cv2.filter2D(image, -1, kernel_sharpen)
        
        # 2. 应用CLAHE增强对比度
        enhanced = enhance_image_contrast(sharpened, clip_limit=1.5, tile_grid_size=(8, 8))
        
        # 3. 轻微增加饱和度
        hsv = cv2.cvtColor(enhanced, cv2.COLOR_BGR2HSV).astype('float32')
        h, s, v = cv2.split(hsv)
        s = np.clip(s * 1.1, 0, 255)  # 轻微增加饱和度
        hsv_enhanced = cv2.merge([h, s, v])
        result = cv2.cvtColor(hsv_enhanced.astype('uint8'), cv2.COLOR_HSV2BGR)
        
        return result
    
    def _preprocess_image(self, image):
        """
        预处理图像以提高拼接质量
        """
        return enhance_image_quality(image)
    
    def _crop_black_borders(self, image):
        """
        裁剪图像中的黑色边框
        """
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 阈值处理找出非黑像素
        _, thresh = cv2.threshold(gray, 1, 255, cv2.THRESH_BINARY)
        
        # 找到所有非零像素的边界
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return image
            
        # 找到所有轮廓的边界矩形
        all_contours = np.vstack([cnt for cnt in contours])
        x, y, w, h = cv2.boundingRect(all_contours)
        
        # 添加小边框
        border = 5
        x = max(0, x - border)
        y = max(0, y - border)
        w = min(image.shape[1] - x, w + 2*border)
        h = min(image.shape[0] - y, h + 2*border)
        
        # 裁剪图像
        return image[y:y+h, x:x+w]
        
    def draw_matches(self, imageA, imageB, kpsA, kpsB, matches, status):
        """
        绘制匹配点
        """
        # 创建输出图像
        (hA, wA) = imageA.shape[:2]
        (hB, wB) = imageB.shape[:2]
        vis = np.zeros((max(hA, hB), wA + wB, 3), dtype="uint8")
        vis[0:hA, 0:wA] = imageA
        vis[0:hB, wA:] = imageB

        # 遍历匹配点
        for ((trainIdx, queryIdx), s) in zip(matches, status):
            # 只绘制内点
            if s == 1:
                # 绘制匹配点
                ptA = (int(kpsA[queryIdx][0]), int(kpsA[queryIdx][1]))
                ptB = (int(kpsB[trainIdx][0]) + wA, int(kpsB[trainIdx][1]))
                cv2.line(vis, ptA, ptB, (0, 255, 0), 1)
                
        return vis
        
    def stitch_multiple(self, images):
        """
        拼接多张图片
        :param images: 图片列表
        :return: 拼接后的全景图
        """
        if len(images) < 2:
            return images[0] if images else None
        
        print(f"开始拼接 {len(images)} 张图片...")
        
        # 预处理所有图像
        processed_images = [self._preprocess_image(img) for img in images]
        
        # 校正曝光差异
        from .utils import correct_exposure_differences
        processed_images = correct_exposure_differences(processed_images)
        
        # 优化图像顺序
        from .utils import optimize_image_order
        processed_images = optimize_image_order(processed_images)
        
        # 从第一张图开始，依次拼接
        result = processed_images[0]
        
        for i in range(1, len(processed_images)):
            print(f"正在拼接图片 {i+1}/{len(processed_images)}...")
            # 注意传入图片顺序为 [左图(当前已拼接结果), 右图(新图)]
            current_result = self.stitch([result, processed_images[i]])
            
            if current_result is None:
                print(f"拼接图片 {i} 失败，尝试反转顺序...")
                # 尝试反转顺序
                current_result = self.stitch([processed_images[i], result])
                
                if current_result is None:
                    print(f"拼接图片 {i} 失败")
                    if i > 1:
                        # 如果至少拼接了一张图，返回当前结果
                        return result
                    return processed_images[0]  # 如果拼接失败，返回第一张图片
            
            result = current_result
            
        return result 