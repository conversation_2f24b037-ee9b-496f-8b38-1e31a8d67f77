#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Street View Sequential Stitcher
使用Street View模式将图片从前往后拼接成一张完整的全景图

功能:
1. 按文件名顺序加载图片
2. 使用Google Street View算法进行拼接
3. 支持高度统一处理
4. 支持重叠区域检测和混合
5. 输出高质量的全景图
"""

import cv2
import numpy as np
import os
import argparse
from glob import glob
from tqdm import tqdm
import time
from panorama.google_street_view import GoogleStreetViewCreator

def load_images_from_folder(folder_path, target_height=None):
    """
    从文件夹加载图像并按名称排序
    
    Args:
        folder_path (str): 图像文件夹路径
        target_height (int, optional): 目标高度，如果指定则统一所有图像高度
        
    Returns:
        list: 加载的图像列表
    """
    # 支持的图像格式
    extensions = ['jpg', 'jpeg', 'png', 'bmp', 'tiff']
    image_paths = []
    
    # 收集所有图像文件
    for ext in extensions:
        image_paths.extend(glob(os.path.join(folder_path, f'*.{ext}')))
        image_paths.extend(glob(os.path.join(folder_path, f'*.{ext.upper()}')))
    
    # 按文件名排序（确保从前往后的顺序）
    image_paths.sort()
    
    if not image_paths:
        print(f"❌ 在文件夹 {folder_path} 中未找到图像文件")
        return []
    
    print(f"📁 找到 {len(image_paths)} 张图像")
    
    # 加载图像
    images = []
    for i, path in enumerate(tqdm(image_paths, desc="加载图像")):
        img = cv2.imread(path)
        if img is not None:
            h, w = img.shape[:2]
            
            # 如果指定了目标高度，统一处理
            if target_height is not None and h != target_height:
                scale = target_height / h
                new_width = int(w * scale)
                img = cv2.resize(img, (new_width, target_height), interpolation=cv2.INTER_AREA)
                print(f"  📐 调整图像 {i+1}: {w}×{h} → {new_width}×{target_height}")
            
            images.append(img)
        else:
            print(f"⚠️  无法加载图像: {path}")
    
    print(f"✅ 成功加载 {len(images)} 张图像")
    return images

def create_street_view_panorama(input_folder, output_file, target_height=None, debug=False):
    """
    创建Street View风格的全景图
    
    Args:
        input_folder (str): 输入图像文件夹
        output_file (str): 输出文件路径
        target_height (int, optional): 目标高度
        debug (bool): 是否开启调试模式
        
    Returns:
        bool: 成功返回True，失败返回False
    """
    
    print("🚀 启动Street View全景图拼接程序")
    print("=" * 60)
    
    # 检查输入文件夹
    if not os.path.exists(input_folder):
        print(f"❌ 输入文件夹不存在: {input_folder}")
        return False
    
    # 创建输出目录
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
        print(f"📁 创建输出目录: {output_dir}")
    
    # 加载图像
    images = load_images_from_folder(input_folder, target_height)
    if not images:
        return False
    
    # 初始化Google Street View拼接器
    print("\n🔧 初始化Street View拼接器...")
    street_view_creator = GoogleStreetViewCreator(target_height=target_height)
    
    # 开始拼接
    print(f"\n🎯 开始拼接 {len(images)} 张图像...")
    print(f"📏 目标高度: {target_height}px" if target_height else "📏 保持原始高度")
    print("-" * 60)
    
    start_time = time.time()
    
    try:
        # 使用create_single_panorama方法进行拼接
        # 这个方法会将所有图像按顺序拼接成一张完整的全景图
        panorama_files = street_view_creator.create_single_panorama(
            image_folder=input_folder,
            output_folder=output_dir,
            target_height=target_height
        )
        
        if panorama_files and len(panorama_files) > 0:
            # 移动生成的文件到指定输出位置
            generated_file = panorama_files[0]
            if generated_file != output_file:
                import shutil
                shutil.move(generated_file, output_file)
            
            # 获取最终图像信息
            final_img = cv2.imread(output_file)
            if final_img is not None:
                h, w = final_img.shape[:2]
                file_size = os.path.getsize(output_file) / (1024 * 1024)  # MB
                
                end_time = time.time()
                process_time = end_time - start_time
                
                print("\n🎉 拼接完成！")
                print("=" * 60)
                print(f"📊 最终全景图信息:")
                print(f"   • 尺寸: {w} × {h} 像素")
                print(f"   • 文件大小: {file_size:.2f} MB")
                print(f"   • 处理时间: {process_time:.2f} 秒")
                print(f"   • 输出文件: {output_file}")
                print("=" * 60)
                
                return True
            else:
                print("❌ 无法读取生成的全景图文件")
                return False
        else:
            print("❌ 全景图拼接失败")
            return False
            
    except Exception as e:
        print(f"❌ 拼接过程中发生错误: {str(e)}")
        if debug:
            import traceback
            traceback.print_exc()
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Street View Sequential Stitcher - 将图片按前往后顺序拼接成全景图",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python street_view_sequential_stitch.py -i roma2 -o output/roma2_streetview.jpg
  python street_view_sequential_stitch.py -i data/data -o output/data_panorama.jpg --height 1200
  python street_view_sequential_stitch.py -i your_images_folder -o output.jpg --height 1500 --debug
        """
    )
    
    parser.add_argument(
        '-i', '--input', 
        required=True,
        help='输入图像文件夹路径'
    )
    
    parser.add_argument(
        '-o', '--output',
        required=True, 
        help='输出全景图文件路径'
    )
    
    parser.add_argument(
        '--height',
        type=int,
        default=None,
        help='目标高度(像素)，如果指定则统一所有图像高度'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='开启调试模式，显示详细错误信息'
    )
    
    args = parser.parse_args()
    
    # 执行拼接
    success = create_street_view_panorama(
        input_folder=args.input,
        output_file=args.output,
        target_height=args.height,
        debug=args.debug
    )
    
    if success:
        print("✅ 程序执行成功！")
        exit(0)
    else:
        print("❌ 程序执行失败！")
        exit(1)

if __name__ == "__main__":
    main() 