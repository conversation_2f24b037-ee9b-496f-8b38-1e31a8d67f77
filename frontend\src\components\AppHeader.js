import React from 'react';
import { Link as RouterLink } from 'react-router-dom';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import PanoramaIcon from '@mui/icons-material/Panorama';

const AppHeader = () => {
  return (
    <AppBar position="static">
      <Toolbar>
        <PanoramaIcon sx={{ mr: 1 }} />
        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
          Panorama Image Viewer | Panorama Viewer
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button color="inherit" component={RouterLink} to="/">Home</Button>
          <Button color="inherit" component={RouterLink} to="/scenes">Scene List</Button>
          <Button 
            color="inherit" 
            component={RouterLink} 
            to="/create" 
            variant="outlined" 
            sx={{ borderColor: 'white', '&:hover': { borderColor: 'white' } }}
          >
            New Scene
          </Button>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default AppHeader; 