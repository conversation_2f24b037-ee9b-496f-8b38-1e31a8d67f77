import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Grid';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardActions from '@mui/material/CardActions';
import CardMedia from '@mui/material/CardMedia';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import Chip from '@mui/material/Chip';
import Alert from '@mui/material/Alert';
import PanoramaIcon from '@mui/icons-material/Panorama';
import ViewCarouselIcon from '@mui/icons-material/ViewCarousel';
import Add from '@mui/icons-material/Add';

const SceneList = () => {
  const navigate = useNavigate();
  const [scenes, setScenes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchScenes = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/api/scenes');
        setScenes(response.data);
        setError('');
      } catch (err) {
        setError('Failed to fetch scene list: ' + (err.response?.data?.message || err.message));
      } finally {
        setLoading(false);
      }
    };

    fetchScenes();
  }, []);

  // Scene type to icon/color mapping
  const getSceneTypeInfo = (type) => {
    switch (type) {
      case 'panorama':
        return { 
          icon: <PanoramaIcon />, 
          color: 'primary',
          label: 'Panorama'
        };
      case 'streetview':
        return { 
          icon: <ViewCarouselIcon />, 
          color: 'secondary',
          label: 'Street View Mode'
        };
      default:
        return { 
          icon: <PanoramaIcon />, 
          color: 'default',
          label: 'Unknown Type'
        };
    }
  };

  // Status to color mapping
  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'processing':
        return 'warning';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  // Status to label mapping
  const getStatusLabel = (status) => {
    switch (status) {
      case 'created':
        return 'Created';
      case 'uploaded':
        return 'Uploaded';
      case 'processing':
        return 'Processing';
      case 'completed':
        return 'Completed';
      case 'error':
        return 'Error';
      default:
        return status;
    }
  };

  const handleCreateNew = () => {
    navigate('/create');
  };

  const handleViewScene = (sceneId) => {
    navigate(`/scenes/${sceneId}`);
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4">Scene List</Typography>
        <Button 
          variant="contained" 
          color="primary" 
          startIcon={<Add />}
          onClick={handleCreateNew}
        >
          New Scene
        </Button>
      </Box>

      {error && <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : scenes.length === 0 ? (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="textSecondary" gutterBottom>
            No scenes found
          </Typography>
          <Typography variant="body1" color="textSecondary" gutterBottom>
            Click the "New Scene" button to create your first panorama scene
          </Typography>
          <Button 
            variant="contained" 
            color="primary" 
            sx={{ mt: 2 }} 
            startIcon={<Add />}
            onClick={handleCreateNew}
          >
            New Scene
          </Button>
        </Box>
      ) : (
        <Grid container spacing={3}>
          {scenes.map((scene) => {
            const typeInfo = getSceneTypeInfo(scene.type);
            const statusColor = getStatusColor(scene.status);
            const statusLabel = getStatusLabel(scene.status);
            
            // Determine if we have a preview image
            const hasPreview = scene.panoramas && scene.panoramas.length > 0;
            const previewUrl = hasPreview ? `/static/${scene.panoramas[0]}` : null;
            
            return (
              <Grid item xs={12} sm={6} md={4} key={scene.id}>
                <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                  {hasPreview ? (
                    <CardMedia
                      component="img"
                      height="160"
                      image={previewUrl}
                      alt={scene.name}
                    />
                  ) : (
                    <Box 
                      sx={{ 
                        height: 160, 
                        display: 'flex', 
                        alignItems: 'center', 
                        justifyContent: 'center',
                        bgcolor: 'grey.200'
                      }}
                    >
                      <PanoramaIcon sx={{ fontSize: 60, color: 'grey.400' }} />
                    </Box>
                  )}
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Typography gutterBottom variant="h6" component="div">
                      {scene.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Created at: {scene.created_at}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                      <Chip 
                        icon={typeInfo.icon} 
                        label={typeInfo.label} 
                        color={typeInfo.color} 
                        size="small"
                        sx={{ minWidth: 85 }} 
                      />
                      <Chip 
                        label={statusLabel} 
                        color={statusColor}
                        size="small"
                        sx={{ minWidth: 70 }} 
                      />
                    </Box>
                  </CardContent>
                  <CardActions>
                    <Button 
                      size="small" 
                      color="primary"
                      disabled={scene.status !== 'completed'}
                      onClick={() => handleViewScene(scene.id)}
                    >
                      View Scene
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            );
          })}
        </Grid>
      )}
    </Box>
  );
};

export default SceneList; 