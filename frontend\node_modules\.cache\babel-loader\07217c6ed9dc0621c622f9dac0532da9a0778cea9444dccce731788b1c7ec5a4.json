{"ast": null, "code": "var _jsxFileName = \"D:\\\\Study_Code\\\\2025-8-5\\\\qrjy_images\\\\frontend\\\\src\\\\pages\\\\CreateScene.js\";\nimport React, { useState, useCallback, useRef } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport Typography from '@mui/material/Typography';\nimport Button from '@mui/material/Button';\nimport Paper from '@mui/material/Paper';\nimport Box from '@mui/material/Box';\nimport TextField from '@mui/material/TextField';\nimport CircularProgress from '@mui/material/CircularProgress';\nimport LinearProgress from '@mui/material/LinearProgress';\nimport Card from '@mui/material/Card';\nimport CardContent from '@mui/material/CardContent';\nimport FormControl from '@mui/material/FormControl';\nimport FormLabel from '@mui/material/FormLabel';\nimport RadioGroup from '@mui/material/RadioGroup';\nimport Radio from '@mui/material/Radio';\nimport FormControlLabel from '@mui/material/FormControlLabel';\nimport Stepper from '@mui/material/Stepper';\nimport Step from '@mui/material/Step';\nimport StepLabel from '@mui/material/StepLabel';\nimport Alert from '@mui/material/Alert';\nimport ImageList from '@mui/material/ImageList';\nimport ImageListItem from '@mui/material/ImageListItem';\nimport CloudUploadIcon from '@mui/icons-material/CloudUpload';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CreateScene = () => {\n  const navigate = useNavigate();\n  const [activeStep, setActiveStep] = useState(0);\n  const [sceneName, setSceneName] = useState('');\n  const [scene, setScene] = useState(null);\n  const [files, setFiles] = useState([]);\n  const [uploading, setUploading] = useState(false);\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [processingMode, setProcessingMode] = useState('panorama');\n  const [stitcherType, setStitcherType] = useState('real'); // 默认使用真实街景算法\n  const stitchMode = 'single'; // 拼接模式：固定使用完整拼接（单张全景图）\n  const [processing, setProcessing] = useState(false);\n  const [error, setError] = useState('');\n  const [processingInterval, setProcessingInterval] = useState(null);\n\n  // File dropzone configuration\n  const onDrop = useCallback(acceptedFiles => {\n    // Filter for image files and add preview URLs\n    const imageFiles = acceptedFiles.filter(file => file.type.startsWith('image/'));\n    setFiles(imageFiles.map(file => Object.assign(file, {\n      preview: URL.createObjectURL(file)\n    })));\n  }, []);\n  const {\n    getRootProps,\n    getInputProps,\n    isDragActive\n  } = useDropzone({\n    onDrop,\n    accept: {\n      'image/*': []\n    },\n    multiple: true\n  });\n\n  // Create a new scene\n  const handleCreateScene = async () => {\n    if (!sceneName.trim()) {\n      setError('Please enter a scene name');\n      return;\n    }\n    try {\n      const formData = new FormData();\n      formData.append('name', sceneName);\n      const response = await axios.post('/api/scenes', formData);\n      setScene(response.data);\n      setActiveStep(1);\n      setError('');\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError('Failed to create scene: ' + (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || err.message));\n    }\n  };\n\n  // Upload files to the scene\n  const handleUploadFiles = async () => {\n    if (files.length === 0) {\n      setError('Please select files first');\n      return;\n    }\n    setUploading(true);\n    setUploadProgress(0);\n    try {\n      const formData = new FormData();\n      files.forEach(file => {\n        formData.append('files', file);\n      });\n      await axios.post(`/api/scenes/${scene.id}/upload`, formData, {\n        onUploadProgress: progressEvent => {\n          const percentCompleted = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n          setUploadProgress(percentCompleted);\n        }\n      });\n      setActiveStep(2);\n      setError('');\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      setError('Upload failed: ' + (((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.message) || err.message));\n    } finally {\n      setUploading(false);\n    }\n  };\n\n  // Process the uploaded images\n  const handleProcess = async () => {\n    setProcessing(true);\n    setError('');\n    try {\n      // 构建处理参数\n      const params = {\n        stitcher_type: stitcherType\n      };\n\n      // 如果是 Street View 模式，添加相关参数\n      if (processingMode === 'streetview') {\n        // 添加拼接模式参数\n        params.stitch_mode = stitchMode;\n      }\n\n      // Start processing\n      await axios.post(`/api/scenes/${scene.id}/process`, {\n        mode: processingMode,\n        params: params\n      });\n\n      // Set up polling to check status\n      const interval = setInterval(async () => {\n        try {\n          const statusResponse = await axios.get(`/api/scenes/${scene.id}/status`);\n          const {\n            status,\n            progress\n          } = statusResponse.data;\n          setUploadProgress(progress);\n          if (status === 'completed') {\n            clearInterval(interval);\n            setProcessing(false);\n\n            // After processing is completed, detect landmarks automatically\n            try {\n              await axios.post(`/api/scenes/${scene.id}/detect_landmarks`);\n            } catch (landmarkError) {\n              console.error('Landmark detection failed:', landmarkError);\n            }\n            setActiveStep(3);\n\n            // Get updated scene data\n            const sceneResponse = await axios.get(`/api/scenes/${scene.id}`);\n            setScene(sceneResponse.data);\n          } else if (status === 'error') {\n            clearInterval(interval);\n            setProcessing(false);\n            setError('Processing failed: ' + (statusResponse.data.error || 'Unknown error'));\n          }\n        } catch (err) {\n          console.error('Polling error:', err);\n        }\n      }, 2000);\n      setProcessingInterval(interval);\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      setError('Processing request failed: ' + (((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || err.message));\n      setProcessing(false);\n    }\n  };\n\n  // Cleanup when unmounting\n  React.useEffect(() => {\n    return () => {\n      // Clear any object URLs to avoid memory leaks\n      files.forEach(file => {\n        if (file.preview) {\n          URL.revokeObjectURL(file.preview);\n        }\n      });\n\n      // Clear polling interval if exists\n      if (processingInterval) {\n        clearInterval(processingInterval);\n      }\n    };\n  }, [files, processingInterval]);\n\n  // View the scene after processing\n  const handleViewScene = () => {\n    navigate(`/scenes/${scene.id}`);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Create New Scene\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Stepper, {\n      activeStep: activeStep,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Step, {\n        children: /*#__PURE__*/_jsxDEV(StepLabel, {\n          children: \"Create Scene\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Step, {\n        children: /*#__PURE__*/_jsxDEV(StepLabel, {\n          children: \"Upload Images\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Step, {\n        children: /*#__PURE__*/_jsxDEV(StepLabel, {\n          children: \"Processing Options\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Step, {\n        children: /*#__PURE__*/_jsxDEV(StepLabel, {\n          children: \"View Result\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 17\n    }, this), activeStep === 0 && /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 2,\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Scene Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        label: \"Scene Name\",\n        variant: \"outlined\",\n        fullWidth: true,\n        value: sceneName,\n        onChange: e => setSceneName(e.target.value),\n        margin: \"normal\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleCreateScene,\n          children: \"Create Scene\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 9\n    }, this), activeStep === 1 && /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 2,\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Upload Images\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        ...getRootProps(),\n        sx: {\n          border: '2px dashed #ccc',\n          borderRadius: 2,\n          p: 3,\n          mb: 2,\n          textAlign: 'center',\n          cursor: 'pointer',\n          bgcolor: isDragActive ? 'rgba(63, 81, 181, 0.1)' : 'transparent',\n          '&:hover': {\n            bgcolor: 'rgba(63, 81, 181, 0.05)'\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          ...getInputProps()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(CloudUploadIcon, {\n          sx: {\n            fontSize: 48,\n            color: 'primary.main',\n            mb: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          children: isDragActive ? 'Release files here...' : 'Drag and drop files here, or click to select files'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          children: \"Supports uploading multiple image files\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 11\n      }, this), files.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: [\"Selected \", files.length, \" files\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(ImageList, {\n          sx: {\n            height: 220\n          },\n          cols: 6,\n          children: files.map((file, index) => /*#__PURE__*/_jsxDEV(ImageListItem, {\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: file.preview,\n              alt: `Preview ${index}`,\n              loading: \"lazy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 21\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 13\n      }, this), uploading && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          gutterBottom: true,\n          children: [\"Upload Progress: \", uploadProgress, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n          variant: \"determinate\",\n          value: uploadProgress\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          onClick: () => setActiveStep(0),\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleUploadFiles,\n          disabled: files.length === 0 || uploading,\n          startIcon: uploading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 38\n          }, this) : null,\n          children: uploading ? 'Uploading...' : 'Upload Files'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 9\n    }, this), activeStep === 2 && /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 2,\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Processing Options\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n        component: \"fieldset\",\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(FormLabel, {\n          component: \"legend\",\n          children: \"Processing Mode\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n          value: processingMode,\n          onChange: e => setProcessingMode(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n            value: \"panorama\",\n            control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 26\n            }, this),\n            label: \"Panorama - Stitch all images into a single panorama\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            value: \"streetview\",\n            control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 26\n            }, this),\n            label: \"Street View Mode - Create a navigable panorama sequence\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 11\n      }, this), processing && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          gutterBottom: true,\n          children: [\"Processing Progress: \", uploadProgress, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n          variant: uploadProgress > 0 ? \"determinate\" : \"indeterminate\",\n          value: uploadProgress\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          onClick: () => setActiveStep(1),\n          disabled: processing,\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleProcess,\n          disabled: processing,\n          startIcon: processing ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 39\n          }, this) : null,\n          children: processing ? 'Processing...' : 'Start Processing'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 9\n    }, this), activeStep === 3 && scene && /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 2,\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Processing Complete\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"success\",\n        sx: {\n          mb: 3\n        },\n        children: \"Scene processing is complete! You can now view the results.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            children: [\"Scene Name: \", scene.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: [\"Type: \", scene.type === 'panorama' ? 'Panorama' : 'Street View Mode']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: [\"Created At: \", scene.created_at]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: handleViewScene,\n        color: \"primary\",\n        children: \"View Scene\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 201,\n    columnNumber: 5\n  }, this);\n};\nexport default CreateScene;", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useRef", "useNavigate", "axios", "Typography", "<PERSON><PERSON>", "Paper", "Box", "TextField", "CircularProgress", "LinearProgress", "Card", "<PERSON><PERSON><PERSON><PERSON>", "FormControl", "FormLabel", "RadioGroup", "Radio", "FormControlLabel", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "ImageList", "ImageListItem", "CloudUploadIcon", "jsxDEV", "_jsxDEV", "CreateScene", "navigate", "activeStep", "setActiveStep", "scene<PERSON><PERSON>", "setSceneName", "scene", "setScene", "files", "setFiles", "uploading", "setUploading", "uploadProgress", "setUploadProgress", "processingMode", "setProcessingMode", "stitcherType", "setStitcherType", "stitchMode", "processing", "setProcessing", "error", "setError", "processingInterval", "setProcessingInterval", "onDrop", "acceptedFiles", "imageFiles", "filter", "file", "type", "startsWith", "map", "Object", "assign", "preview", "URL", "createObjectURL", "getRootProps", "getInputProps", "isDragActive", "useDropzone", "accept", "multiple", "handleCreateScene", "trim", "formData", "FormData", "append", "response", "post", "data", "err", "_err$response", "_err$response$data", "message", "handleUploadFiles", "length", "for<PERSON>ach", "id", "onUploadProgress", "progressEvent", "percentCompleted", "Math", "round", "loaded", "total", "_err$response2", "_err$response2$data", "handleProcess", "params", "stitcher_type", "stitch_mode", "mode", "interval", "setInterval", "statusResponse", "get", "status", "progress", "clearInterval", "landmarkError", "console", "sceneResponse", "_err$response3", "_err$response3$data", "useEffect", "revokeObjectURL", "handleViewScene", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "mb", "severity", "elevation", "p", "label", "fullWidth", "value", "onChange", "e", "target", "margin", "mt", "onClick", "border", "borderRadius", "textAlign", "cursor", "bgcolor", "fontSize", "color", "height", "cols", "index", "src", "alt", "loading", "display", "gap", "disabled", "startIcon", "size", "component", "control", "name", "created_at"], "sources": ["D:/Study_Code/2025-8-5/qrjy_images/frontend/src/pages/CreateScene.js"], "sourcesContent": ["import React, { useState, useCallback, useRef } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\n\nimport Typography from '@mui/material/Typography';\nimport Button from '@mui/material/Button';\nimport Paper from '@mui/material/Paper';\nimport Box from '@mui/material/Box';\nimport TextField from '@mui/material/TextField';\nimport CircularProgress from '@mui/material/CircularProgress';\nimport LinearProgress from '@mui/material/LinearProgress';\nimport Card from '@mui/material/Card';\nimport CardContent from '@mui/material/CardContent';\nimport FormControl from '@mui/material/FormControl';\nimport FormLabel from '@mui/material/FormLabel';\nimport RadioGroup from '@mui/material/RadioGroup';\nimport Radio from '@mui/material/Radio';\nimport FormControlLabel from '@mui/material/FormControlLabel';\nimport Stepper from '@mui/material/Stepper';\nimport Step from '@mui/material/Step';\nimport StepLabel from '@mui/material/StepLabel';\nimport Alert from '@mui/material/Alert';\nimport ImageList from '@mui/material/ImageList';\nimport ImageListItem from '@mui/material/ImageListItem';\nimport CloudUploadIcon from '@mui/icons-material/CloudUpload';\n\nconst CreateScene = () => {\n  const navigate = useNavigate();\n  const [activeStep, setActiveStep] = useState(0);\n  const [sceneName, setSceneName] = useState('');\n  const [scene, setScene] = useState(null);\n  const [files, setFiles] = useState([]);\n  const [uploading, setUploading] = useState(false);\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [processingMode, setProcessingMode] = useState('panorama');\n  const [stitcherType, setStitcherType] = useState('real'); // 默认使用真实街景算法\n  const stitchMode = 'single'; // 拼接模式：固定使用完整拼接（单张全景图）\n  const [processing, setProcessing] = useState(false);\n  const [error, setError] = useState('');\n  const [processingInterval, setProcessingInterval] = useState(null);\n\n  // File dropzone configuration\n  const onDrop = useCallback((acceptedFiles) => {\n    // Filter for image files and add preview URLs\n    const imageFiles = acceptedFiles.filter(file => file.type.startsWith('image/'));\n    \n    setFiles(imageFiles.map(file => \n      Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      })\n    ));\n  }, []);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'image/*': []\n    },\n    multiple: true\n  });\n\n  // Create a new scene\n  const handleCreateScene = async () => {\n    if (!sceneName.trim()) {\n      setError('Please enter a scene name');\n      return;\n    }\n\n    try {\n      const formData = new FormData();\n      formData.append('name', sceneName);\n\n      const response = await axios.post('/api/scenes', formData);\n      setScene(response.data);\n      setActiveStep(1);\n      setError('');\n    } catch (err) {\n      setError('Failed to create scene: ' + (err.response?.data?.message || err.message));\n    }\n  };\n\n  // Upload files to the scene\n  const handleUploadFiles = async () => {\n    if (files.length === 0) {\n      setError('Please select files first');\n      return;\n    }\n\n    setUploading(true);\n    setUploadProgress(0);\n    \n    try {\n      const formData = new FormData();\n      files.forEach(file => {\n        formData.append('files', file);\n      });\n\n      await axios.post(`/api/scenes/${scene.id}/upload`, formData, {\n        onUploadProgress: (progressEvent) => {\n          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);\n          setUploadProgress(percentCompleted);\n        }\n      });\n\n      setActiveStep(2);\n      setError('');\n    } catch (err) {\n      setError('Upload failed: ' + (err.response?.data?.message || err.message));\n    } finally {\n      setUploading(false);\n    }\n  };\n\n  // Process the uploaded images\n  const handleProcess = async () => {\n    setProcessing(true);\n    setError('');\n\n    try {\n      // 构建处理参数\n      const params = {\n        stitcher_type: stitcherType\n      };\n      \n      // 如果是 Street View 模式，添加相关参数\n      if (processingMode === 'streetview') {\n        // 添加拼接模式参数\n        params.stitch_mode = stitchMode;\n      }\n\n      // Start processing\n      await axios.post(`/api/scenes/${scene.id}/process`, {\n        mode: processingMode,\n        params: params\n      });\n\n      // Set up polling to check status\n      const interval = setInterval(async () => {\n        try {\n          const statusResponse = await axios.get(`/api/scenes/${scene.id}/status`);\n          const { status, progress } = statusResponse.data;\n          \n          setUploadProgress(progress);\n          \n          if (status === 'completed') {\n            clearInterval(interval);\n            setProcessing(false);\n            \n            // After processing is completed, detect landmarks automatically\n            try {\n              await axios.post(`/api/scenes/${scene.id}/detect_landmarks`);\n            } catch (landmarkError) {\n              console.error('Landmark detection failed:', landmarkError);\n            }\n            \n            setActiveStep(3);\n            \n            // Get updated scene data\n            const sceneResponse = await axios.get(`/api/scenes/${scene.id}`);\n            setScene(sceneResponse.data);\n          } else if (status === 'error') {\n            clearInterval(interval);\n            setProcessing(false);\n            setError('Processing failed: ' + (statusResponse.data.error || 'Unknown error'));\n          }\n        } catch (err) {\n          console.error('Polling error:', err);\n        }\n      }, 2000);\n\n      setProcessingInterval(interval);\n    } catch (err) {\n      setError('Processing request failed: ' + (err.response?.data?.message || err.message));\n      setProcessing(false);\n    }\n  };\n\n  // Cleanup when unmounting\n  React.useEffect(() => {\n    return () => {\n      // Clear any object URLs to avoid memory leaks\n      files.forEach(file => {\n        if (file.preview) {\n          URL.revokeObjectURL(file.preview);\n        }\n      });\n\n      // Clear polling interval if exists\n      if (processingInterval) {\n        clearInterval(processingInterval);\n      }\n    };\n  }, [files, processingInterval]);\n\n  // View the scene after processing\n  const handleViewScene = () => {\n    navigate(`/scenes/${scene.id}`);\n  };\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" gutterBottom>Create New Scene</Typography>\n      \n      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>\n        <Step>\n          <StepLabel>Create Scene</StepLabel>\n        </Step>\n        <Step>\n          <StepLabel>Upload Images</StepLabel>\n        </Step>\n        <Step>\n          <StepLabel>Processing Options</StepLabel>\n        </Step>\n        <Step>\n          <StepLabel>View Result</StepLabel>\n        </Step>\n      </Stepper>\n\n      {error && <Alert severity=\"error\" sx={{ mb: 2 }}>{error}</Alert>}\n      \n      {activeStep === 0 && (\n        <Paper elevation={2} sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>Scene Information</Typography>\n          <TextField\n            label=\"Scene Name\"\n            variant=\"outlined\"\n            fullWidth\n            value={sceneName}\n            onChange={(e) => setSceneName(e.target.value)}\n            margin=\"normal\"\n          />\n          <Box sx={{ mt: 2 }}>\n            <Button \n              variant=\"contained\" \n              onClick={handleCreateScene}\n            >\n              Create Scene\n            </Button>\n          </Box>\n        </Paper>\n      )}\n\n      {activeStep === 1 && (\n        <Paper elevation={2} sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>Upload Images</Typography>\n          <Box \n            {...getRootProps()} \n            sx={{ \n              border: '2px dashed #ccc',\n              borderRadius: 2,\n              p: 3,\n              mb: 2,\n              textAlign: 'center',\n              cursor: 'pointer',\n              bgcolor: isDragActive ? 'rgba(63, 81, 181, 0.1)' : 'transparent',\n              '&:hover': {\n                bgcolor: 'rgba(63, 81, 181, 0.05)'\n              }\n            }}\n          >\n            <input {...getInputProps()} />\n            <CloudUploadIcon sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />\n            <Typography variant=\"body1\">\n              {isDragActive ? 'Release files here...' : 'Drag and drop files here, or click to select files'}\n            </Typography>\n            <Typography variant=\"body2\" color=\"textSecondary\">\n              Supports uploading multiple image files\n            </Typography>\n          </Box>\n          \n          {files.length > 0 && (\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle1\" gutterBottom>\n                Selected {files.length} files\n              </Typography>\n              <ImageList sx={{ height: 220 }} cols={6}>\n                {files.map((file, index) => (\n                  <ImageListItem key={index}>\n                    <img\n                      src={file.preview}\n                      alt={`Preview ${index}`}\n                      loading=\"lazy\"\n                    />\n                  </ImageListItem>\n                ))}\n              </ImageList>\n            </Box>\n          )}\n\n          {uploading && (\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"body2\" gutterBottom>\n                Upload Progress: {uploadProgress}%\n              </Typography>\n              <LinearProgress variant=\"determinate\" value={uploadProgress} />\n            </Box>\n          )}\n          \n          <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>\n            <Button \n              variant=\"outlined\" \n              onClick={() => setActiveStep(0)}\n            >\n              Previous\n            </Button>\n            <Button \n              variant=\"contained\" \n              onClick={handleUploadFiles}\n              disabled={files.length === 0 || uploading}\n              startIcon={uploading ? <CircularProgress size={20} /> : null}\n            >\n              {uploading ? 'Uploading...' : 'Upload Files'}\n            </Button>\n          </Box>\n        </Paper>\n      )}\n\n      {activeStep === 2 && (\n        <Paper elevation={2} sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>Processing Options</Typography>\n          \n          <FormControl component=\"fieldset\" sx={{ mb: 3 }}>\n            <FormLabel component=\"legend\">Processing Mode</FormLabel>\n            <RadioGroup \n              value={processingMode} \n              onChange={(e) => setProcessingMode(e.target.value)}\n            >\n              <FormControlLabel \n                value=\"panorama\" \n                control={<Radio />} \n                label=\"Panorama - Stitch all images into a single panorama\" \n              />\n              <FormControlLabel \n                value=\"streetview\" \n                control={<Radio />} \n                label=\"Street View Mode - Create a navigable panorama sequence\" \n              />\n            </RadioGroup>\n          </FormControl>\n\n          {processing && (\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"body2\" gutterBottom>\n                Processing Progress: {uploadProgress}%\n              </Typography>\n              <LinearProgress \n                variant={uploadProgress > 0 ? \"determinate\" : \"indeterminate\"} \n                value={uploadProgress} \n              />\n            </Box>\n          )}\n          \n          <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>\n            <Button \n              variant=\"outlined\" \n              onClick={() => setActiveStep(1)}\n              disabled={processing}\n            >\n              Previous\n            </Button>\n            <Button \n              variant=\"contained\" \n              onClick={handleProcess}\n              disabled={processing}\n              startIcon={processing ? <CircularProgress size={20} /> : null}\n            >\n              {processing ? 'Processing...' : 'Start Processing'}\n            </Button>\n          </Box>\n        </Paper>\n      )}\n\n      {activeStep === 3 && scene && (\n        <Paper elevation={2} sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>Processing Complete</Typography>\n          \n          <Alert severity=\"success\" sx={{ mb: 3 }}>\n            Scene processing is complete! You can now view the results.\n          </Alert>\n          \n          <Card sx={{ mb: 3 }}>\n            <CardContent>\n              <Typography variant=\"subtitle1\">\n                Scene Name: {scene.name}\n              </Typography>\n              <Typography variant=\"body2\" color=\"textSecondary\">\n                Type: {scene.type === 'panorama' ? 'Panorama' : 'Street View Mode'}\n              </Typography>\n              <Typography variant=\"body2\" color=\"textSecondary\">\n                Created At: {scene.created_at}\n              </Typography>\n            </CardContent>\n          </Card>\n          \n          <Button \n            variant=\"contained\" \n            onClick={handleViewScene}\n            color=\"primary\"\n          >\n            View Scene\n          </Button>\n        </Paper>\n      )}\n    </Box>\n  );\n};\n\nexport default CreateScene; "], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AAC5D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AAEzB,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,GAAG,MAAM,mBAAmB;AACnC,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,eAAe,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,WAAW,GAAGA,CAAA,KAAM;EACxB,MAAMC,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACoC,KAAK,EAAEC,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACwC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC0C,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAAC,UAAU,CAAC;EAChE,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAC1D,MAAM8C,UAAU,GAAG,QAAQ,CAAC,CAAC;EAC7B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiD,KAAK,EAAEC,QAAQ,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACmD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;;EAElE;EACA,MAAMqD,MAAM,GAAGpD,WAAW,CAAEqD,aAAa,IAAK;IAC5C;IACA,MAAMC,UAAU,GAAGD,aAAa,CAACE,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,CAAC;IAE/EtB,QAAQ,CAACkB,UAAU,CAACK,GAAG,CAACH,IAAI,IAC1BI,MAAM,CAACC,MAAM,CAACL,IAAI,EAAE;MAClBM,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACR,IAAI;IACnC,CAAC,CACH,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM;IAAES,YAAY;IAAEC,aAAa;IAAEC;EAAa,CAAC,GAAGC,WAAW,CAAC;IAChEhB,MAAM;IACNiB,MAAM,EAAE;MACN,SAAS,EAAE;IACb,CAAC;IACDC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACxC,SAAS,CAACyC,IAAI,CAAC,CAAC,EAAE;MACrBvB,QAAQ,CAAC,2BAA2B,CAAC;MACrC;IACF;IAEA,IAAI;MACF,MAAMwB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE5C,SAAS,CAAC;MAElC,MAAM6C,QAAQ,GAAG,MAAMzE,KAAK,CAAC0E,IAAI,CAAC,aAAa,EAAEJ,QAAQ,CAAC;MAC1DvC,QAAQ,CAAC0C,QAAQ,CAACE,IAAI,CAAC;MACvBhD,aAAa,CAAC,CAAC,CAAC;MAChBmB,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAO8B,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZhC,QAAQ,CAAC,0BAA0B,IAAI,EAAA+B,aAAA,GAAAD,GAAG,CAACH,QAAQ,cAAAI,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcF,IAAI,cAAAG,kBAAA,uBAAlBA,kBAAA,CAAoBC,OAAO,KAAIH,GAAG,CAACG,OAAO,CAAC,CAAC;IACrF;EACF,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAIhD,KAAK,CAACiD,MAAM,KAAK,CAAC,EAAE;MACtBnC,QAAQ,CAAC,2BAA2B,CAAC;MACrC;IACF;IAEAX,YAAY,CAAC,IAAI,CAAC;IAClBE,iBAAiB,CAAC,CAAC,CAAC;IAEpB,IAAI;MACF,MAAMiC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BvC,KAAK,CAACkD,OAAO,CAAC7B,IAAI,IAAI;QACpBiB,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEnB,IAAI,CAAC;MAChC,CAAC,CAAC;MAEF,MAAMrD,KAAK,CAAC0E,IAAI,CAAC,eAAe5C,KAAK,CAACqD,EAAE,SAAS,EAAEb,QAAQ,EAAE;QAC3Dc,gBAAgB,EAAGC,aAAa,IAAK;UACnC,MAAMC,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAAEH,aAAa,CAACI,MAAM,GAAG,GAAG,GAAIJ,aAAa,CAACK,KAAK,CAAC;UACvFrD,iBAAiB,CAACiD,gBAAgB,CAAC;QACrC;MACF,CAAC,CAAC;MAEF3D,aAAa,CAAC,CAAC,CAAC;MAChBmB,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAO8B,GAAG,EAAE;MAAA,IAAAe,cAAA,EAAAC,mBAAA;MACZ9C,QAAQ,CAAC,iBAAiB,IAAI,EAAA6C,cAAA,GAAAf,GAAG,CAACH,QAAQ,cAAAkB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAchB,IAAI,cAAAiB,mBAAA,uBAAlBA,mBAAA,CAAoBb,OAAO,KAAIH,GAAG,CAACG,OAAO,CAAC,CAAC;IAC5E,CAAC,SAAS;MACR5C,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM0D,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCjD,aAAa,CAAC,IAAI,CAAC;IACnBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF;MACA,MAAMgD,MAAM,GAAG;QACbC,aAAa,EAAEvD;MACjB,CAAC;;MAED;MACA,IAAIF,cAAc,KAAK,YAAY,EAAE;QACnC;QACAwD,MAAM,CAACE,WAAW,GAAGtD,UAAU;MACjC;;MAEA;MACA,MAAM1C,KAAK,CAAC0E,IAAI,CAAC,eAAe5C,KAAK,CAACqD,EAAE,UAAU,EAAE;QAClDc,IAAI,EAAE3D,cAAc;QACpBwD,MAAM,EAAEA;MACV,CAAC,CAAC;;MAEF;MACA,MAAMI,QAAQ,GAAGC,WAAW,CAAC,YAAY;QACvC,IAAI;UACF,MAAMC,cAAc,GAAG,MAAMpG,KAAK,CAACqG,GAAG,CAAC,eAAevE,KAAK,CAACqD,EAAE,SAAS,CAAC;UACxE,MAAM;YAAEmB,MAAM;YAAEC;UAAS,CAAC,GAAGH,cAAc,CAACzB,IAAI;UAEhDtC,iBAAiB,CAACkE,QAAQ,CAAC;UAE3B,IAAID,MAAM,KAAK,WAAW,EAAE;YAC1BE,aAAa,CAACN,QAAQ,CAAC;YACvBtD,aAAa,CAAC,KAAK,CAAC;;YAEpB;YACA,IAAI;cACF,MAAM5C,KAAK,CAAC0E,IAAI,CAAC,eAAe5C,KAAK,CAACqD,EAAE,mBAAmB,CAAC;YAC9D,CAAC,CAAC,OAAOsB,aAAa,EAAE;cACtBC,OAAO,CAAC7D,KAAK,CAAC,4BAA4B,EAAE4D,aAAa,CAAC;YAC5D;YAEA9E,aAAa,CAAC,CAAC,CAAC;;YAEhB;YACA,MAAMgF,aAAa,GAAG,MAAM3G,KAAK,CAACqG,GAAG,CAAC,eAAevE,KAAK,CAACqD,EAAE,EAAE,CAAC;YAChEpD,QAAQ,CAAC4E,aAAa,CAAChC,IAAI,CAAC;UAC9B,CAAC,MAAM,IAAI2B,MAAM,KAAK,OAAO,EAAE;YAC7BE,aAAa,CAACN,QAAQ,CAAC;YACvBtD,aAAa,CAAC,KAAK,CAAC;YACpBE,QAAQ,CAAC,qBAAqB,IAAIsD,cAAc,CAACzB,IAAI,CAAC9B,KAAK,IAAI,eAAe,CAAC,CAAC;UAClF;QACF,CAAC,CAAC,OAAO+B,GAAG,EAAE;UACZ8B,OAAO,CAAC7D,KAAK,CAAC,gBAAgB,EAAE+B,GAAG,CAAC;QACtC;MACF,CAAC,EAAE,IAAI,CAAC;MAER5B,qBAAqB,CAACkD,QAAQ,CAAC;IACjC,CAAC,CAAC,OAAOtB,GAAG,EAAE;MAAA,IAAAgC,cAAA,EAAAC,mBAAA;MACZ/D,QAAQ,CAAC,6BAA6B,IAAI,EAAA8D,cAAA,GAAAhC,GAAG,CAACH,QAAQ,cAAAmC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcjC,IAAI,cAAAkC,mBAAA,uBAAlBA,mBAAA,CAAoB9B,OAAO,KAAIH,GAAG,CAACG,OAAO,CAAC,CAAC;MACtFnC,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACAjD,KAAK,CAACmH,SAAS,CAAC,MAAM;IACpB,OAAO,MAAM;MACX;MACA9E,KAAK,CAACkD,OAAO,CAAC7B,IAAI,IAAI;QACpB,IAAIA,IAAI,CAACM,OAAO,EAAE;UAChBC,GAAG,CAACmD,eAAe,CAAC1D,IAAI,CAACM,OAAO,CAAC;QACnC;MACF,CAAC,CAAC;;MAEF;MACA,IAAIZ,kBAAkB,EAAE;QACtByD,aAAa,CAACzD,kBAAkB,CAAC;MACnC;IACF,CAAC;EACH,CAAC,EAAE,CAACf,KAAK,EAAEe,kBAAkB,CAAC,CAAC;;EAE/B;EACA,MAAMiE,eAAe,GAAGA,CAAA,KAAM;IAC5BvF,QAAQ,CAAC,WAAWK,KAAK,CAACqD,EAAE,EAAE,CAAC;EACjC,CAAC;EAED,oBACE5D,OAAA,CAACnB,GAAG;IAAA6G,QAAA,gBACF1F,OAAA,CAACtB,UAAU;MAACiH,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAAgB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEnEhG,OAAA,CAACR,OAAO;MAACW,UAAU,EAAEA,UAAW;MAAC8F,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,gBAC7C1F,OAAA,CAACP,IAAI;QAAAiG,QAAA,eACH1F,OAAA,CAACN,SAAS;UAAAgG,QAAA,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eACPhG,OAAA,CAACP,IAAI;QAAAiG,QAAA,eACH1F,OAAA,CAACN,SAAS;UAAAgG,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eACPhG,OAAA,CAACP,IAAI;QAAAiG,QAAA,eACH1F,OAAA,CAACN,SAAS;UAAAgG,QAAA,EAAC;QAAkB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACPhG,OAAA,CAACP,IAAI;QAAAiG,QAAA,eACH1F,OAAA,CAACN,SAAS;UAAAgG,QAAA,EAAC;QAAW;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAET1E,KAAK,iBAAItB,OAAA,CAACL,KAAK;MAACwG,QAAQ,EAAC,OAAO;MAACF,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,EAAEpE;IAAK;MAAAuE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAE/D7F,UAAU,KAAK,CAAC,iBACfH,OAAA,CAACpB,KAAK;MAACwH,SAAS,EAAE,CAAE;MAACH,EAAE,EAAE;QAAEI,CAAC,EAAE;MAAE,CAAE;MAAAX,QAAA,gBAChC1F,OAAA,CAACtB,UAAU;QAACiH,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACpEhG,OAAA,CAAClB,SAAS;QACRwH,KAAK,EAAC,YAAY;QAClBX,OAAO,EAAC,UAAU;QAClBY,SAAS;QACTC,KAAK,EAAEnG,SAAU;QACjBoG,QAAQ,EAAGC,CAAC,IAAKpG,YAAY,CAACoG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAC9CI,MAAM,EAAC;MAAQ;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACFhG,OAAA,CAACnB,GAAG;QAACoH,EAAE,EAAE;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAAnB,QAAA,eACjB1F,OAAA,CAACrB,MAAM;UACLgH,OAAO,EAAC,WAAW;UACnBmB,OAAO,EAAEjE,iBAAkB;UAAA6C,QAAA,EAC5B;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEA7F,UAAU,KAAK,CAAC,iBACfH,OAAA,CAACpB,KAAK;MAACwH,SAAS,EAAE,CAAE;MAACH,EAAE,EAAE;QAAEI,CAAC,EAAE;MAAE,CAAE;MAAAX,QAAA,gBAChC1F,OAAA,CAACtB,UAAU;QAACiH,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAAa;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAChEhG,OAAA,CAACnB,GAAG;QAAA,GACE0D,YAAY,CAAC,CAAC;QAClB0D,EAAE,EAAE;UACFc,MAAM,EAAE,iBAAiB;UACzBC,YAAY,EAAE,CAAC;UACfX,CAAC,EAAE,CAAC;UACJH,EAAE,EAAE,CAAC;UACLe,SAAS,EAAE,QAAQ;UACnBC,MAAM,EAAE,SAAS;UACjBC,OAAO,EAAE1E,YAAY,GAAG,wBAAwB,GAAG,aAAa;UAChE,SAAS,EAAE;YACT0E,OAAO,EAAE;UACX;QACF,CAAE;QAAAzB,QAAA,gBAEF1F,OAAA;UAAA,GAAWwC,aAAa,CAAC;QAAC;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC9BhG,OAAA,CAACF,eAAe;UAACmG,EAAE,EAAE;YAAEmB,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE,cAAc;YAAEnB,EAAE,EAAE;UAAE;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvEhG,OAAA,CAACtB,UAAU;UAACiH,OAAO,EAAC,OAAO;UAAAD,QAAA,EACxBjD,YAAY,GAAG,uBAAuB,GAAG;QAAoD;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,eACbhG,OAAA,CAACtB,UAAU;UAACiH,OAAO,EAAC,OAAO;UAAC0B,KAAK,EAAC,eAAe;UAAA3B,QAAA,EAAC;QAElD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EAELvF,KAAK,CAACiD,MAAM,GAAG,CAAC,iBACf1D,OAAA,CAACnB,GAAG;QAACoH,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,gBACjB1F,OAAA,CAACtB,UAAU;UAACiH,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,GAAC,WAClC,EAACjF,KAAK,CAACiD,MAAM,EAAC,QACzB;QAAA;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbhG,OAAA,CAACJ,SAAS;UAACqG,EAAE,EAAE;YAAEqB,MAAM,EAAE;UAAI,CAAE;UAACC,IAAI,EAAE,CAAE;UAAA7B,QAAA,EACrCjF,KAAK,CAACwB,GAAG,CAAC,CAACH,IAAI,EAAE0F,KAAK,kBACrBxH,OAAA,CAACH,aAAa;YAAA6F,QAAA,eACZ1F,OAAA;cACEyH,GAAG,EAAE3F,IAAI,CAACM,OAAQ;cAClBsF,GAAG,EAAE,WAAWF,KAAK,EAAG;cACxBG,OAAO,EAAC;YAAM;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf;UAAC,GALgBwB,KAAK;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMV,CAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CACN,EAEArF,SAAS,iBACRX,OAAA,CAACnB,GAAG;QAACoH,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,gBACjB1F,OAAA,CAACtB,UAAU;UAACiH,OAAO,EAAC,OAAO;UAACC,YAAY;UAAAF,QAAA,GAAC,mBACtB,EAAC7E,cAAc,EAAC,GACnC;QAAA;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbhG,OAAA,CAAChB,cAAc;UAAC2G,OAAO,EAAC,aAAa;UAACa,KAAK,EAAE3F;QAAe;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CACN,eAEDhG,OAAA,CAACnB,GAAG;QAACoH,EAAE,EAAE;UAAEY,EAAE,EAAE,CAAC;UAAEe,OAAO,EAAE,MAAM;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAnC,QAAA,gBAC1C1F,OAAA,CAACrB,MAAM;UACLgH,OAAO,EAAC,UAAU;UAClBmB,OAAO,EAAEA,CAAA,KAAM1G,aAAa,CAAC,CAAC,CAAE;UAAAsF,QAAA,EACjC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThG,OAAA,CAACrB,MAAM;UACLgH,OAAO,EAAC,WAAW;UACnBmB,OAAO,EAAErD,iBAAkB;UAC3BqE,QAAQ,EAAErH,KAAK,CAACiD,MAAM,KAAK,CAAC,IAAI/C,SAAU;UAC1CoH,SAAS,EAAEpH,SAAS,gBAAGX,OAAA,CAACjB,gBAAgB;YAACiJ,IAAI,EAAE;UAAG;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG,IAAK;UAAAN,QAAA,EAE5D/E,SAAS,GAAG,cAAc,GAAG;QAAc;UAAAkF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEA7F,UAAU,KAAK,CAAC,iBACfH,OAAA,CAACpB,KAAK;MAACwH,SAAS,EAAE,CAAE;MAACH,EAAE,EAAE;QAAEI,CAAC,EAAE;MAAE,CAAE;MAAAX,QAAA,gBAChC1F,OAAA,CAACtB,UAAU;QAACiH,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAAkB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAErEhG,OAAA,CAACb,WAAW;QAAC8I,SAAS,EAAC,UAAU;QAAChC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAC9C1F,OAAA,CAACZ,SAAS;UAAC6I,SAAS,EAAC,QAAQ;UAAAvC,QAAA,EAAC;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACzDhG,OAAA,CAACX,UAAU;UACTmH,KAAK,EAAEzF,cAAe;UACtB0F,QAAQ,EAAGC,CAAC,IAAK1F,iBAAiB,CAAC0F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAAAd,QAAA,gBAEnD1F,OAAA,CAACT,gBAAgB;YACfiH,KAAK,EAAC,UAAU;YAChB0B,OAAO,eAAElI,OAAA,CAACV,KAAK;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnBM,KAAK,EAAC;UAAqD;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eACFhG,OAAA,CAACT,gBAAgB;YACfiH,KAAK,EAAC,YAAY;YAClB0B,OAAO,eAAElI,OAAA,CAACV,KAAK;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnBM,KAAK,EAAC;UAAyD;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAEb5E,UAAU,iBACTpB,OAAA,CAACnB,GAAG;QAACoH,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,gBACjB1F,OAAA,CAACtB,UAAU;UAACiH,OAAO,EAAC,OAAO;UAACC,YAAY;UAAAF,QAAA,GAAC,uBAClB,EAAC7E,cAAc,EAAC,GACvC;QAAA;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbhG,OAAA,CAAChB,cAAc;UACb2G,OAAO,EAAE9E,cAAc,GAAG,CAAC,GAAG,aAAa,GAAG,eAAgB;UAC9D2F,KAAK,EAAE3F;QAAe;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,eAEDhG,OAAA,CAACnB,GAAG;QAACoH,EAAE,EAAE;UAAEY,EAAE,EAAE,CAAC;UAAEe,OAAO,EAAE,MAAM;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAnC,QAAA,gBAC1C1F,OAAA,CAACrB,MAAM;UACLgH,OAAO,EAAC,UAAU;UAClBmB,OAAO,EAAEA,CAAA,KAAM1G,aAAa,CAAC,CAAC,CAAE;UAChC0H,QAAQ,EAAE1G,UAAW;UAAAsE,QAAA,EACtB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThG,OAAA,CAACrB,MAAM;UACLgH,OAAO,EAAC,WAAW;UACnBmB,OAAO,EAAExC,aAAc;UACvBwD,QAAQ,EAAE1G,UAAW;UACrB2G,SAAS,EAAE3G,UAAU,gBAAGpB,OAAA,CAACjB,gBAAgB;YAACiJ,IAAI,EAAE;UAAG;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG,IAAK;UAAAN,QAAA,EAE7DtE,UAAU,GAAG,eAAe,GAAG;QAAkB;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEA7F,UAAU,KAAK,CAAC,IAAII,KAAK,iBACxBP,OAAA,CAACpB,KAAK;MAACwH,SAAS,EAAE,CAAE;MAACH,EAAE,EAAE;QAAEI,CAAC,EAAE;MAAE,CAAE;MAAAX,QAAA,gBAChC1F,OAAA,CAACtB,UAAU;QAACiH,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAAmB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEtEhG,OAAA,CAACL,KAAK;QAACwG,QAAQ,EAAC,SAAS;QAACF,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,EAAC;MAEzC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAERhG,OAAA,CAACf,IAAI;QAACgH,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,eAClB1F,OAAA,CAACd,WAAW;UAAAwG,QAAA,gBACV1F,OAAA,CAACtB,UAAU;YAACiH,OAAO,EAAC,WAAW;YAAAD,QAAA,GAAC,cAClB,EAACnF,KAAK,CAAC4H,IAAI;UAAA;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACbhG,OAAA,CAACtB,UAAU;YAACiH,OAAO,EAAC,OAAO;YAAC0B,KAAK,EAAC,eAAe;YAAA3B,QAAA,GAAC,QAC1C,EAACnF,KAAK,CAACwB,IAAI,KAAK,UAAU,GAAG,UAAU,GAAG,kBAAkB;UAAA;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACbhG,OAAA,CAACtB,UAAU;YAACiH,OAAO,EAAC,OAAO;YAAC0B,KAAK,EAAC,eAAe;YAAA3B,QAAA,GAAC,cACpC,EAACnF,KAAK,CAAC6H,UAAU;UAAA;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEPhG,OAAA,CAACrB,MAAM;QACLgH,OAAO,EAAC,WAAW;QACnBmB,OAAO,EAAErB,eAAgB;QACzB4B,KAAK,EAAC,SAAS;QAAA3B,QAAA,EAChB;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAED,eAAe/F,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}