{"ast": null, "code": "export const reflow = node => node.scrollTop;\nexport function getTransitionProps(props, options) {\n  var _style$transitionDura, _style$transitionTimi;\n  const {\n    timeout,\n    easing,\n    style = {}\n  } = props;\n  return {\n    duration: (_style$transitionDura = style.transitionDuration) != null ? _style$transitionDura : typeof timeout === 'number' ? timeout : timeout[options.mode] || 0,\n    easing: (_style$transitionTimi = style.transitionTimingFunction) != null ? _style$transitionTimi : typeof easing === 'object' ? easing[options.mode] : easing,\n    delay: style.transitionDelay\n  };\n}", "map": {"version": 3, "names": ["reflow", "node", "scrollTop", "getTransitionProps", "props", "options", "_style$transitionDura", "_style$transitionTimi", "timeout", "easing", "style", "duration", "transitionDuration", "mode", "transitionTimingFunction", "delay", "transitionDelay"], "sources": ["D:/Study_Code/2025-8-5/qrjy_images/frontend/node_modules/@mui/material/transitions/utils.js"], "sourcesContent": ["export const reflow = node => node.scrollTop;\nexport function getTransitionProps(props, options) {\n  var _style$transitionDura, _style$transitionTimi;\n  const {\n    timeout,\n    easing,\n    style = {}\n  } = props;\n  return {\n    duration: (_style$transitionDura = style.transitionDuration) != null ? _style$transitionDura : typeof timeout === 'number' ? timeout : timeout[options.mode] || 0,\n    easing: (_style$transitionTimi = style.transitionTimingFunction) != null ? _style$transitionTimi : typeof easing === 'object' ? easing[options.mode] : easing,\n    delay: style.transitionDelay\n  };\n}"], "mappings": "AAAA,OAAO,MAAMA,MAAM,GAAGC,IAAI,IAAIA,IAAI,CAACC,SAAS;AAC5C,OAAO,SAASC,kBAAkBA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACjD,IAAIC,qBAAqB,EAAEC,qBAAqB;EAChD,MAAM;IACJC,OAAO;IACPC,MAAM;IACNC,KAAK,GAAG,CAAC;EACX,CAAC,GAAGN,KAAK;EACT,OAAO;IACLO,QAAQ,EAAE,CAACL,qBAAqB,GAAGI,KAAK,CAACE,kBAAkB,KAAK,IAAI,GAAGN,qBAAqB,GAAG,OAAOE,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGA,OAAO,CAACH,OAAO,CAACQ,IAAI,CAAC,IAAI,CAAC;IACjKJ,MAAM,EAAE,CAACF,qBAAqB,GAAGG,KAAK,CAACI,wBAAwB,KAAK,IAAI,GAAGP,qBAAqB,GAAG,OAAOE,MAAM,KAAK,QAAQ,GAAGA,MAAM,CAACJ,OAAO,CAACQ,IAAI,CAAC,GAAGJ,MAAM;IAC7JM,KAAK,EAAEL,KAAK,CAACM;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}