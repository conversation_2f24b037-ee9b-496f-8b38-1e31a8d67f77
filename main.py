from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, Form, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any, Union
import uvicorn
import os
import cv2
import numpy as np
import time
import json
import shutil
import uuid
from panorama.stitcher import Stitcher
from panorama.simple_stitcher import SimpleStitcher
from panorama.basic_stitcher import BasicStitcher
from panorama.street_view import StreetViewCreator
from panorama.google_street_view import GoogleStreetViewCreator
from panorama.recursive_stitcher import RecursivePanoramaStitcher
from panorama.sequential_stitcher import SequentialPanoramaStitcher
from panorama.anti_ghosting_stitcher import AntiGhostingPanoramaStitcher
from panorama.utils import load_images, save_image, enhance_image_quality, correct_exposure_differences, optimize_image_order
from dotenv import load_dotenv
# 导入AI分析模块
from panorama.ai_analyzer import AIImage<PERSON>nalyzer


app = FastAPI(
    title="Panorama API",
    description="API for creating panoramic images and street views",
    version="1.0.0"
)


app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


app.mount("/static", StaticFiles(directory="static"), name="static")


UPLOAD_FOLDER = "static/uploads"
OUTPUT_FOLDER = "static/output"


os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)


stitcher = Stitcher(detector='sift')
simple_stitcher = SimpleStitcher()
basic_stitcher = BasicStitcher()
street_view_creator = StreetViewCreator()
# 新增：Google 街景风格的生成器
google_street_view_creator = GoogleStreetViewCreator()
# 新增：递归拼接器
recursive_stitcher = RecursivePanoramaStitcher()
# 新增：顺序拼接器
sequential_stitcher = SequentialPanoramaStitcher()
# 新增：防重影拼接器
anti_ghosting_stitcher = AntiGhostingPanoramaStitcher()


scenes_db = {}


class Scene(BaseModel):
    """
    场景模型类，用于定义场景的数据结构
    
    Attributes:
        id (str): 场景唯一标识符
        name (str): 场景名称
        created_at (str): 创建时间
        status (str): 场景状态 (created, uploaded, processing, completed, error)
        panoramas (List[str]): 全景图文件路径列表
        type (str): 场景类型 (panorama 或 streetview)
        metadata (Dict[str, Any]): 其他元数据信息
    """
    id: str
    name: str
    created_at: str
    status: str
    panoramas: List[str] = []
    type: str
    metadata: Dict[str, Any] = {}


class Hotspot(BaseModel):
    """
    热点模型类，用于定义全景图中热点的数据结构
    
    Attributes:
        id (str): 热点唯一标识符
        name (str): 热点名称
        pitch (float): 俯仰角
        yaw (float): 偏航角
        description (str): 热点描述
    """
    id: str
    name: str
    pitch: float
    yaw: float
    description: str = ""


class ProcessingParams(BaseModel):
    """
    处理参数模型类，用于定义场景处理的参数结构
    
    Attributes:
        mode (str): 处理模式 (panorama 或 streetview)
        params (Dict[str, Any]): 具体处理参数
    """
    mode: str
    params: Dict[str, Any] = {}


async def read_image_file(file: UploadFile) -> np.ndarray:
    """
    读取上传的图像文件并转换为numpy数组
    
    Args:
        file (UploadFile): 上传的图像文件对象
        
    Returns:
        np.ndarray: 解码后的图像numpy数组
    """
    contents = await file.read()
    nparr = np.frombuffer(contents, np.uint8)
    img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    return img


@app.post("/api/scenes", response_model=Scene)
async def create_scene(name: str = Form(...)):
    """
    创建新场景
    
    Args:
        name (str): 场景名称，通过表单数据传递
        
    Returns:
        Scene: 创建的场景对象
    """
    scene_id = str(uuid.uuid4())
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    

    scene_upload_dir = os.path.join(UPLOAD_FOLDER, scene_id)
    scene_output_dir = os.path.join(OUTPUT_FOLDER, scene_id)
    
    print(f"Creating directories: {scene_upload_dir}, {scene_output_dir}")
    os.makedirs(scene_upload_dir, exist_ok=True)
    os.makedirs(scene_output_dir, exist_ok=True)
    

    scene = Scene(
        id=scene_id,
        name=name,
        created_at=timestamp,
        status="created",
        type="",
        metadata={}
    )
    

    scenes_db[scene_id] = scene.dict()
    
    return scene


@app.get("/api/scenes", response_model=List[Scene])
async def get_scenes():
    """
    获取所有场景列表
    
    Returns:
        List[Scene]: 所有场景对象的列表
    """
    return list(scenes_db.values())


@app.get("/api/scenes/{scene_id}", response_model=Scene)
async def get_scene(scene_id: str):
    """
    获取特定场景的详细信息
    
    Args:
        scene_id (str): 场景唯一标识符
        
    Returns:
        Scene: 指定的场景对象
        
    Raises:
        HTTPException: 当场景不存在时抛出404异常
    """
    if scene_id not in scenes_db:
        raise HTTPException(status_code=404, detail="Scene not found")
    return scenes_db[scene_id]


@app.post("/api/scenes/{scene_id}/upload")
async def upload_images(scene_id: str, files: List[UploadFile] = File(...)):
    """
    为指定场景上传图像文件
    
    Args:
        scene_id (str): 场景唯一标识符
        files (List[UploadFile]): 要上传的图像文件列表
        
    Returns:
        dict: 上传结果，包含成功状态和上传文件数量
        
    Raises:
        HTTPException: 当场景不存在时抛出404异常
    """
    if scene_id not in scenes_db:
        raise HTTPException(status_code=404, detail="Scene not found")
    
    scene_upload_dir = os.path.join(UPLOAD_FOLDER, scene_id)
    os.makedirs(scene_upload_dir, exist_ok=True)
    

    filenames = []
    for file in files:
        if file.content_type.startswith('image/'):
            filename = f"{len(filenames)}_{file.filename}"
            file_path = os.path.join(scene_upload_dir, filename)
            

            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            
            filenames.append(filename)

    scenes_db[scene_id]["status"] = "uploaded"
    scenes_db[scene_id]["metadata"]["file_count"] = len(filenames)
    scenes_db[scene_id]["metadata"]["files"] = filenames
    
    return {"success": True, "files_uploaded": len(filenames)}


@app.post("/api/scenes/{scene_id}/process")
async def process_scene(
    scene_id: str, 
    params: ProcessingParams,
    background_tasks: BackgroundTasks
):
    """
    处理指定场景的图像，生成全景图或街景
    
    Args:
        scene_id (str): 场景唯一标识符
        params (ProcessingParams): 处理参数
        background_tasks (BackgroundTasks): 后台任务管理器
        
    Returns:
        dict: 处理结果状态
        
    Raises:
        HTTPException: 当场景不存在时抛出404异常
    """
    if scene_id not in scenes_db:
        raise HTTPException(status_code=404, detail="Scene not found")
    

    scenes_db[scene_id]["status"] = "processing"
    scenes_db[scene_id]["type"] = params.mode
    

    background_tasks.add_task(
        process_scene_task, 
        scene_id, 
        params.mode, 
        params.params
    )
    
    return {"success": True, "status": "processing"}


@app.get("/api/scenes/{scene_id}/status")
async def get_processing_status(scene_id: str):
    """
    获取场景处理状态和进度
    
    Args:
        scene_id (str): 场景唯一标识符
        
    Returns:
        dict: 包含状态和进度信息的字典
        
    Raises:
        HTTPException: 当场景不存在时抛出404异常
    """
    if scene_id not in scenes_db:
        raise HTTPException(status_code=404, detail="Scene not found")
    
    return {
        "status": scenes_db[scene_id]["status"],
        "progress": scenes_db[scene_id].get("metadata", {}).get("progress", 0)
    }


async def process_scene_task(scene_id: str, mode: str, params: Dict[str, Any]):
    """
    后台任务处理场景图像的函数
    
    Args:
        scene_id (str): 场景唯一标识符
        mode (str): 处理模式 (panorama 或 streetview)
        params (Dict[str, Any]): 处理参数
    """
    try:

        scenes_db[scene_id]["status"] = "processing"
        scenes_db[scene_id]["metadata"]["progress"] = 0
        

        scene_upload_dir = os.path.join(UPLOAD_FOLDER, scene_id)
        scene_output_dir = os.path.join(OUTPUT_FOLDER, scene_id)
        os.makedirs(scene_output_dir, exist_ok=True)
        

        max_size = params.get("max_size", (2000, 2000))
        images = load_images(scene_upload_dir, max_size)
        
        if not images:
            raise ValueError("No valid images found")
            

        

        scenes_db[scene_id]["metadata"]["progress"] = 10
        
        if mode == "panorama":

            print(f"Processing {len(images)} images for panorama")
            

            stitcher_type = params.get("stitcher", "basic")
            
            if stitcher_type == "basic":

                panorama = basic_stitcher.stitch_multiple(images)
            elif stitcher_type == "simple":

                panorama = simple_stitcher.stitch_multiple(images)
            else:

                panorama = stitcher.stitch_sequence(images)
                

            if panorama is None and stitcher_type != "basic":
                print("Falling back to basic stitcher")
                panorama = basic_stitcher.stitch_multiple(images)
                
            if panorama is None and stitcher_type != "simple":
                print("Falling back to simple stitcher")
                panorama = simple_stitcher.stitch_multiple(images)
                
            if panorama is None:
                raise ValueError("Failed to create panorama")
                

            scenes_db[scene_id]["metadata"]["progress"] = 80
            

            output_file = os.path.join(scene_output_dir, "panorama.jpg")
            save_image(output_file, panorama, [cv2.IMWRITE_JPEG_QUALITY, 100])
            

            relative_path = os.path.relpath(output_file, "static")
            scenes_db[scene_id]["panoramas"] = [relative_path]
            scenes_db[scene_id]["status"] = "completed"
            scenes_db[scene_id]["metadata"]["panorama_url"] = f"/static/{relative_path}"
            
        elif mode == "recursive":
            # 递归拼接模式
            print(f"Processing {len(images)} images for recursive panorama")
            
            scenes_db[scene_id]["metadata"]["progress"] = 20
            
            # 使用递归拼接器
            debug_mode = params.get("debug", False)
            panorama = recursive_stitcher.recursive_stitch_images(images, debug=debug_mode)
            
            if panorama is None:
                raise ValueError("Failed to create recursive panorama")
            
            scenes_db[scene_id]["metadata"]["progress"] = 80
            
            # 保存结果
            output_file = os.path.join(scene_output_dir, "recursive_panorama.jpg")
            save_image(output_file, panorama, [cv2.IMWRITE_JPEG_QUALITY, 100])
            
            # 更新场景信息
            relative_path = os.path.relpath(output_file, "static")
            scenes_db[scene_id]["panoramas"] = [relative_path]
            scenes_db[scene_id]["status"] = "completed"
            scenes_db[scene_id]["metadata"]["panorama_url"] = f"/static/{relative_path}"
            
        elif mode == "streetview":

            print(f"Creating street view from {len(images)} images")
            

            step = params.get("step", 5)
            if len(images) < step * 2:
                step = max(2, len(images) // 3)
            
            # 获取目标高度参数，如果未设置则为None（不限制高度）
            target_height = params.get("target_height", None)
            if target_height is not None:
                print(f"Street view with unified height: {target_height}px")
            else:
                print("Street view with original image heights (no height limit)")
            
            # 获取拼接模式参数：single = 单张全景图，sequence = 多张全景图序列
            stitch_mode = params.get("stitch_mode", "sequence")  # 默认为序列模式
            
            if stitch_mode == "single":
                print("Street view mode: 创建单张完整全景图（所有图片拼接成一张）")
                # 使用防重影拼接器创建单张完整全景图
                if target_height:
                    anti_ghosting_stitcher.target_height = target_height

                panorama = anti_ghosting_stitcher.create_anti_ghosting_panorama_from_images(
                    images,
                    output_file=os.path.join(scene_output_dir, "anti_ghosting_panorama.jpg"),
                    debug=params.get("debug", False)
                )

                if panorama is not None:
                    panorama_files = [os.path.join(scene_output_dir, "anti_ghosting_panorama.jpg")]
                else:
                    # 回退到原始方法
                    panorama_files = google_street_view_creator.create_single_panorama(
                        scene_upload_dir,
                        output_folder=scene_output_dir,
                        target_height=target_height
                    )
            else:
                print("Street view mode: 创建全景图序列（分组拼接）")
                # 选择街景生成器类型
                stitcher_type = params.get("stitcher_type", "anti_ghosting")  # 默认使用防重影算法
                enhanced_mode = params.get("enhanced_sequence", True)  # 默认使用增强模式

                print(f"Using {stitcher_type} stitcher for street view creation...")
                print(f"Enhanced sequence mode: {'ON' if enhanced_mode else 'OFF'}")

                if stitcher_type == "anti_ghosting":
                    # 使用防重影拼接器创建序列
                    print("Using anti-ghosting stitcher for better quality")
                    if target_height:
                        anti_ghosting_stitcher.target_height = target_height

                    panorama_files = anti_ghosting_stitcher.create_sequence_panoramas(
                        scene_upload_dir,
                        step=step,
                        output_folder=scene_output_dir,
                        debug=params.get("debug", False)
                    )
                elif enhanced_mode:
                    # 使用增强的序列生成算法
                    panorama_files = google_street_view_creator.create_enhanced_sequence(
                        scene_upload_dir,
                        step=step,
                        output_folder=scene_output_dir,
                        target_height=target_height
                    )
                else:
                    # 使用原始的Google街景算法
                    panorama_files = google_street_view_creator.create_google_streetview(
                        scene_upload_dir,
                        step=step,
                        output_folder=scene_output_dir,
                        target_height=target_height
                    )
            
            if not panorama_files:
                raise ValueError("Failed to create street view panoramas")
                

            relative_paths = [os.path.relpath(file, "static") for file in panorama_files]
            scenes_db[scene_id]["panoramas"] = relative_paths
            scenes_db[scene_id]["status"] = "completed"
            scenes_db[scene_id]["metadata"]["panorama_urls"] = [f"/static/{path}" for path in relative_paths]
            

            sequence_data = {
                "scene_id": scene_id,
                "panoramas": [os.path.basename(file) for file in panorama_files],
                "count": len(panorama_files)
            }
            
            with open(os.path.join(scene_output_dir, "sequence.json"), "w") as f:
                json.dump(sequence_data, f, indent=2)
                
        elif mode == "sequential":
            # 顺序拼接模式 - 处理上传的图片
            print(f"Creating sequential panorama from {len(images)} uploaded images")
            
            scenes_db[scene_id]["metadata"]["progress"] = 20
            
            # 配置参数
            target_height = params.get("target_height", 1500)
            debug_mode = params.get("debug", False)
            
            print(f"Sequential stitching with target height: {target_height}px")
            
            # 使用顺序拼接器处理上传的图片
            sequential_stitcher.target_height = target_height
            panorama = sequential_stitcher.create_sequential_panorama_from_images(
                images=images,
                debug=debug_mode
            )
            
            if panorama is None:
                raise ValueError("Failed to create sequential panorama")
            
            scenes_db[scene_id]["metadata"]["progress"] = 80
            
            # 保存结果
            output_file = os.path.join(scene_output_dir, "sequential_panorama.jpg")
            save_image(output_file, panorama, [cv2.IMWRITE_JPEG_QUALITY, 95])
            
            # 更新场景信息
            relative_path = os.path.relpath(output_file, "static")
            scenes_db[scene_id]["panoramas"] = [relative_path]
            scenes_db[scene_id]["status"] = "completed"
            scenes_db[scene_id]["metadata"]["panorama_url"] = f"/static/{relative_path}"
            
            # 添加拼接统计信息
            h, w = panorama.shape[:2]
            scenes_db[scene_id]["metadata"]["dimensions"] = {"width": w, "height": h}
            scenes_db[scene_id]["metadata"]["pixels"] = w * h
            scenes_db[scene_id]["metadata"]["file_size"] = os.path.getsize(output_file)
            
        elif mode == "anti_ghosting":
            # 防重影拼接模式 - 处理上传的图片
            print(f"Creating anti-ghosting panorama from {len(images)} uploaded images")
            
            scenes_db[scene_id]["metadata"]["progress"] = 20
            
            # 配置参数
            target_height = params.get("target_height", 1500)
            debug_mode = params.get("debug", False)
            
            print(f"Anti-ghosting stitching with target height: {target_height}px")
            
            # 使用防重影拼接器处理上传的图片
            anti_ghosting_stitcher.target_height = target_height
            panorama = anti_ghosting_stitcher.create_anti_ghosting_panorama_from_images(
                images=images,
                debug=debug_mode
            )
            
            if panorama is None:
                raise ValueError("Failed to create anti-ghosting panorama")
            
            scenes_db[scene_id]["metadata"]["progress"] = 80
            
            # 保存结果
            output_file = os.path.join(scene_output_dir, "anti_ghosting_panorama.jpg")
            save_image(output_file, panorama, [cv2.IMWRITE_JPEG_QUALITY, 95])
            
            # 更新场景信息
            relative_path = os.path.relpath(output_file, "static")
            scenes_db[scene_id]["panoramas"] = [relative_path]
            scenes_db[scene_id]["status"] = "completed"
            scenes_db[scene_id]["metadata"]["panorama_url"] = f"/static/{relative_path}"
            
            # 添加拼接统计信息
            h, w = panorama.shape[:2]
            scenes_db[scene_id]["metadata"]["dimensions"] = {"width": w, "height": h}
            scenes_db[scene_id]["metadata"]["pixels"] = w * h
            scenes_db[scene_id]["metadata"]["file_size"] = os.path.getsize(output_file)
            
            # 计算重影评分
            try:
                # 简化版重影检测
                gray = cv2.cvtColor(panorama, cv2.COLOR_BGR2GRAY)
                edges = cv2.Canny(gray, 50, 150)
                edge_density = np.sum(edges > 0) / (gray.shape[0] * gray.shape[1])
                ghosting_score = min(10, max(0, 10 - abs(edge_density - 0.1) * 100))
                scenes_db[scene_id]["metadata"]["ghosting_score"] = round(ghosting_score, 2)
            except:
                scenes_db[scene_id]["metadata"]["ghosting_score"] = "N/A"
        
        scenes_db[scene_id]["metadata"]["progress"] = 100
        
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error during processing: {str(e)}")
        print(error_details)
        
        scenes_db[scene_id]["status"] = "error"
        scenes_db[scene_id]["metadata"]["error"] = str(e)
        scenes_db[scene_id]["metadata"]["error_details"] = error_details


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """
    全局异常处理器
    
    Args:
        request: 请求对象
        exc: 异常对象
        
    Returns:
        JSONResponse: 包含异常信息的JSON响应
    """
    return JSONResponse(
        status_code=500,
        content={"message": str(exc), "type": type(exc).__name__}
    )


@app.delete("/api/scenes/clear")
async def clear_all_scenes():
    """
    删除所有场景及其相关文件
    """
    try:
        # Get a copy of scene IDs to avoid modifying dict during iteration
        scene_ids = list(scenes_db.keys())
        
        # Clear scenes from memory
        scenes_db.clear()
        
        # Remove files
        for scene_id in scene_ids:
            scene_upload_dir = os.path.join(UPLOAD_FOLDER, scene_id)
            scene_output_dir = os.path.join(OUTPUT_FOLDER, scene_id)
            
            # Remove upload directory
            if os.path.exists(scene_upload_dir):
                shutil.rmtree(scene_upload_dir)
                
            # Remove output directory
            if os.path.exists(scene_output_dir):
                shutil.rmtree(scene_output_dir)
        
        return {"success": True, "message": "All scenes deleted", "count": len(scene_ids)}
    except Exception as e:
        return {"success": False, "error": str(e)}


# Initialize AI analyzer (now using basic OpenCV analysis)
load_dotenv()  # Load environment variables
ai_analyzer = None
try:
    ai_analyzer = AIImageAnalyzer()  # No API key required anymore
    print("AI Analyzer initialized successfully (using basic OpenCV analysis)")
except Exception as e:
    print(f"AI Analyzer not initialized: {e}")
    ai_analyzer = None


@app.post("/api/scenes/{scene_id}/analyze")
async def analyze_scene_with_ai(scene_id: str, provider: str = "gpt-4v"):
    """
    使用AI分析场景
    
    Args:
        scene_id (str): 场景唯一标识符
        provider (str): AI提供者，默认为"gpt-4v"
        
    Returns:
        dict: 分析结果
    """
    if scene_id not in scenes_db:
        raise HTTPException(status_code=404, detail="Scene not found")
    
    if not ai_analyzer:
        raise HTTPException(status_code=501, detail="AI Analyzer not configured")
    
    try:
        # Get scene info
        scene = scenes_db[scene_id]
        if scene["status"] != "completed":
            raise HTTPException(status_code=400, detail="Scene processing not completed")
        
        # Get the panorama image path
        if not scene["panoramas"]:
            raise HTTPException(status_code=400, detail="No panoramas found for this scene")
        
        panorama_path = os.path.join("static", scene["panoramas"][0])
        if not os.path.exists(panorama_path):
            raise HTTPException(status_code=404, detail="Panorama image not found")
        
        # Perform AI analysis
        analysis_result = ai_analyzer.analyze_panorama_scene(image_path=panorama_path)
        
        # Save analysis result
        scenes_db[scene_id]["metadata"]["analysis"] = analysis_result
        scenes_db[scene_id]["metadata"]["ai_provider"] = provider
        
        return {
            "success": True,
            "provider": provider,
            "analysis": analysis_result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")


@app.post("/api/scenes/{scene_id}/detect_landmarks")
async def detect_scene_landmarks(scene_id: str, provider: str = "gpt-4v"):
    """
    使用AI检测场景中的地标
    
    Args:
        scene_id (str): 场景唯一标识符
        provider (str): AI提供者，默认为"gpt-4v"
        
    Returns:
        dict: 地标检测结果和热点列表
    """
    if scene_id not in scenes_db:
        raise HTTPException(status_code=404, detail="Scene not found")
    
    if not ai_analyzer:
        raise HTTPException(status_code=501, detail="AI Analyzer not configured")
    
    try:
        # Get scene info
        scene = scenes_db[scene_id]
        if scene["status"] != "completed":
            raise HTTPException(status_code=400, detail="Scene processing not completed")
        
        # Get the panorama image path
        if not scene["panoramas"]:
            raise HTTPException(status_code=400, detail="No panoramas found for this scene")
        
        panorama_path = os.path.join("static", scene["panoramas"][0])
        if not os.path.exists(panorama_path):
            raise HTTPException(status_code=404, detail="Panorama image not found")
        
        # Perform landmark detection
        detection_result = ai_analyzer.detect_landmarks(image_path=panorama_path)
        
        # Save detection result
        scenes_db[scene_id]["metadata"]["landmark_detection"] = detection_result
        scenes_db[scene_id]["metadata"]["ai_provider"] = provider
        
        # Convert detected landmarks to hotspots
        try:
            import json
            landmarks_data = json.loads(detection_result['response'])
            hotspots = []
            for landmark in landmarks_data.get('landmarks', []):
                hotspot = {
                    'id': str(uuid.uuid4()),
                    'name': landmark.get('name', 'Unknown Landmark'),
                    'pitch': landmark.get('pitch', 0),
                    'yaw': landmark.get('yaw', 0),
                    'description': landmark.get('description', '')
                }
                hotspots.append(hotspot)
            
            # Save hotspots to scene metadata
            scenes_db[scene_id]["metadata"]["hotspots"] = hotspots
            
            # Analyze each hotspot in detail
            detailed_hotspots = []
            for hotspot in hotspots:
                detail_result = ai_analyzer.analyze_region_detail(
                    image_path=panorama_path,
                    pitch=hotspot['pitch'],
                    yaw=hotspot['yaw']
                )
                hotspot['detailed_description'] = detail_result.get('response', '')
                detailed_hotspots.append(hotspot)
            
            # Update hotspots with detailed descriptions
            scenes_db[scene_id]["metadata"]["hotspots"] = detailed_hotspots
            
        except json.JSONDecodeError:
            # If we can't parse the response as JSON, we'll just save the raw response
            pass
        
        return {
            "success": True,
            "provider": provider,
            "detection": detection_result,
            "hotspots": scenes_db[scene_id]["metadata"].get("hotspots", [])
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Landmark detection failed: {str(e)}")


@app.post("/api/scenes/{scene_id}/chat")
async def chat_with_scene(scene_id: str, message: str, provider: str = "kimi-thinking-preview"):
    """
    与AI就场景进行对话
    
    Args:
        scene_id (str): 场景唯一标识符
        message (str): 用户消息
        provider (str): AI提供者，默认为"kimi-thinking-preview"
        
    Returns:
        dict: 对话响应结果
    """
    if scene_id not in scenes_db:
        raise HTTPException(status_code=404, detail="Scene not found")
    
    if not ai_analyzer:
        raise HTTPException(status_code=501, detail="AI Analyzer not configured")
    
    try:
        # Get scene info
        scene = scenes_db[scene_id]
        if scene["status"] != "completed":
            raise HTTPException(status_code=400, detail="Scene processing not completed")
        
        # Get the panorama image path
        if not scene["panoramas"]:
            raise HTTPException(status_code=400, detail="No panoramas found for this scene")
        
        panorama_path = os.path.join("static", scene["panoramas"][0])
        if not os.path.exists(panorama_path):
            raise HTTPException(status_code=404, detail="Panorama image not found")
        
        # Build context from scene metadata
        context = f"Scene: {scene['name']}\n"
        if scene["metadata"].get("analysis"):
            try:
                analysis = json.loads(scene["metadata"]["analysis"]["response"])
                context += f"Scene Description: {analysis.get('description', '')}\n"
            except:
                context += f"Scene Description: {scene['metadata']['analysis'].get('response', '')}\n"
        
        # Add hotspot information to context
        if scene["metadata"].get("hotspots"):
            context += "Points of Interest:\n"
            for hotspot in scene["metadata"]["hotspots"]:
                context += f"- {hotspot['name']}: {hotspot.get('detailed_description', hotspot.get('description', ''))}\n"
        
        prompt = f"""基础图像分析 - 用户问题: {message}
        
        场景信息: {context}
        
        注意：当前使用基础图像分析功能，提供场景的基本技术信息。"""

        # Perform chat
        chat_result = ai_analyzer.analyze_image_with_gpt4v(
            image_path=panorama_path,
            prompt=prompt
        )
        
        # Save chat history
        chat_entry = {
            "message": message,
            "response": chat_result,
            "timestamp": time.time()
        }
        
        if "chat_history" not in scenes_db[scene_id]["metadata"]:
            scenes_db[scene_id]["metadata"]["chat_history"] = []
        
        scenes_db[scene_id]["metadata"]["chat_history"].append(chat_entry)
        
        return {
            "success": True,
            "provider": provider,
            "response": chat_result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chat failed: {str(e)}")


@app.put("/api/scenes/{scene_id}/hotspots")
async def update_scene_hotspots(scene_id: str, hotspots: List[Dict[str, Any]]):
    """
    更新场景的热点信息
    
    Args:
        scene_id (str): 场景唯一标识符
        hotspots (List[Dict[str, Any]]): 新的热点列表
        
    Returns:
        dict: 更新结果和热点列表
    """
    if scene_id not in scenes_db:
        raise HTTPException(status_code=404, detail="Scene not found")
    
    try:
        scenes_db[scene_id]["metadata"]["hotspots"] = hotspots
        return {"success": True, "hotspots": hotspots}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update hotspots: {str(e)}")


@app.get("/api/scenes/{scene_id}/hotspots")
async def get_scene_hotspots(scene_id: str, pano_index: Optional[int] = None):
    """
    获取场景的热点信息，可选择按全景图索引过滤
    
    Args:
        scene_id (str): 场景唯一标识符
        pano_index (Optional[int]): 可选的全景图索引，用于过滤热点
        
    Returns:
        List[Dict]: 热点列表，如果提供了pano_index则返回过滤后的结果
    """
    if scene_id not in scenes_db:
        raise HTTPException(status_code=404, detail="Scene not found")
    
    hotspots = scenes_db[scene_id]["metadata"].get("hotspots", [])
    
    # 如果支持按全景图索引过滤热点
    if pano_index is not None and "panoramas" in scenes_db[scene_id]:
        # 实现基于全景图索引的热点过滤逻辑
        # 这里假设每个热点有panorama_index属性
        filtered_hotspots = [h for h in hotspots if h.get("panorama_index", 0) == pano_index]
        return filtered_hotspots
    
    return hotspots


if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True) 