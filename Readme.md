# 全景图像拼接工具

## 项目概述

这是一个图像拼接和全景展示的应用，结合了前端界面与后端处理。项目主要功能包括图像拼接、全景展示和场景管理。

**注意：已移除 GPT-4V 集成，现在使用基础的 OpenCV 图像分析功能。**

## 项目结构

### 1. 前端部分 (frontend/)
```
frontend/
├── public/                 # 静态资源文件
│   ├── index.html          # 主页面
│   └── manifest.json       # 应用配置文件
├── src/                    # React源代码
│   ├── components/         # 公共组件
│   │   └── AppHeader.js    # 应用头部组件
│   ├── pages/              # 页面组件
│   │   ├── CreateScene.js  # 创建场景页面
│   │   ├── Home.js         # 主页
│   │   ├── SceneList.js    # 场景列表页面
│   │   └── SceneViewer.js  # 场景查看器页面
│   ├── App.js              # 主应用组件
│   ├── index.css           # 全局样式
│   └── index.js            # 应用入口
├── package.json            # Node.js依赖配置
└── webpack.config.js       # Webpack配置
```

### 2. 后端部分 (Python)
```
panorama/                   # 图像处理模块
├── __init__.py             # Python包初始化文件
├── ai_analyzer.py          # 基础图像分析 (已移除GPT-4V)
├── basic_stitcher.py       # 基础图像拼接实现
├── simple_stitcher.py      # 简单图像拼接实现
├── stitcher.py             # 主要图像拼接实现
├── street_view.py          # 街景模式实现
└── utils.py                # 工具函数
```

### 3. 静态资源 (static/)
```
static/
├── css/
│   ├── style.css           # 主要样式文件
│   └── viewer.css          # 查看器样式
└── js/
    ├── main.js             # 主要JavaScript文件
    └── viewer.js           # 查看器相关JavaScript
```

### 4. 核心应用文件
- `main.py` - FastAPI后端主应用
- `test_stitching.py` - 图像拼接测试脚本

## 功能模块分析

### 1. 图像拼接功能
项目实现了多种图像拼接算法：
- **Basic Stitcher**: 基础拼接功能，直接拼接两张图像
- **Simple Stitcher**: 简单拼接功能
- **Advanced Stitcher**: 高级拼接功能，包含多种特征检测器
- **Street View**: 街景模式，从一系列图像创建街景全景图

### 2. 前端功能
#### 场景管理
- 创建新场景 (CreateScene.js)
- 场景列表展示 (SceneList.js)
- 场景查看 (SceneViewer.js)

#### 全景查看器功能
- 全景图像展示
- 热点标记功能
- 街景导航模式
- 全屏显示
- 热点添加和交互
- 基础图像分析功能 (替代了GPT-4V)

#### Street View Mode 增强功能 ✨
- **两种拼接模式**: 
  - **完整拼接模式**: 将所有图片按顺序拼接成一张完整的全景图
  - **分组拼接模式**: 将图片分组拼接成多张全景图，便于导航浏览
- **统一高度处理**: 支持将所有图片调整到相同高度进行拼接
- **无高度限制**: 可选择保持原始图片尺寸，去掉高度限制
- **灵活配置**: 支持任意目标高度设置（600px - 2000px+）
- **智能缩放**: 自动调整图片尺寸以适应目标高度

### 3. 后端API
FastAPI提供RESTful API接口：
- 场景创建和管理
- 文件上传处理
- 图像处理任务调度
- 结果存储和检索
- 基础图像分析 (使用OpenCV)

## 技术栈

### 前端
- React.js - UI框架
- Webpack - 构建工具
- Material UI - UI组件库
- Pannellum - 全景图像查看器

### 后端
- FastAPI - Python Web框架
- OpenCV - 图像处理库
- NumPy - 数值计算库
- Pillow - 图像处理库

### 图像处理相关
- SIFT/SURF/ORB - 特征检测算法
- RANSAC - 图像配准算法
- CLAHE - 对比度增强算法
- 基础特征点检测 - 替代AI分析

## 安装和使用

### 环境要求
- Python 3.8+
- Node.js 14+

### 后端安装
```bash
pip install -r requirements.txt
```

### 前端安装
```bash
cd frontend
npm install
```

### 运行应用
```bash
# 启动后端
python main.py

# 启动前端 (在另一个终端)
cd frontend
npm start
```

## Street View Mode API 使用

### 基本用法
```bash
POST /api/scenes/{scene_id}/process
Content-Type: application/json

{
  "mode": "streetview",
  "params": {
    "step": 3,
    "target_height": 1000  // 可选：统一高度（像素）
  }
}
```

### 参数说明
- `step`: 每组拼接的图片数量（默认3，仅分组模式使用）
- `stitch_mode`: 拼接模式
  - `"single"`: 完整拼接模式 - 所有图片拼接成一张
  - `"sequence"`: 分组拼接模式 - 分组创建多张全景图（默认）
- `target_height`: 目标高度（可选）
  - 如果设置：所有图片将调整到此高度
  - 如果不设置：保持原始图片尺寸，无高度限制

### 使用示例

#### 1. 完整拼接模式（单张全景图）
```json
{
  "mode": "streetview",
  "params": {
    "stitch_mode": "single",
    "target_height": 1500
  }
}
```

#### 2. 分组拼接模式（多张全景图）
```json
{
  "mode": "streetview",
  "params": {
    "stitch_mode": "sequence",
    "step": 3,
    "target_height": 1500
  }
}
```

#### 3. 无高度限制模式
```json
{
  "mode": "streetview",
  "params": {
    "stitch_mode": "single"
  }
}
```

## 工作流程

1. 用户通过前端界面上传图像文件
2. 系统根据选择的模式（全景或街景）处理图像
3. 后端使用OpenCV进行图像拼接处理
4. 生成的全景图像存储在服务器上
5. 用户可以在前端查看和交互全景图像
6. 可以使用基础图像分析功能获取场景信息

## 重要变更说明

### GPT-4V 功能移除
- **移除了 OpenAI 依赖**：不再需要 OpenAI API 密钥
- **替换为基础分析**：使用 OpenCV 进行基本的图像信息提取
- **功能简化**：AI 聊天功能现在提供基础的技术信息而非智能对话
- **成本降低**：无需支付 API 调用费用

### 当前分析功能
- 图像尺寸和基本属性
- 简单的场景类型分类
- 特征点检测
- 全景图类型识别

这个项目现在提供了一个轻量级的图像拼接和全景展示解决方案，无需外部AI服务依赖。
