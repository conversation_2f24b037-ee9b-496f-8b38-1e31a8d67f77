import os
import cv2
import numpy as np
from typing import List, Dict, Any, Optional


class AIImageAnalyzer:
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the AI analyzer - now uses basic OpenCV analysis instead of GPT-4V
        
        Args:
            api_key: Not used anymore, kept for compatibility
        """
        pass
    
    def analyze_image_basic(self, image_path: str = None, image_array: np.ndarray = None) -> Dict[str, Any]:
        """
        Basic image analysis using OpenCV instead of GPT-4V
        
        Args:
            image_path: Path to the image file
            image_array: OpenCV image array
            
        Returns:
            Basic analysis result
        """
        if not image_path and image_array is None:
            raise ValueError("Either image_path or image_array must be provided")
        
        try:
            # Load image
            if image_path:
                if not os.path.exists(image_path):
                    raise FileNotFoundError(f"Image file not found: {image_path}")
                image = cv2.imread(image_path)
            else:
                image = image_array.copy()
            
            if image is None:
                raise ValueError("Failed to load image")
            
            # Basic image analysis
            height, width = image.shape[:2]
            channels = image.shape[2] if len(image.shape) > 2 else 1
            
            # Calculate basic statistics
            mean_color = np.mean(image, axis=(0, 1)) if channels > 1 else np.mean(image)
            
            # Determine basic scene type based on color analysis
            scene_type = "unknown"
            if channels >= 3:
                b, g, r = mean_color
                if g > r and g > b:
                    scene_type = "landscape"
                elif b > r and b > g:
                    scene_type = "sky/water"
                elif r > g and r > b:
                    scene_type = "indoor/warm"
                else:
                    scene_type = "mixed"
            
            # File size if available
            file_size = 0
            if image_path and os.path.exists(image_path):
                file_size = os.path.getsize(image_path)
            
            return {
                "model": "basic-opencv-analysis",
                "prompt": "Basic image analysis",
                "response": f"图像尺寸: {width}x{height}, 通道数: {channels}, 场景类型: {scene_type}",
                "analysis": {
                    "dimensions": f"{width}x{height}",
                    "channels": channels,
                    "file_size": file_size,
                    "scene_type": scene_type,
                    "mean_color": mean_color.tolist() if hasattr(mean_color, 'tolist') else float(mean_color)
                },
                "usage": None
            }
        except Exception as e:
            return {
                "error": str(e),
                "model": "basic-opencv-analysis",
                "prompt": "Basic image analysis",
                "response": f"图像分析失败: {str(e)}",
                "usage": None
            }
    
    def analyze_image_with_gpt4v(self, 
                                image_path: str = None,
                                image_array: np.ndarray = None,
                                prompt: str = "What's in this image?") -> Dict[str, Any]:
        """
        Replacement for GPT-4V analysis - now uses basic OpenCV analysis
        
        Args:
            image_path: Path to the image file
            image_array: OpenCV image array
            prompt: Prompt (ignored, kept for compatibility)
            
        Returns:
            Basic analysis result
        """
        return self.analyze_image_basic(image_path, image_array)
    
    def analyze_panorama_scene(self, 
                              image_path: str = None,
                              image_array: np.ndarray = None) -> Dict[str, Any]:
        """
        Analyze panorama scene using basic OpenCV analysis
        
        Args:
            image_path: Path to the panorama image
            image_array: OpenCV image array of the panorama
            
        Returns:
            Basic analysis result with scene description
        """
        result = self.analyze_image_basic(image_path, image_array)
        
        if "error" not in result:
            # Add panorama-specific analysis
            try:
                if image_path:
                    image = cv2.imread(image_path)
                else:
                    image = image_array.copy()
                
                height, width = image.shape[:2]
                aspect_ratio = width / height
                
                # Basic panorama classification
                if aspect_ratio > 2.0:
                    panorama_type = "wide panorama"
                elif aspect_ratio > 1.5:
                    panorama_type = "standard panorama"
                else:
                    panorama_type = "square/portrait"
                
                result["response"] = f"全景图像分析: {panorama_type}, 尺寸: {width}x{height}, 宽高比: {aspect_ratio:.2f}"
                result["analysis"]["panorama_type"] = panorama_type
                result["analysis"]["aspect_ratio"] = aspect_ratio
                
            except Exception as e:
                result["response"] += f" (全景分析失败: {str(e)})"
        
        return result
    
    def detect_landmarks(self, 
                        image_path: str = None,
                        image_array: np.ndarray = None) -> Dict[str, Any]:
        """
        Basic landmark detection using OpenCV (replaces GPT-4V landmark detection)
        
        Args:
            image_path: Path to the image file
            image_array: OpenCV image array
            
        Returns:
            Basic landmark detection result
        """
        try:
            if image_path:
                image = cv2.imread(image_path)
            else:
                image = image_array.copy()
            
            if image is None:
                raise ValueError("Failed to load image")
            
            height, width = image.shape[:2]
            
            # Simple corner detection as "landmarks"
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            corners = cv2.goodFeaturesToTrack(gray, maxCorners=10, qualityLevel=0.01, minDistance=100)
            
            landmarks = []
            if corners is not None:
                for i, corner in enumerate(corners):
                    x, y = corner.ravel()
                    landmarks.append({
                        "name": f"特征点 {i+1}",
                        "description": f"图像特征点位于 ({int(x)}, {int(y)})",
                        "position": {"x": int(x), "y": int(y)},
                        "confidence": 0.8  # Mock confidence
                    })
            
            return {
                "model": "basic-opencv-detection",
                "response": f"检测到 {len(landmarks)} 个特征点",
                "landmarks": landmarks,
                "total_landmarks": len(landmarks),
                "usage": None
            }
            
        except Exception as e:
            return {
                "error": str(e),
                "model": "basic-opencv-detection",
                "response": f"特征点检测失败: {str(e)}",
                "landmarks": [],
                "total_landmarks": 0,
                "usage": None
            }
    
    def compare_images_with_gpt4v(self, 
                                 image_paths: List[str],
                                 prompt: str = "Compare these images") -> Dict[str, Any]:
        """
        Basic image comparison using OpenCV (replaces GPT-4V comparison)
        
        Args:
            image_paths: List of paths to image files
            prompt: Prompt (ignored, kept for compatibility)
            
        Returns:
            Basic comparison result
        """
        try:
            if len(image_paths) < 2:
                return {
                    "error": "At least 2 images required for comparison",
                    "model": "basic-opencv-comparison",
                    "response": "需要至少2张图像进行比较",
                    "usage": None
                }
            
            comparisons = []
            for i, path in enumerate(image_paths):
                if os.path.exists(path):
                    image = cv2.imread(path)
                    if image is not None:
                        height, width = image.shape[:2]
                        file_size = os.path.getsize(path)
                        comparisons.append({
                            "image": f"图像 {i+1}",
                            "dimensions": f"{width}x{height}",
                            "file_size": file_size
                        })
            
            response = f"比较了 {len(comparisons)} 张图像的基本属性"
            
            return {
                "model": "basic-opencv-comparison",
                "response": response,
                "comparisons": comparisons,
                "usage": None
            }
            
        except Exception as e:
            return {
                "error": str(e),
                "model": "basic-opencv-comparison",
                "response": f"图像比较失败: {str(e)}",
                "usage": None
            }
