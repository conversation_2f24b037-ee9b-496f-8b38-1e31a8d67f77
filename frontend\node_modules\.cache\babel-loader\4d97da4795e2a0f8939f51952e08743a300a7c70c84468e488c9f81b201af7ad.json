{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\n\n/**\n * A version of `React.useLayoutEffect` that does not show a warning when server-side rendering.\n * This is useful for effects that are only needed for client-side rendering but not for SSR.\n *\n * Before you use this hook, make sure to read https://gist.github.com/gaearon/e7d97cdf38a2907924ea12e4ebdf3c85\n * and confirm it doesn't apply to your use-case.\n */\nconst useEnhancedEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\nexport default useEnhancedEffect;", "map": {"version": 3, "names": ["React", "useEnhancedEffect", "window", "useLayoutEffect", "useEffect"], "sources": ["D:/Study_Code/2025-8-5/qrjy_images/frontend/node_modules/@mui/material/node_modules/@mui/utils/esm/useEnhancedEffect/useEnhancedEffect.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\n\n/**\n * A version of `React.useLayoutEffect` that does not show a warning when server-side rendering.\n * This is useful for effects that are only needed for client-side rendering but not for SSR.\n *\n * Before you use this hook, make sure to read https://gist.github.com/gaearon/e7d97cdf38a2907924ea12e4ebdf3c85\n * and confirm it doesn't apply to your use-case.\n */\nconst useEnhancedEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\nexport default useEnhancedEffect;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGF,KAAK,CAACG,eAAe,GAAGH,KAAK,CAACI,SAAS;AACjG,eAAeH,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}