{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"component\", \"enableColorScheme\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport { html, body } from '../CssBaseline/CssBaseline';\nimport { getScopedCssBaselineUtilityClass } from './scopedCssBaselineClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getScopedCssBaselineUtilityClass, classes);\n};\nconst ScopedCssBaselineRoot = styled('div', {\n  name: 'MuiScopedCssBaseline',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme,\n  ownerState\n}) => {\n  const colorSchemeStyles = {};\n  if (ownerState.enableColorScheme && theme.colorSchemes) {\n    Object.entries(theme.colorSchemes).forEach(([key, scheme]) => {\n      var _scheme$palette;\n      colorSchemeStyles[`&${theme.getColorSchemeSelector(key).replace(/\\s*&/, '')}`] = {\n        colorScheme: (_scheme$palette = scheme.palette) == null ? void 0 : _scheme$palette.mode\n      };\n    });\n  }\n  return _extends({}, html(theme, ownerState.enableColorScheme), body(theme), {\n    '& *, & *::before, & *::after': {\n      boxSizing: 'inherit'\n    },\n    '& strong, & b': {\n      fontWeight: theme.typography.fontWeightBold\n    }\n  }, colorSchemeStyles);\n});\nconst ScopedCssBaseline = /*#__PURE__*/React.forwardRef(function ScopedCssBaseline(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiScopedCssBaseline'\n  });\n  const {\n      className,\n      component = 'div'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ScopedCssBaselineRoot, _extends({\n    as: component,\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? ScopedCssBaseline.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Enable `color-scheme` CSS property to use `theme.palette.mode`.\n   * For more details, check out https://developer.mozilla.org/en-US/docs/Web/CSS/color-scheme\n   * For browser support, check out https://caniuse.com/?search=color-scheme\n   */\n  enableColorScheme: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ScopedCssBaseline;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "useDefaultProps", "styled", "html", "body", "getScopedCssBaselineUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "ScopedCssBaselineRoot", "name", "slot", "overridesResolver", "props", "styles", "theme", "colorSchemeStyles", "enableColorScheme", "colorSchemes", "Object", "entries", "for<PERSON>ach", "key", "scheme", "_scheme$palette", "getColorSchemeSelector", "replace", "colorScheme", "palette", "mode", "boxSizing", "fontWeight", "typography", "fontWeightBold", "ScopedCssBaseline", "forwardRef", "inProps", "ref", "className", "component", "other", "as", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "elementType", "bool", "sx", "oneOfType", "arrayOf", "func"], "sources": ["D:/Study_Code/2025-8-5/qrjy_images/frontend/node_modules/@mui/material/ScopedCssBaseline/ScopedCssBaseline.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"component\", \"enableColorScheme\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport { html, body } from '../CssBaseline/CssBaseline';\nimport { getScopedCssBaselineUtilityClass } from './scopedCssBaselineClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getScopedCssBaselineUtilityClass, classes);\n};\nconst ScopedCssBaselineRoot = styled('div', {\n  name: 'MuiScopedCssBaseline',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme,\n  ownerState\n}) => {\n  const colorSchemeStyles = {};\n  if (ownerState.enableColorScheme && theme.colorSchemes) {\n    Object.entries(theme.colorSchemes).forEach(([key, scheme]) => {\n      var _scheme$palette;\n      colorSchemeStyles[`&${theme.getColorSchemeSelector(key).replace(/\\s*&/, '')}`] = {\n        colorScheme: (_scheme$palette = scheme.palette) == null ? void 0 : _scheme$palette.mode\n      };\n    });\n  }\n  return _extends({}, html(theme, ownerState.enableColorScheme), body(theme), {\n    '& *, & *::before, & *::after': {\n      boxSizing: 'inherit'\n    },\n    '& strong, & b': {\n      fontWeight: theme.typography.fontWeightBold\n    }\n  }, colorSchemeStyles);\n});\nconst ScopedCssBaseline = /*#__PURE__*/React.forwardRef(function ScopedCssBaseline(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiScopedCssBaseline'\n  });\n  const {\n      className,\n      component = 'div'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ScopedCssBaselineRoot, _extends({\n    as: component,\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? ScopedCssBaseline.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Enable `color-scheme` CSS property to use `theme.palette.mode`.\n   * For more details, check out https://developer.mozilla.org/en-US/docs/Web/CSS/color-scheme\n   * For browser support, check out https://caniuse.com/?search=color-scheme\n   */\n  enableColorScheme: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ScopedCssBaseline;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,mBAAmB,CAAC;AACjE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,IAAI,EAAEC,IAAI,QAAQ,4BAA4B;AACvD,SAASC,gCAAgC,QAAQ,4BAA4B;AAC7E,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOZ,cAAc,CAACW,KAAK,EAAEN,gCAAgC,EAAEK,OAAO,CAAC;AACzE,CAAC;AACD,MAAMG,qBAAqB,GAAGX,MAAM,CAAC,KAAK,EAAE;EAC1CY,IAAI,EAAE,sBAAsB;EAC5BC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAAC,CAAC;EACFO,KAAK;EACLV;AACF,CAAC,KAAK;EACJ,MAAMW,iBAAiB,GAAG,CAAC,CAAC;EAC5B,IAAIX,UAAU,CAACY,iBAAiB,IAAIF,KAAK,CAACG,YAAY,EAAE;IACtDC,MAAM,CAACC,OAAO,CAACL,KAAK,CAACG,YAAY,CAAC,CAACG,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,MAAM,CAAC,KAAK;MAC5D,IAAIC,eAAe;MACnBR,iBAAiB,CAAC,IAAID,KAAK,CAACU,sBAAsB,CAACH,GAAG,CAAC,CAACI,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG;QAC/EC,WAAW,EAAE,CAACH,eAAe,GAAGD,MAAM,CAACK,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,eAAe,CAACK;MACrF,CAAC;IACH,CAAC,CAAC;EACJ;EACA,OAAOtC,QAAQ,CAAC,CAAC,CAAC,EAAEQ,IAAI,CAACgB,KAAK,EAAEV,UAAU,CAACY,iBAAiB,CAAC,EAAEjB,IAAI,CAACe,KAAK,CAAC,EAAE;IAC1E,8BAA8B,EAAE;MAC9Be,SAAS,EAAE;IACb,CAAC;IACD,eAAe,EAAE;MACfC,UAAU,EAAEhB,KAAK,CAACiB,UAAU,CAACC;IAC/B;EACF,CAAC,EAAEjB,iBAAiB,CAAC;AACvB,CAAC,CAAC;AACF,MAAMkB,iBAAiB,GAAG,aAAazC,KAAK,CAAC0C,UAAU,CAAC,SAASD,iBAAiBA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC/F,MAAMxB,KAAK,GAAGhB,eAAe,CAAC;IAC5BgB,KAAK,EAAEuB,OAAO;IACd1B,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACF4B,SAAS;MACTC,SAAS,GAAG;IACd,CAAC,GAAG1B,KAAK;IACT2B,KAAK,GAAGlD,6BAA6B,CAACuB,KAAK,EAAErB,SAAS,CAAC;EACzD,MAAMa,UAAU,GAAGd,QAAQ,CAAC,CAAC,CAAC,EAAEsB,KAAK,EAAE;IACrC0B;EACF,CAAC,CAAC;EACF,MAAMjC,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACM,qBAAqB,EAAElB,QAAQ,CAAC;IACvDkD,EAAE,EAAEF,SAAS;IACbD,SAAS,EAAE3C,IAAI,CAACW,OAAO,CAACE,IAAI,EAAE8B,SAAS,CAAC;IACxCD,GAAG,EAAEA,GAAG;IACRhC,UAAU,EAAEA;EACd,CAAC,EAAEmC,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACFE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGV,iBAAiB,CAACW,SAAS,CAAC,yBAAyB;EAC3F;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEpD,SAAS,CAACqD,IAAI;EACxB;AACF;AACA;EACEzC,OAAO,EAAEZ,SAAS,CAACsD,MAAM;EACzB;AACF;AACA;EACEV,SAAS,EAAE5C,SAAS,CAACuD,MAAM;EAC3B;AACF;AACA;AACA;EACEV,SAAS,EAAE7C,SAAS,CAACwD,WAAW;EAChC;AACF;AACA;AACA;AACA;EACEjC,iBAAiB,EAAEvB,SAAS,CAACyD,IAAI;EACjC;AACF;AACA;EACEC,EAAE,EAAE1D,SAAS,CAAC2D,SAAS,CAAC,CAAC3D,SAAS,CAAC4D,OAAO,CAAC5D,SAAS,CAAC2D,SAAS,CAAC,CAAC3D,SAAS,CAAC6D,IAAI,EAAE7D,SAAS,CAACsD,MAAM,EAAEtD,SAAS,CAACyD,IAAI,CAAC,CAAC,CAAC,EAAEzD,SAAS,CAAC6D,IAAI,EAAE7D,SAAS,CAACsD,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAed,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}